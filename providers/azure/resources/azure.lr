// Copyright (c) Mondoo, Inc.
// SPDX-License-Identifier: BUSL-1.1

option provider = "go.mondoo.com/cnquery/v9/providers/azure"
option go_package = "go.mondoo.com/cnquery/v11/providers/azure/resources"

// Azure resource
azure {
}

// Azure subscription
azure.subscription @defaults ("name") {
  // Full resource identifier of the subscription
  id string
  // Name of the subscription
  name string
  // Subscription identifier
  subscriptionId string
  // Subscription tenant identifier
  tenantId string
  // List of tenants that manage the subscription
  managedByTenants []string
  // Subscription tags
  tags map[string]string
  // Subscription state
  state string
  // Subscription authorization source
  authorizationSource string
  // Subscription policies
  subscriptionsPolicies dict
  // All resources in a subscription
  resources() []azure.subscription.resource
  // Resource groups in the subscription
  resourceGroups() []azure.subscription.resourcegroup
  // Compute resources in the subscription
  compute() azure.subscription.computeService
  // Network resources in the subscription
  network() azure.subscription.networkService
  // Storage resources in the subscription
  storage() azure.subscription.storageService
  // Web resources in the subscription
  web() azure.subscription.webService
  // SQL resources in the subscription
  sql() azure.subscription.sqlService
  // MySQL resources inside the subscription
  mySql() azure.subscription.mySqlService
  // PostgreSQL resources in the subscription
  postgreSql() azure.subscription.postgreSqlService
  // MariaDB resources in the subscription
  mariaDb() azure.subscription.mariaDbService
  // Cosmos DB resources in the subscription
  cosmosDb() azure.subscription.cosmosDbService
  // Azure Key Vault resources in the subscription
  keyVault() azure.subscription.keyVaultService
  // Authorization resources in the subscription
  iam() azure.subscription.authorizationService
  // Deprecated: use `iam` instead
  authorization() azure.subscription.authorizationService
  // Monitor resources in the subscription
  monitor() azure.subscription.monitorService
  // Cloud defender resources in the subscription
  cloudDefender() azure.subscription.cloudDefenderService
  // AKS resources in the subscription
  aks() azure.subscription.aksService
  // Advisor resources in the subscription
  advisor() azure.subscription.advisorService
  // Policy service in the subscription
  policy azure.subscription.policy
  // IoT resources in the subscription
  iot() azure.subscription.iotService
}

// Azure function
private azure.subscription.webService.function @defaults("name type") {
  // ID of the function
  id string
  // Name of the function
  name string
  // Type of function
  type string
  // Kind of function
  kind string
  // Properties for the function
  properties dict
}

// Azure resource group
private azure.subscription.resourcegroup @defaults("name location") {
  // Resource group ID
  id string
  // Resource group name
  name string
  // Resource group location
  location string
  // Resource group tags
  tags map[string]string
  // Resource group type
  type string
  // ID of the resource that manages this resource group
  managedBy string
  // Resource group provisioning state
  provisioningState string
}

// Azure resource
private azure.subscription.resource @defaults("id name location") {
  // Resource ID
  id string
  // Resource name
  name string
  // Resource kind
  kind string
  // Resource location
  location string
  // Resource tags
  tags map[string]string
  // Resource type
  type string
  // ID of the resource that manages this resource
  managedBy string
  // Resource SKU
  sku dict
  // Resource plan
  plan dict
  // Resource identity
  identity dict
  // Resource provisioning state
  provisioningState string
  // When the resource was created
  createdTime time
  // When the resource was last changed
  changedTime time
}

// Azure compute
private azure.subscription.computeService {
  // Subscription identifier
  subscriptionId string
  // All compute instances under a subscription
  vms() []azure.subscription.computeService.vm
  // All the disks under a subscription
  disks() []azure.subscription.computeService.disk
}

// Azure compute virtual machine
azure.subscription.computeService.vm @defaults("name location properties.hardwareProfile.vmSize properties.storageProfile.osDisk.osType") {
  // VM ID
  id string
  // VM name
  name string
  // VM location
  location string
  // VM zones
  zones []string
  // VM state
  state() string
  // Whether the VM is running
  isRunning() bool
  // VM tags
  tags map[string]string
  // VM type
  type string
  // VM properties
  properties dict
  // VM extension
  extensions() []dict
  // VM compute disk
  osDisk() azure.subscription.computeService.disk
  // VM compute data disk
  dataDisks() []azure.subscription.computeService.disk
  // VM public IP addresses
  publicIpAddresses() []azure.subscription.networkService.ipAddress
}

// Azure disk resource
azure.subscription.computeService.disk @defaults("name location properties.osType properties.diskSizeGB properties.diskState") {
  // Disk resource ID
  id string
  // Disk resource name
  name string
  // Disk resource location
  location string
  // Disk resource tags
  tags map[string]string
  // Disk resource type
  type string
  // A relative URI containing the ID of the VM that has the disk attached
  managedBy string
  // List of relative URIs containing the IDs of the VMs that have the disk attached
  managedByExtended []string
  // The logical zone list for disk
  zones []string
  // The disk SKU name and tier
  sku dict
  // Disk resource properties
  properties dict
}

// Azure network
private azure.subscription.networkService {
  // Subscription identifier
  subscriptionId string
  // List of network interfaces
  interfaces() []azure.subscription.networkService.interface
  // List of network security groups
  securityGroups() []azure.subscription.networkService.securityGroup
  // List of network watchers
  watchers() []azure.subscription.networkService.watcher
  // List of public IP addresses
  publicIpAddresses() []azure.subscription.networkService.ipAddress
  // List of Bastion hosts
  bastionHosts() []azure.subscription.networkService.bastionHost
  // List of load balancers
  loadBalancers() []azure.subscription.networkService.loadBalancer
  // List of NAT gateways
  natGateways() []azure.subscription.networkService.natGateway
  // List of virtual networks
  virtualNetworks() []azure.subscription.networkService.virtualNetwork
  // List of virtual network gateways
  virtualNetworkGateways() []azure.subscription.networkService.virtualNetworkGateway
  // List of network firewalls
  firewalls() []azure.subscription.networkService.firewall
  // List of firewall policies
  firewallPolicies() []azure.subscription.networkService.firewallPolicy
  // List of application security groups
  applicationSecurityGroups() []azure.subscription.networkService.appSecurityGroup
  // List of application gateways
  applicationGateways() []azure.subscription.networkService.applicationGateway
  // List of application firewall policies
  applicationFirewallPolicies() []azure.subscription.networkService.applicationFirewallPolicy
}

// Azure Virtual Network (VNet) gateway
azure.subscription.networkService.virtualNetworkGateway @defaults("id name location") {
  // VNet gateway ID
  id string
  // VNet gateway name
  name string
  // VNet gateway location
  location string
  // VNet gateway tags
  tags map[string]string
  // VNet gateway resource type
  type string
  // VNet gateway etag
  etag string
  // VNet gateway properties
  properties dict
  // Whether the virtual network gateway is active
  active bool
  // Whether BGP is enabled for this virtual network gateway
  enableBgp bool
  // Whether BGP route translation is enabled for this VNet gateway
  enableBgpRouteTranslationForNat bool
  // Whether DNS forwarding is enabled for this VNet gateway
  enableDNSForwarding bool
  // Whether private IP must be enabled for connections
  enablePrivateIPAddress bool
  // Whether IP sec replay protection is disabled for this VNet gateway
  disableIPSecReplayProtection bool
  // VNet gateway provisioning state
  provisioningState string
  // The IP address allocated by the gateway to which DNS requests can be sent
  inboundDNSForwardingEndpoint string
  // VNet gateway SKU name
  skuName string
  // VNet gateway SKU capacity
  skuCapacity int
  // A list of address blocks reserved for this virtual network in CIDR notation
  addressPrefixes []string
  // VNet gateway type
  gatewayType string
  // VNet gateway generation
  vpnGatewayGeneration string
  // VNet gateway VPN type
  vpnType string
  // VNet gateway IP configurations
  ipConfigurations []azure.subscription.networkService.virtualNetworkGateway.ipConfig
  // VNet gateway BGP settings
  bgpSettings azure.subscription.networkService.bgpSettings
  // VNet gateway NAT rules
  natRules []azure.subscription.networkService.virtualNetworkGateway.natRule
  // Applicable connections for the gateway
  connections() []azure.subscription.networkService.virtualNetworkGateway.connection
  // VPN client configuration (only set if P2S is configured for the gateway)
  vpnClientConfiguration dict
}

// Azure network application security group
azure.subscription.networkService.appSecurityGroup @defaults("id name location") {
  // Application security group ID
  id string
  // Application security group name
  name string
  // Application security group location
  location string
  // Application security group tags
  tags map[string]string
  // Application security group type
  type string
  // Application security group etag
  etag string
  // Application security group properties
  properties dict
}

// Azure network firewall
azure.subscription.networkService.firewall @defaults("id name location") {
  // Firewall ID
  id string
  // Firewall name
  name string
  // Firewall location
  location string
  // Firewall tags
  tags map[string]string
  // Firewall type
  type string
  // Firewall etag
  etag string
  // Firewall properties
  properties dict
  // Firewall provisioning state
  provisioningState string
  // Firewall SKU name
  skuName string
  // Firewall SKU tier
  skuTier string
  // Firewall threat intel mode
  threatIntelMode string
  // Policy associated with this firewall
  policy() azure.subscription.networkService.firewallPolicy
  // List of IP configurations for the firewall
  ipConfigurations []azure.subscription.networkService.firewall.ipConfig
  // The IP configuration used for management traffic
  managementIpConfiguration azure.subscription.networkService.firewall.ipConfig
  // List of network rules for the firewall
  networkRules []azure.subscription.networkService.firewall.networkRule
  // List of NAT rules for the firewall
  natRules []azure.subscription.networkService.firewall.natRule
  // List of application rules for the firewall
  applicationRules []azure.subscription.networkService.firewall.applicationRule
}

// Azure network firewall IP configuration
private azure.subscription.networkService.firewall.ipConfig @defaults("id name") {
  // Firewall IP configuration ID
  id string
  // Firewall IP configuration name
  name string
  // Firewall IP configuration etag
  etag string
  // Firewall IP configuration private IP address
  privateIpAddress string
  // Firewall IP configuration properties
  properties dict
  // Public IP address associated with this IP configuration
  publicIpAddress() azure.subscription.networkService.ipAddress
  // Subnet associated with this IP configuration
  subnet() azure.subscription.networkService.subnet
}

// Azure network firewall network rule
private azure.subscription.networkService.firewall.networkRule @defaults("id name") {
  // Firewall network rule ID
  id string
  // Firewall network rule name
  name string
  // Firewall network rule etag
  etag string
  // Firewall network rule properties
  properties dict
}

// Azure network firewall application rule
private azure.subscription.networkService.firewall.applicationRule @defaults("id name") {
  // Firewall application rule ID
  id string
  // Firewall application rule name
  name string
  // Firewall application rule etag
  etag string
  // Firewall application rule properties
  properties dict
}

// Azure network firewall NAT rule
private azure.subscription.networkService.firewall.natRule @defaults("id name") {
  // Firewall NAT rule ID
  id string
  // Firewall NAT rule name
  name string
  // Firewall NAT rule etag
  etag string
  // Firewall NAT rule properties
  properties dict
}

// Azure network firewall policy
azure.subscription.networkService.firewallPolicy @defaults("id name location") {
  // Firewall policy ID
  id string
  // Firewall policy name
  name string
  // Firewall policy location
  location string
  // Firewall policy tags
  tags map[string]string
  // Firewall policy type
  type string
  // Firewall policy etag
  etag string
  // Firewall policy properties
  properties dict
  // Firewall policy provisioning state
  provisioningState string
  // The parent firewall policy from which rules are inherited
  basePolicy() azure.subscription.networkService.firewallPolicy
  // List of child policies this policy is associated with
  childPolicies() []azure.subscription.networkService.firewallPolicy
  // List of firewalls the policy is associated with
  firewalls() []azure.subscription.networkService.firewall
}

// Azure Virtual Network (VNet) gateway IP configuration
private azure.subscription.networkService.virtualNetworkGateway.ipConfig @defaults("id name") {
  // VNet gateway IP Configuration ID
  id string
  // VNet gateway IP Configuration name
  name string
  // VNet gateway IP Configuration etag
  etag string
  // VNet gateway IP Configuration private IP address
  privateIpAddress string
  // VNet gateway IP Configuration properties
  properties dict
  // The public IP address, associated with this IP configuration
  publicIpAddress() azure.subscription.networkService.ipAddress
}

// Azure Virtual Network (VNet) gateway connection
private azure.subscription.networkService.virtualNetworkGateway.connection @defaults("id name") {
  // VNet gateway connection ID
  id string
  // VNet gateway Connection name
  name string
  // VNet gateway Connection type
  type string
  // VNet gateway Connection etag
  etag string
  // VNet gateway Connection properties
  properties dict
}

// Azure network BGP settings
private azure.subscription.networkService.bgpSettings @defaults("asn bgpPeeringAddress") {
  // BGP Settings ID
  id string
  // BGP Settings speaker ASN
  asn int
  // The BGP peering address and BGP identifier of this BGP speaker
  bgpPeeringAddress string
  // The weight added to routes learned from this BGP speaker
  peerWeight int
  // The BGP peering addresses with IP configuration
  bgpPeeringAddressesConfig []azure.subscription.networkService.bgpSettings.ipConfigurationBgpPeeringAddress
}

// Azure BGP settings IP configuration
private azure.subscription.networkService.bgpSettings.ipConfigurationBgpPeeringAddress @defaults("defaultBgpIpAddresses") {
  // BGP Settings IP Configuration ID
  id string
  // BGP Settings IP Configuration custom BGP IP addresses
  customBgpIpAddresses []string
  // BGP Settings IP Configuration ID
  ipConfigurationId string
  // BGP Settings IP Configuration default BGP IP addresses
  defaultBgpIpAddresses []string
  // BGP Settings IP Configuration tunnel public BGP IP addresses
  tunnelIpAddresses []string
}

// Azure NAT gateway
azure.subscription.networkService.natGateway @defaults("id name location") {
  // NAT Gateway ID
  id string
  // NAT Gateway name
  name string
  // NAT Gateway location
  location string
  // NAT Gateway tags
  tags map[string]string
  // NAT Gateway type
  type string
  // NAT Gateway etag
  etag string
  // NAT Gateway properties
  properties dict
  // NAT Gateway availability zones
  zones []string
  // List of public IP addresses the NAT Gateway is associated with
  publicIpAddresses() []azure.subscription.networkService.ipAddress
  // List of subnets the NAT Gateway is associated with
  subnets() []azure.subscription.networkService.subnet
}

// Azure Virtual Network (VNet) subnet
azure.subscription.networkService.subnet @defaults("id name addressPrefix") {
  // Subnet ID
  id string
  // Subnet name
  name string
  // Subnet type
  type string
  // Subnet etag
  etag string
  // Subnet address prefix
  addressPrefix string
  // Subnet properties
  properties dict
  // The NAT gateway this subnet is associated with, if any
  natGateway() azure.subscription.networkService.natGateway
  // List of IP configurations for the subnet
  ipConfigurations() []azure.subscription.networkService.virtualNetworkGateway.ipConfig
}

// Azure Virtual network (VNet)
azure.subscription.networkService.virtualNetwork @defaults("id name location") {
  // Virtual Network ID
  id string
  // Virtual Network name
  name string
  // Virtual Network location
  location string
  // Virtual Network tags
  tags map[string]string
  // Virtual Network type
  type string
  // Virtual Network etag
  etag string
  // Virtual Network properties
  properties dict
  // List of subnets within the virtual network
  subnets []azure.subscription.networkService.subnet
  // Virtual Network DHCP options
  dhcpOptions azure.subscription.networkService.virtualNetwork.dhcpOptions
  // Whether DDoS protection is enabled for all the protected resources in the virtual network
  enableDdosProtection bool
  // Whether VM protection is enabled for all the subnets in the virtual network
  enableVmProtection bool
}

// Azure Virtual Network (VNet) DHCP options
private azure.subscription.networkService.virtualNetwork.dhcpOptions {
  // DHCP options ID
  id string
  // The DNS servers, used by the virtual network
  dnsServers []string
}

// Azure Load Balancer
azure.subscription.networkService.loadBalancer @defaults("id name location") {
  // Load Balancer ID
  id string
  // Load Balancer name
  name string
  // Load Balancer location
  location string
  // Load Balancer tags
  tags map[string]string
  // Load Balancer type
  type string
  // Load Balancer properties
  properties dict
  // Load Balancer etag
  etag string
  // Load Balancer SKU
  sku string
  // List of Load Balancer probes
  probes []azure.subscription.networkService.probe
  // List of Load Balancer backend address pools
  backendPools []azure.subscription.networkService.backendAddressPool
  // List of Load Balancer frontend IP configurations
  frontendIpConfigs []azure.subscription.networkService.frontendIpConfig
  // List of Load Balancer inbound NAT pools
  inboundNatPools []azure.subscription.networkService.inboundNatPool
  // List of Load Balancer inbound NAT rules
  inboundNatRules []azure.subscription.networkService.inboundNatRule
  // List of Load Balancer outbound rules
  outboundRules []azure.subscription.networkService.outboundRule
  // List of Load Balancer rules
  loadBalancerRules []azure.subscription.networkService.loadBalancerRule
}

// Azure network probe
private azure.subscription.networkService.probe @defaults("id name"){
  // Probe ID
  id string
  // Probe name
  name string
  // Probe type
  type string
  // Probe etag
  etag string
  // Probe properties
  properties dict
}

// Azure network backend address pool
private azure.subscription.networkService.backendAddressPool @defaults("id name") {
  // Backend Address Pool ID
  id string
  // Backend Address Pool name
  name string
  // Backend Address Pool type
  type string
  // Backend Address Pool etag
  etag string
  // Backend Address Pool properties
  properties dict
}

// Azure network inbound NAT pool
private azure.subscription.networkService.inboundNatPool @defaults("id name") {
  // Inbound NAT Pool ID
  id string
  // Inbound NAT Pool name
  name string
  // Inbound NAT Pool type
  type string
  // Inbound NAT Pool etag
  etag string
  // Inbound NAT Pool properties
  properties dict
}

// Azure network inbound NAT rule
private azure.subscription.networkService.inboundNatRule @defaults("id name") {
  // Inbound NAT Rule ID
  id string
  // Inbound NAT Rule name
  name string
  // Inbound NAT Rule type
  type string
  // Inbound NAT Rule etag
  etag string
  // Inbound NAT Rule properties
  properties dict
}

// Azure network frontend IP configuration
private azure.subscription.networkService.frontendIpConfig @defaults("id name") {
  // Frontend IP Configuration ID
  id string
  // Frontend IP Configuration name
  name string
  // Frontend IP Configuration type
  type string
  // Frontend IP Configuration etag
  etag string
  // Frontend IP Configuration properties
  properties dict
  // Frontend IP Configuration zones
  zones []string
}

// Azure Load Balancer rule
private azure.subscription.networkService.loadBalancerRule @defaults("id name") {
  // Load Balancer rule ID
  id string
  // Load Balancer rule name
  name string
  // Load Balancer rule type
  type string
  // Load Balancer rule etag
  etag string
  // Load Balancer rule properties
  properties dict
}

// Azure network outbound rule
private azure.subscription.networkService.outboundRule @defaults("id name") {
  // Outbound rule ID
  id string
  // Outbound rule name
  name string
  // Outbound rule type
  type string
  // Outbound rule etag
  etag string
  // Outbound rule properties
  properties dict
}

// Azure network interface
azure.subscription.networkService.interface @defaults("name location properties.macAddress properties.nicType") {
  // Network interface ID
  id string
  // Network interface name
  name string
  // Network interface name
  location string
  // Network interface tags
  tags map[string]string
  // Network interface type
  type string
  // Network interface etag
  etag string
  // Network interface properties
  properties dict
  // Network interface compute vm
  vm() azure.subscription.computeService.vm
}

// Azure network IP address
private azure.subscription.networkService.ipAddress @defaults("name location ipAddress") {
  // IP address ID
  id string
  // IP address name
  name string
  // IP address location
  location string
  // IP address tags
  tags map[string]string
  // IP address
  ipAddress string
  // IP address type
  type string
}

// Azure Network Bastion host
private azure.subscription.networkService.bastionHost @defaults("id name location") {
  // Bastion Host ID
  id string
  // Bastion Host name
  name string
  // Bastion Host location
  location string
  // Bastion Host tags
  tags map[string]string
  // Bastion Host type
  type string
  // Bastion host properties
  properties dict
  // Bastion host SKU
  sku dict
}

// Azure network security group
private azure.subscription.networkService.securityGroup @defaults("id name location") {
  // Security group ID
  id string
  // Security group name
  name string
  // Security group location
  location string
  // Security group tags
  tags map[string]string
  // Security group type
  type string
  // Security group etag
  etag string
  // Security group properties
  properties dict
  // Security group interfaces
  interfaces []azure.subscription.networkService.interface
  // Security group rules
  securityRules []azure.subscription.networkService.securityrule
  // Security group default security rules
  defaultSecurityRules []azure.subscription.networkService.securityrule
}

// Azure network security rule
private azure.subscription.networkService.securityrule @defaults("id name") {
  // Security rule ID
  id string
  // Security rule name
  name string
  // Security rule etag
  etag string
  // Security rule properties
  properties dict
  // Security rule destination port range
  destinationPortRange []dict
  // Security rule direction (outbound or inbound)
  direction string
}

// Azure Network Watcher
private azure.subscription.networkService.watcher @defaults("name location") {
  // Network watcher ID
  id string
  // Network watcher name
  name string
  // Network watcher location
  location string
  // Network watcher tags
  tags map[string]string
  // Network watcher type
  type string
  // Network watcher etag
  etag string
  // Network watcher properties
  properties dict
  // Network watcher flow logs
  flowLogs() []azure.subscription.networkService.watcher.flowlog
  // Network watcher provisioning state
  provisioningState string
}

// Azure Network Watcher flow log
private azure.subscription.networkService.watcher.flowlog @defaults("name location") {
  // Network watcher flow log ID
  id string
  // Network watcher flow log name
  name string
  // Network watcher flow log location
  location string
  // Network watcher flow log tags
  tags map[string]string
  // Network watcher flow log type
  type string
  // Network watcher flow log etag
  etag string
  // Network watcher flow log provisioning state
  provisioningState string
  // Whether the network watcher flow log is enabled
  enabled bool
  // Network watcher flow log storage account identifier
  storageAccountId string
  // Network watcher flow log target resource identifier
  targetResourceId string
  // Network watcher flow log target resource guid
  targetResourceGuid string
  // Network watcher flow log version
  version int
  // Network watcher flow log format
  format string
  // Network watcher flow log retention policy
  retentionPolicy dict
  // Network watcher flow log analytics
  analytics dict
}

// Azure Application Gateway
azure.subscription.networkService.applicationGateway @defaults("id name location") {
  // Application Gateway ID
  id string
  // Application Gateway name
  name string
  // Application Gateway location
  location string
  // Application Gateway tags
  tags map[string]string
  // Application Gateway resource type
  type string
  // Application Gateway etag
  etag string
  // Application Gateway properties
  properties dict
  // Gets the attached application firewall policy
  policy() azure.subscription.networkService.applicationFirewallPolicy
  // WAF configurations
  wafConfiguration() []azure.subscription.networkService.wafConfig
}

// Azure Application Firewall Config
azure.subscription.networkService.wafConfig @defaults("id name type") {
  // ID of the WAF configuration
  id string
  // Name of the WAF configuration
  name string
  // Type of WAF configuration
  type string
  // Kind of WAF configuration
  kind string
  // Properties for the WAF configuration
  properties dict
}

// Azure Application Firewall Policy (WAF)
azure.subscription.networkService.applicationFirewallPolicy @defaults("id name location") {
  // Application firewall policy ID
  id string
  // Application firewall policy name
  name string
  // Application firewall policy location
  location string
  // Application firewall policy tags
  tags map[string]string
  // Application firewall policy resource type
  type string
  // Application firewall policy etag
  etag string
  // Application firewall policy properties
  properties dict
  // List of associated application gateways
  gateways() []azure.subscription.networkService.applicationGateway
}

// Azure Storage
private azure.subscription.storageService {
  // Subscription identifier
  subscriptionId string
  // List of storage accounts
  accounts() []azure.subscription.storageService.account
}

// Azure Storage account
private azure.subscription.storageService.account @defaults("id name location") {
  // Storage account ID
  id string
  // Storage account name
  name string
  // Storage account location
  location string
  // Storage account tags
  tags map[string]string
  // Storage account type
  type string
  // Storage account properties
  properties dict
  // Storage account identity
  identity dict
  // Storage account SKU
  sku dict
  // Storage account kind
  kind string
  // Storage account containers
  containers() []azure.subscription.storageService.account.container
  // Storage account queue properties
  queueProperties() azure.subscription.storageService.account.service.properties
  // Storage account table properties
  tableProperties() azure.subscription.storageService.account.service.properties
  // Storage account blob properties
  blobProperties() azure.subscription.storageService.account.service.properties
  // Storage account data protection
  dataProtection() azure.subscription.storageService.account.dataProtection
}

// Azure Storage account data protection
private azure.subscription.storageService.account.dataProtection @defaults("blobSoftDeletionEnabled blobRetentionDays") {
  // ID of the storage account
  storageAccountId string
  // Whether soft deletion of blobs is enabled
  blobSoftDeletionEnabled bool
  // Retention period in days for deleted blobs
  blobRetentionDays int
   // Whether soft deletion of containers is enabled
  containerSoftDeletionEnabled bool
   // Retention period in days for deleted containers
  containerRetentionDays int
}

// Azure Storage account service properties
private azure.subscription.storageService.account.service.properties @defaults("id") {
  // ID of the service
  id string
  // Hourly metrics properties
  hourMetrics azure.subscription.storageService.account.service.properties.metrics
  // Minute metrics properties
  minuteMetrics azure.subscription.storageService.account.service.properties.metrics
  // Logging properties
  logging azure.subscription.storageService.account.service.properties.logging
}

// Azure Storage account service properties metrics
private azure.subscription.storageService.account.service.properties.metrics @defaults("id includeAPIs enabled") {
  // ID of the metrics
  id string
  // Whether metrics generate summary statistics for called API operations
  includeAPIs bool
  // Retention policy for the metrics
  retentionPolicy azure.subscription.storageService.account.service.properties.retentionPolicy
  // Whether the metrics are enabled
  enabled bool
  // Version of the metrics
  version string
}

// Azure Storage account service properties retention policy
private azure.subscription.storageService.account.service.properties.retentionPolicy @defaults("id") {
  // ID of the retention policy
  id string
  // Number of days that metrics or logging data is retained
  retentionDays int
  // Whether a retention policy is enabled for the Azure Storage
  enabled bool
}

// Azure Storage account service properties logging
private azure.subscription.storageService.account.service.properties.logging @defaults("id") {
  // ID of the logging configuration
  id string
  // Whether delete requests are logged
  delete bool
  // Whether read requests are logged
  read bool
  // Whether write requests are logged
  write bool
  // Logging version
  version string
  // Retention policy for the logging metrics
  retentionPolicy azure.subscription.storageService.account.service.properties.retentionPolicy
}

// Azure Storage container
private azure.subscription.storageService.account.container @defaults("id name") {
  // Storage container ID
  id string
  // Storage container name
  name string
  // Storage container type
  type string
  // Storage container etag
  etag string
  // Storage container properties
  properties dict
}


// Azure Web
private azure.subscription.webService {
  // Subscription identifier
  subscriptionId string
  // List of web apps
  apps() []azure.subscription.webService.appsite
  // Available runtimes
  availableRuntimes() []azure.subscription.webService.appRuntimeStack
}

// Azure Web App runtime stack
private azure.subscription.webService.appRuntimeStack @defaults("preferredOs runtimeVersion") {
  // Web App stack name
  name string
  // Web App stack preferred OS
  preferredOs string
  // Web App runtime version
  runtimeVersion string
  // Web App stack major version name
  majorVersion string
  // Web App stack minor version name
  minorVersion string
  // Whether the stack version is auto-updated
  autoUpdate bool
  // Whether the stack is deprecated
  deprecated bool
  // Whether the stack is hidden
  hidden bool
  // End-of-life date for the minor version
  endOfLifeDate time
}

// Azure Web app site
private azure.subscription.webService.appsite @defaults("id name location") {
  // App site ID
  id string
  // App site name
  name string
  // App site kind
  kind string
  // App site location
  location string
  // App site type
  type string
  // App site tags
  tags map[string]string
  // App site properties
  properties dict
  // App site identity
  identity dict
  // App site configuration
  configuration() azure.subscription.webService.appsiteconfig
  // App site authentication settings
  authenticationSettings() azure.subscription.webService.appsiteauthsettings
  // App site metadata
  metadata() dict
  // App site application settings
  applicationSettings() dict
  // App site connection settings
  connectionSettings() dict
  // App site stack
  stack() dict
  // Diagnostic settings for the web app site
  diagnosticSettings() []azure.subscription.monitorService.diagnosticsetting
  // List of functions for the web app site
  functions() []azure.subscription.webService.function
  // FTP publishing method policies
  ftp() azure.subscription.webService.appsite.basicPublishingCredentialsPolicies
  // SCM publishing method policies
  scm() azure.subscription.webService.appsite.basicPublishingCredentialsPolicies
}

// Azure AppSite basic publishing credentials policies
private azure.subscription.webService.appsite.basicPublishingCredentialsPolicies @defaults("id allow") {
  // Resource ID
  id string
  // Resource name
  name string
  // Resource type
  type string
  // Whether to allow access to a publishing method
  allow bool
}

// Azure AppSite authentication settings
private azure.subscription.webService.appsiteauthsettings @defaults("id name") {
  // Auth settings ID
  id string
  // Auth settings name
  name string
  // Auth settings kind
  kind string
  // Auth settings type
  type string
  // Auth settings properties
  properties dict
}

// Azure AppSite config
private azure.subscription.webService.appsiteconfig @defaults("id name") {
  // Appsite config ID
  id string
  // Appsite config name
  name string
  // Appsite config kind
  kind string
  // Appsite config type
  type string
  // Appsite config properties
  properties dict
}

// Azure SQL
private azure.subscription.sqlService {
  // Subscription identifier
  subscriptionId string
  // List of servers
  servers() []azure.subscription.sqlService.server
}

// Azure SQL Database server
private azure.subscription.sqlService.server @defaults("name location properties.version properties.state properties.fullyQualifiedDomainName") {
  // SQL Database server ID
  id string
  // SQL Database server name
  name string
  // SQL Database server location
  location string
  // SQL Database server tags
  tags map[string]string
  // SQL Database server type
  type string
  // SQL Database server properties
  properties dict
  // SQL Database server databases
  databases() []azure.subscription.sqlService.database
  // SQL Database server firewall rules
  firewallRules() []azure.subscription.sqlService.firewallrule
  // SQL Database server Entra ID administrators
  azureAdAdministrators() []azure.subscription.sqlService.server.administrator
  // SQL Database server connection policy
  connectionPolicy() dict
  // SQL Database server auditing policy
  auditingPolicy() dict
  // SQL Database server security alert policy
  securityAlertPolicy() dict
  // SQL Database server encryption protector
  encryptionProtector() dict
  // SQL Database server threat detection policy
  threatDetectionPolicy() dict
  // SQL Database server vulnerability assessment settings
  vulnerabilityAssessmentSettings() azure.subscription.sqlService.server.vulnerabilityassessmentsettings
  virtualNetworkRules() []azure.subscription.sqlService.virtualNetworkRule
}

// Azure SQL Database server vulnerability assessment settings
private azure.subscription.sqlService.server.vulnerabilityassessmentsettings {
  // ID of the vulnerability assessment
  id string
  // Name of the vulnerability assessment
  name string
  // Type of the vulnerability assessment
  type string
  // Blob storage container path to hold the scan results
  storageContainerPath string
  // Identifier key of the storage account for vulnerability assessment scan results
  storageAccountAccessKey string
  // Shared access signature key that has write access to the blob container specified in 'storageContainerPath'
  storageContainerSasKey string
  // Whether recurring scan is enabled
  recurringScanEnabled bool
  // Array of email addresses to which the scan notification is sent
  recurringScanEmails []string
  // Whether the schedule scan notification is also sent to the subscription administrators
  mailSubscriptionAdmins bool
}

// Azure SQL Database server administrator
private azure.subscription.sqlService.server.administrator @defaults("id name") {
  // SQL administrator ID
  id string
  // SQL administrator name
  name string
  // SQL administrator type
  type string
  // SQL administrator type
  administratorType string
  // SQL administrator login
  login string
  // SQL administrator subscription ID
  sid string
  // SQL administrator tenant ID
  tenantId string
}

// Azure SQL Database service database
private azure.subscription.sqlService.database @defaults("name edition zoneRedundant status creationDate maxSizeBytes earliestRestoreDate") {
  // SQL database ID
  id string
  // SQL database name
  name string
  // SQL database type
  type string
  // SQL database collation
  collation string
  // SQL database create date
  creationDate time
  // Database ID
  databaseId string
  // SQL database earliest restore date
  earliestRestoreDate time
  // SQL database create mode
  createMode string
  // SQL database source database ID
  sourceDatabaseId string
  // SQL database deletion date
  sourceDatabaseDeletionDate time
  // SQL database restore point in time
  restorePointInTime time
  // SQL database recovery services recovery point ID
  recoveryServicesRecoveryPointResourceId string
  // SQL database edition
  edition string
  // SQL database maximum byte size
  maxSizeBytes int
  // SQL database requested objective name
  requestedServiceObjectiveName string
  // SQL database service level objective
  serviceLevelObjective string
  // SQL database status
  status string
  // SQL database elastic pool name
  elasticPoolName string
  // SQL database default secondary location
  defaultSecondaryLocation string
  // SQL database failover group ID
  failoverGroupId string
  // SQL database read scale
  readScale string
  // SQL database sample name
  sampleName string
  // Whether the database is zone redundant
  zoneRedundant bool
  // SQL database transparent data encryption
  transparentDataEncryption() dict
  // SQL database advisor
  advisor() []dict
  // SQL database threat detection policy
  threatDetectionPolicy() dict
  // SQL database connection policy
  connectionPolicy() dict
  // SQL database auditing policy
  auditingPolicy() dict
  // SQL database usage
  usage() []azure.subscription.sqlService.databaseusage
}

// Azure SQL Database service database usage
private azure.subscription.sqlService.databaseusage @defaults("id name") {
  // Database usage ID
  id string
  // Database usage name
  name string
  // Database usage resource name
  resourceName string
  // Database usage display name
  displayName string
  // Database usage current value
  currentValue float
  // Database usage limit
  limit float
  // Database usage unit
  unit string
}

// Azure Database for PostgreSQL
private azure.subscription.postgreSqlService {
  // Subscription identifier
  subscriptionId string
  // List of PostgreSQL servers
  servers() []azure.subscription.postgreSqlService.server
  // List of PostgreSQL flexible servers
  flexibleServers() []azure.subscription.postgreSqlService.flexibleServer
}

// Azure Database for PostgreSQL flexible server
private azure.subscription.postgreSqlService.flexibleServer @defaults("name location properties.version properties.state properties.fullyQualifiedDomainName") {
  // PostgreSQL server ID
  id string
  // PostgreSQL server name
  name string
  // PostgreSQL server location
  location string
  // PostgreSQL server tags
  tags map[string]string
  // PostgreSQL server type
  type string
  // PostgreSQL server properties
  properties dict
  // PostgreSQL server configuration
  configuration() []azure.subscription.sqlService.configuration
  // PostgreSQL server databases
  databases() []azure.subscription.postgreSqlService.database
  // PostgreSQL server firewall rules
  firewallRules() []azure.subscription.sqlService.firewallrule
}

// Azure Database for PostgreSQL server
private azure.subscription.postgreSqlService.server @defaults("id name location") {
  // PostgreSQL server ID
  id string
  // PostgreSQL server name
  name string
  // PostgreSQL server location
  location string
  // PostgreSQL server tags
  tags map[string]string
  // PostgreSQL server type
  type string
  // PostgreSQL server properties
  properties dict
  // PostgreSQL server configuration
  configuration() []azure.subscription.sqlService.configuration
  // PostgreSQL server databases
  databases() []azure.subscription.postgreSqlService.database
  // PostgreSQL server firewall rules
  firewallRules() []azure.subscription.sqlService.firewallrule
}

// Azure Database for PostgreSQL database
private azure.subscription.postgreSqlService.database @defaults("id name") {
  // PostgreSQL database ID
  id string
  // PostgreSQL database name
  name string
  // PostgreSQL database type
  type string
  // PostgreSQL database charset
  charset string
  // PostgreSQL database collation
  collation string
}

// Azure SQL configuration
private azure.subscription.sqlService.configuration @defaults("name value") {
  // SQL configuration ID
  id string
  // SQL configuration name
  name string
  // SQL configuration type
  type string
  // SQL configuration value
  value string
  // SQL configuration description
  description string
  // SQL configuration default value
  defaultValue string
  // SQL configuration data type
  dataType string
  // SQL configuration allowed values
  allowedValues string
  // SQL configuration source
  source string
}

// Azure SQL firewall rule
private azure.subscription.sqlService.firewallrule @defaults("id name") {
  // SQL firewall rule ID
  id string
  // SQL firewall rule name
  name string
  // SQL firewall rule type
  type string
  // SQL firewall rule start IP address
  startIpAddress string
  // SQL firewall rule end IP address
  endIpAddress string
}

// Azure SQL virtual network rule
private azure.subscription.sqlService.virtualNetworkRule @defaults("id name") {
  // Virtual network rule ID
  id string
  // Virtual network rule name
  name string
  // Virtual network rule type
  type string
  // Virtual network rule properties
  properties dict
  // Virtual network rule Subnet ID
  virtualNetworkSubnetId string
}

// Azure Database for MySQL
private azure.subscription.mySqlService {
  // Subscription identifier
  subscriptionId string
  // List of MySQL servers
  servers() []azure.subscription.mySqlService.server
  // List of Flexible MySQL servers
  flexibleServers() []azure.subscription.mySqlService.flexibleServer
}

// Azure Database for MySQL server
private azure.subscription.mySqlService.server @defaults("id name location") {
  // MySQL server ID
  id string
  // MySQL server name
  name string
  // MySQL server location
  location string
  // MySQL server tags
  tags map[string]string
  // MySQL server type
  type string
  // MySQL server properties
  properties dict
  // MySQL server configuration
  configuration() []azure.subscription.sqlService.configuration
  // MySQL server databases
  databases() []azure.subscription.mySqlService.database
  // MySQL server firewall rules
  firewallRules() []azure.subscription.sqlService.firewallrule
}

// Azure Database for MySQL database
private azure.subscription.mySqlService.database @defaults("id name") {
  // MySQL database ID
  id string
  // MySQL database name
  name string
  // MySQL database type
  type string
  // MySQL database character set
  charset string
  // MySQL database collation
  collation string
}

// Azure Database for MySQL flexible server
private azure.subscription.mySqlService.flexibleServer @defaults("name location properties.version properties.state properties.fullyQualifiedDomainName") {
  // MySQL flexible server ID
  id string
  // MySQL flexible server name
  name string
  // MySQL flexible server location
  location string
  // MySQL flexible server tags
  tags map[string]string
  // MySQL flexible server type
  type string
  // MySQL flexible server properties
  properties dict
  // MySQL flexible server configuration
  configuration() []azure.subscription.sqlService.configuration
  // MySQL flexible server databases
  databases() []azure.subscription.mySqlService.database
  // MySQL flexible server firewall rules
  firewallRules() []azure.subscription.sqlService.firewallrule
}

// Azure Database for MariaDB
private azure.subscription.mariaDbService {
  // Subscription identifier
  subscriptionId string
  // List of MariaDB servers
  servers() []azure.subscription.mariaDbService.server
}

// Azure Database for MariaDB server
private azure.subscription.mariaDbService.server @defaults("id name location") {
  // MariaDB server ID
  id string
  // MariaDB server name
  name string
  // MariaDB server location
  location string
  // MariaDB server tags
  tags map[string]string
  // MariaDB server type
  type string
  // MariaDB server properties
  properties dict
  // MariaDB server configuration
  configuration() []azure.subscription.sqlService.configuration
  // MariaDB server databases
  databases() []azure.subscription.mariaDbService.database
  // MariaDB server firewall rules
  firewallRules() []azure.subscription.sqlService.firewallrule
}

// Azure Database for MariaDB database
private azure.subscription.mariaDbService.database @defaults("id name") {
  // MariaDB database ID
  id string
  // MariaDB database name
  name string
  // MariaDB database type
  type string
  // MariaDB database character set
  charset string
  // MariaDB database collation
  collation string
}

// Azure Cosmos DB
private azure.subscription.cosmosDbService {
  // Subscription identifier
  subscriptionId string
  // List of Cosmos DB accounts
  accounts() []azure.subscription.cosmosDbService.account
}

// Azure Cosmos DB account
private azure.subscription.cosmosDbService.account @defaults("name kind location") {
  // Cosmos DB account ID
  id string
  // Cosmos DB account name
  name string
  // Cosmos DB account properties
  properties dict
  // Cosmos DB account location
  location string
  // Cosmos DB account tags
  tags map[string]string
  // Cosmos DB account type
  type string
  // Cosmos DB account kind
  kind string
}

// Azure Key Vault
private azure.subscription.keyVaultService {
  // Subscription identifier
  subscriptionId string
  // List of Azure key vaults
  vaults() []azure.subscription.keyVaultService.vault
}

// Azure Key Vault vault
private azure.subscription.keyVaultService.vault @defaults("vaultName type vaultUri location") {
  // Vault ID
  id string
  // Vault name
  vaultName string
  // Vault type
  type string
  // Vault location
  location string
  // Vault tags
  tags map[string]string
  // Vault URL
  vaultUri() string
  // Vault properties
  properties() dict
  // Whether RBAC access to the vault is enabled
  rbacAuthorizationEnabled() bool
  // Vault keys
  keys() []azure.subscription.keyVaultService.key
  // Vault certificates
  certificates() []azure.subscription.keyVaultService.certificate
  // Vault secrets
  secrets() []azure.subscription.keyVaultService.secret
  // Vault diagnostic settings
  diagnosticSettings() []azure.subscription.monitorService.diagnosticsetting
  // Auto-rotation enabled status for all keys
  autorotation() []azure.subscription.keyVaultService.key.autorotation
}

// Azure Key Vault key auto-rotation
private azure.subscription.keyVaultService.key.autorotation @defaults("enabled") {
  // Key ID
  kid string
  // Auto-rotation enabled status
  enabled bool
}

// Azure Key Vault key
private azure.subscription.keyVaultService.key @defaults("kid keyName") {
  // Key ID
  kid string
  // Key tags
  tags map[string]string
  // Whether the key is managed
  managed bool
  // Whether the key is enabled
  enabled bool
  // Date the key begins to be usable
  notBefore time
  // Date the key expires
  expires time
  // Key creation time
  created time
  // Key last update time
  updated time
  // Key recovery level
  recoveryLevel string
  // Key name
  keyName() string
  // Key version
  version() string
  // List of key versions
  versions() []azure.subscription.keyVaultService.key
}

// Azure Key Vault certificate
private azure.subscription.keyVaultService.certificate @defaults("id certName") {
  // Certificate ID
  id string
  // Certificate tags
  tags map[string]string
  // Certificate x5t
  x5t string
  // Whether the certificate is enabled
  enabled bool
  // Certificate not before date
  notBefore time
  // Certificate expiration date
  expires time
  // Certificate creation time
  created time
  // Certificate last update time
  updated time
  // Certificate recovery level
  recoveryLevel string
  // Certificate name
  certName() string
  // Certificate version
  version() string
  // List of certificate versions
  versions() []azure.subscription.keyVaultService.certificate
}

// Azure Key Vault secret
private azure.subscription.keyVaultService.secret @defaults("id secretName") {
  // Secret ID
  id string
  // Secret tags
  tags map[string]string
  // Secret content type
  contentType string
  // Whether the secret is managed
  managed bool
  // Whether the secret is enabled
  enabled bool
  // Date the secret begins to be usable
  notBefore time
  // Secret expiration date
  expires time
  // Secret creation date
  created time
  // Secret last updated date
  updated time
  // Secret name
  secretName() string
  // Secret version
  version() string
  // List of secret versions
  versions() []azure.subscription.keyVaultService.secret
}

// Azure Monitor
private azure.subscription.monitorService {
  // Subscription identifier
  subscriptionId string
  // List of log profiles
  logProfiles() []azure.subscription.monitorService.logprofile
  // List of diagnostic settings for the subscription
  diagnosticSettings() []azure.subscription.monitorService.diagnosticsetting
  // Application insights for the subscription
  applicationInsights() []azure.subscription.monitorService.applicationInsight
  // Monitor activity log
  activityLog() azure.subscription.monitorService.activityLog
}

// Azure Monitor activity log
private azure.subscription.monitorService.activityLog @defaults("alerts") {
  // Subscription identifier
  subscriptionId string
  // List of activity log alerts
  alerts() []azure.subscription.monitorService.activityLog.alert
}

// Azure Monitor application insights
private azure.subscription.monitorService.applicationInsight @defaults("name location type")  {
  // Application insight ID
  id string
  // Application insight name
  name string
  // Application insight properties
  properties dict
  // Application insight tags
  tags map[string]string
  // Application insight location
  location string
  // Application insight kind
  kind string
  // Application insight type
  type string
}

// Azure Monitor activity log alert
private azure.subscription.monitorService.activityLog.alert @defaults("name type") {
  // ID of the activity log alert
  id string
  // Type of the activity log alert
  type string
  // Name of the activity log alert
  name string
  // Description of the activity log alert
  description string
  // Conditions for the activity log alert, all of which must be met
  conditions []dict
  // Location of the alert
  location string
  // Tags of the alert
  tags map[string]string
  // Actions that activate when the conditions are met
  actions []dict
  // List of resource IDs that must be present to trigger the alert
  scopes []string
}

// Azure Monitor log profile
private azure.subscription.monitorService.logprofile @defaults("id name location") {
  // Log profile ID
  id string
  // Log profile name
  name string
  // Log profile location
  location string
  // Log profile type
  type string
  // Log profile tags
  tags map[string]string
  // Log profile properties
  properties dict
  // ID of the log profile storage account
  storageAccountId string
  // Log profile storage account
  storageAccount() azure.subscription.storageService.account
}

// Azure Monitor diagnostic setting
private azure.subscription.monitorService.diagnosticsetting @defaults("id name") {
  // Diagnostic setting ID
  id string
  // Diagnostic setting name
  name string
  // Diagnostic setting type
  type string
  // Diagnostic setting properties
  properties dict
  // ID of the diagnostic setting storage account
  storageAccountId string
  // Diagnostic setting storage account
  storageAccount() azure.subscription.storageService.account
}

// Microsoft Defender for Cloud
private azure.subscription.cloudDefenderService @defaults("defenderForServers.enabled defenderForContainers.enabled"){
  // Subscription identifier
  subscriptionId string
  // Whether the monitoring agent is automatically provisioned on new VMs
  monitoringAgentAutoProvision() bool
  // List of Defender for Servers components and whether they are enabled
  defenderForServers() dict
  // Microsoft Defender for App Service configuration
  defenderForAppServices() dict
  // Microsoft Defender for SQL servers on machines configuration
  defenderForSqlServersOnMachines() dict
  // Microsoft Defender for Azure SQL Databases configuration
  defenderForSqlDatabases() dict
  // Microsoft Defender for open-source relational databases configuration
  defenderForOpenSourceDatabases() dict
  // Microsoft Defender for Azure Cosmos DB configuration
  defenderForCosmosDb() dict
  // Microsoft Defender for Storage Accounts configuration
  defenderForStorageAccounts() dict
  // Microsoft Defender for Key Vault configuration
  defenderForKeyVaults() dict
  // Microsoft Defender for Resource Manager configuration
  defenderForResourceManager() dict
  // Defender for Containers components configuration
  defenderForContainers() dict
  // List of configured security contacts
  securityContacts() []azure.subscription.cloudDefenderService.securityContact
  // Settings for MCAS
  settingsMCAS() azure.subscription.cloudDefenderService.settings
  // Settings for WDATP
  settingsWDATP() azure.subscription.cloudDefenderService.settings
  // Settings for Sentinel
  settingsSentinel() azure.subscription.cloudDefenderService.settings
}

// Microsoft Defender for Cloud security settings
private azure.subscription.cloudDefenderService.settings @defaults("name kind") {
  // Resource ID
  id string
  // The settings name
  name string
  // The settings kind (data export, alert sync, etc.)
  kind string
  // The settings type
  type string
  // The properties dict (enabled)
  properties dict
}

// Microsoft Defender for Cloud security contact
private azure.subscription.cloudDefenderService.securityContact @defaults("id name"){
  // ID of the security contact
  id string
  // Name of the security contact
  name string
  // Emails that receive security alerts
  emails []string
  // Deprecated: use `notificationSources` instead
  alertNotifications dict
  // A collection of sources which evaluate the email notification
  notificationSources dict
  // Notifications by role settings
  notificationsByRole dict
}

// Azure IAM service
private azure.subscription.authorizationService {
  // Subscription identifier
  subscriptionId string
  // Role definitions for the Azure subscription
  roles() []azure.subscription.authorizationService.roleDefinition
  // Deprecated: use `roles` instead
  roleDefinitions() []azure.subscription.authorizationService.roleDefinition
  // Role assignments
  roleAssignments() []azure.subscription.authorizationService.roleAssignment
  // Managed identities
  managedIdentities() []azure.subscription.managedIdentity
}

// Azure role definition
private azure.subscription.authorizationService.roleDefinition @defaults ("name type") {
  // ID of the role definition
  id string
  // Description of the role definition
  description string
  // Name of the role definition
  name string
  // Role type
  type string
  // Deprecated: use `type` instead
  isCustom bool
  // Scopes for which the role definition applies
  scopes []string
  // Permissions that are attached to the role definition
  permissions []azure.subscription.authorizationService.roleDefinition.permission
}

// Azure role definition permission
private azure.subscription.authorizationService.roleDefinition.permission @defaults ("allowedActions deniedActions") {
  // ID of the permission
  id string
  // List of allowed actions that are attached to the permission
  allowedActions []string
  // List of denied actions that are attached to the permission
  deniedActions []string
  // List of allowed data actions that are attached to the permission
  allowedDataActions []string
  // List of denied data actions that are attached to the permission
  deniedDataActions []string
}

// Azure role assignment
private azure.subscription.authorizationService.roleAssignment  @defaults("principalId type role.name") {
  id string
  description string
  type string
  scope string
  principalId string
  condition string
  createdAt time
  updatedAt time
  role() azure.subscription.authorizationService.roleDefinition
}

// Azure managed identity
private azure.subscription.managedIdentity @defaults("name") {
  name string
  clientId string
  principalId string
  tenantId string
  roleAssignments() []azure.subscription.authorizationService.roleAssignment
}

// Azure Kubernetes Service
private azure.subscription.aksService {
  // Subscription identifier
  subscriptionId string
  // List all the AKS clusters inside the subscription
  clusters() []azure.subscription.aksService.cluster
}

// Azure Kubernetes Service cluster
private azure.subscription.aksService.cluster @defaults("name location kubernetesVersion") {
  // ID of the AKS cluster
  id string
  // Name of the AKS cluster
  name string
  // Location of the AKS cluster
  location string
  // The Kubernetes version of the AKS cluster
  kubernetesVersion string
  // The provisioning state of the AKS cluster
  provisioningState string
  // The power state of the AKS cluster
  powerState string
  // The tags of the AKS cluster
  tags map[string]string
  // The node resource group of the AKS cluster
  nodeResourceGroup string
  // Time the AKS cluster was created
  createdAt time
  // Whether RBAC is enabled for the AKS cluster
  rbacEnabled bool
  // The fully qualified domain name of the AKS cluster
  fqdn string
  // The DNS prefix of the AKS cluster
  dnsPrefix string
  // The storage profile of the AKS cluster
  storageProfile dict
  // The workload autoscaler profile of the AKS cluster
  workloadAutoScalerProfile dict
  // The security profile of the AKS cluster
  securityProfile dict
  // The pod identity profile of the AKS cluster
  podIdentityProfile dict
  // The network profile of the AKS cluster
  networkProfile dict
  // The HTTP proxy config of the AKS cluster
  httpProxyConfig dict
  // The add-on profiles of the AKS cluster
  addonProfiles []dict
  // The agent pool profiles of the AKS cluster
  agentPoolProfiles []dict
  // The API server access profile
  apiServerAccessProfile dict
}

// Azure Advisor
private azure.subscription.advisorService @defaults("averageScore recommendations.length scores.length") {
  // Subscription identifier
  subscriptionId string
  // List of all recommendations by the advisor
  recommendations() []azure.subscription.advisorService.recommendation
  // List of scores by categories
  scores() []azure.subscription.advisorService.score
  // Average advisory score
  averageScore() float
}

// Azure Advisor recommendation
private azure.subscription.advisorService.recommendation @defaults("name category impact description impactedResource") {
  // Recommendation ID
  id string
  // Recommendation name
  name string
  // Recommendation resource type
  type string
  // Recommendation category
  category string
  // Recommendation risk
  risk string
  // Recommendation risk
  impact string
  // Recommendation description
  description string
  // Recommendation remediation
  remediation string
  // The impacted resource category type
  impactedResourceType string
  // The impacted resource
  impactedResource string
  // Recommendation properties
  properties dict
}

// Azure Advisor score
private azure.subscription.advisorService.score @defaults("name") {
  // Score identifier
  id string
  // Score name
  name string
  // Score type
  type string
  // Current score for the advisor category
  currentScore azure.subscription.advisorService.securityScore
  // List of time series, containing previous scores for the category
  timeSeries []azure.subscription.advisorService.timeSeries
}

// Azure Advisor time series
private azure.subscription.advisorService.timeSeries {
  // Advisor time series identifier
  id string
  // The aggregation level for the time series
  aggregationLevel string
  // The scores in the time series
  scores []azure.subscription.advisorService.securityScore
}

// Azure Advisor security score
private azure.subscription.advisorService.securityScore {
  // Security score identifier
  id string
  // Security score score
  score float
  // The date the score was calculated
  date time
  // The potential percentage increase in overall score at subscription level after all recommendations are implemented
  potentialScoreIncrease float
  // The number of impacted resources
  impactedResourcesCount int
  // The count of impacted categories
  categoryCount int
  // The consumption units for the score
  consumptionUnits float
}

// Azure Policy service
private azure.subscription.policy @defaults("subscriptionId") {
  // Subscription identifier
  subscriptionId string
  // List of policy assignments in the subscription
  assignments () []azure.subscription.policy.assignment
}

// Azure Policy assignment
private azure.subscription.policy.assignment @defaults("name enforcementMode") {
  // Policy definition ID
  id string
  // Policy name
  name string
  // Policy scope
  scope string
  // Policy description
  description string
  // Policy enforcement Mode
  enforcementMode string
  // Policy parameters
  parameters dict
}

// Azure IoT Hub Service
private azure.subscription.iotService {
  // Subscription identifier
  subscriptionId string
  // List of IoT hubs in the subscription
  hubs() []dict
}
