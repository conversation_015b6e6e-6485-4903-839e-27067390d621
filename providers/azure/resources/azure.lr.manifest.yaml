# Copyright (c) Mondoo, Inc.
# SPDX-License-Identifier: BUSL-1.1

resources:
  azure:
    fields: {}
    min_mondoo_version: latest
    platform:
      name:
      - azure
  azure.subscription:
    docs:
      desc: Use the `azure.subscription` resource to assess the configuration of Azure
        subscriptions.
    fields:
      advisor: {}
      aks: {}
      authorization: {}
      authorizationSource: {}
      cloudDefender: {}
      compute: {}
      cosmosDb: {}
      iam:
        min_mondoo_version: 9.0.0
      id: {}
      iot:
        min_mondoo_version: 9.0.0
      keyVault: {}
      managedByTenants: {}
      mariaDb: {}
      monitor: {}
      mySql: {}
      name: {}
      network: {}
      policy:
        min_mondoo_version: 9.0.0
      postgreSql: {}
      resourceGroups: {}
      resources: {}
      sql: {}
      state: {}
      storage: {}
      subscriptionId: {}
      subscriptionsPolicies: {}
      tags: {}
      tenantId: {}
      web: {}
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Subscriptions, licenses, accounts, and tenants for Microsoft's cloud
        offerings
      url: https://learn.microsoft.com/en-us/microsoft-365/enterprise/subscriptions-licenses-accounts-and-tenants-for-microsoft-cloud-offerings
    snippets:
    - query: "azure.subscription {\n  subscriptionId \n  managedByTenants\n}\n"
      title: Return the subscription ID and a list of tenants that manage the subscription
  azure.subscription.advisor:
    docs:
      desc: Use the `azure.subscription.advisor` resource to retrieve scoring and
        recommendations from Microsoft Azure Advisor.
    fields:
      recommendations: {}
      subscriptionId: {}
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: What are managed identities for Azure resources?
      url: https://learn.microsoft.com/en-us/entra/identity/managed-identities-azure-resources/overview
    snippets:
    - query: "azure.subscription.advisor {\n  averageScore \n recommendations \n}\n"
      title: Return the average score and all recommendations from Microsoft Azure
        Advisor
    - query: |
        azure.subscription.advisorService {
          averageScore > 90
        }
      title: Check if the average score that the Azure Advisor service gives the subscription
        is greater than 90
  azure.subscription.advisor.recommendation:
    fields:
      id: {}
      properties: {}
    min_mondoo_version: latest
    platform:
      name:
      - azure
  azure.subscription.advisorService:
    docs:
      desc: Use the `azure.subscription.advisorService` resource to retrieve scoring
        and recommendations from Microsoft Azure Advisor.
    fields:
      averageScore: {}
      recommendations: {}
      scores: {}
      subscriptionId: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - azure
  azure.subscription.advisorService.recommendation:
    fields:
      category: {}
      description: {}
      id: {}
      impact: {}
      impactedResource: {}
      impactedResourceType: {}
      name: {}
      properties: {}
      remediation: {}
      risk: {}
      type: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - azure
  azure.subscription.advisorService.score:
    fields:
      currentScore: {}
      id: {}
      name: {}
      timeSeries: {}
      type: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - azure
  azure.subscription.advisorService.securityScore:
    fields:
      categoryCount: {}
      consumptionUnits: {}
      date: {}
      id: {}
      impactedResourcesCount: {}
      potentialScoreIncrease: {}
      score: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - azure
  azure.subscription.advisorService.timeSeries:
    fields:
      aggregationLevel: {}
      id: {}
      scores: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - azure
  azure.subscription.aks:
    fields:
      clusters: {}
      subscriptionId: {}
    min_mondoo_version: latest
    platform:
      name:
      - azure
  azure.subscription.aks.cluster:
    docs:
      desc: Use the `azure.subscription.aks.cluster` resource to retrieve and check
        properties for an Azure Kubernetes Service cluster.
    fields:
      addonProfiles: {}
      agentPoolProfiles: {}
      createdAt: {}
      dnsPrefix: {}
      fqdn: {}
      httpProxyConfig: {}
      id: {}
      kubernetesVersion: {}
      location: {}
      name: {}
      networkProfile: {}
      nodeResourceGroup: {}
      podIdentityProfile: {}
      powerState: {}
      provisioningState: {}
      rbacEnabled: {}
      securityProfile: {}
      storageProfile: {}
      tags: {}
      workloadAutoScalerProfile: {}
    min_mondoo_version: 7.17.0
    platform:
      name:
      - azure
    refs:
    - title: What is Azure Kubernetes Service (AKS)?
      url: https://learn.microsoft.com/en-us/azure/aks/what-is-aks
    - title: Azure Kubernetes Service (AKS) documentation
      url: https://learn.microsoft.com/en-us/azure/aks/
    snippets:
    - query: "azure.subscription.aks.clusters {\n  id \n createdAt \n powerState \n}\n"
      title: Return the ID, date and time created, and current power state of clusters
  azure.subscription.aksService:
    fields:
      clusters: {}
      subscriptionId: {}
    is_private: true
    min_mondoo_version: 7.17.0
    platform:
      name:
      - azure
    refs:
    - title: What is Azure Kubernetes Service (AKS)?
      url: https://learn.microsoft.com/en-us/azure/aks/what-is-aks
    - title: Azure Kubernetes Service (AKS) documentation
      url: https://learn.microsoft.com/en-us/azure/aks/
  azure.subscription.aksService.cluster:
    fields:
      addonProfiles: {}
      agentPoolProfiles: {}
      apiServerAccessProfile:
        min_mondoo_version: 9.0.0
      createdAt: {}
      dnsPrefix: {}
      fqdn: {}
      httpProxyConfig: {}
      id: {}
      kubernetesVersion: {}
      location: {}
      name: {}
      networkProfile: {}
      nodeResourceGroup: {}
      podIdentityProfile: {}
      powerState: {}
      provisioningState: {}
      rbacEnabled: {}
      securityProfile: {}
      storageProfile: {}
      tags: {}
      workloadAutoScalerProfile: {}
    is_private: true
    min_mondoo_version: 7.17.0
    platform:
      name:
      - azure
    refs:
    - title: What is Azure Kubernetes Service (AKS)?
      url: https://learn.microsoft.com/en-us/azure/aks/what-is-aks
    - title: Azure Kubernetes Service (AKS) documentation
      url: https://learn.microsoft.com/en-us/azure/aks/
  azure.subscription.authorization:
    fields:
      roleDefinitions: {}
      subscriptionId: {}
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure RBAC documentation
      url: https://learn.microsoft.com/en-us/azure/role-based-access-control/
  azure.subscription.authorization.roleDefinition:
    fields:
      description: {}
      id: {}
      isCustom: {}
      name: {}
      permissions: {}
      scopes: {}
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure RBAC documentation
      url: https://learn.microsoft.com/en-us/azure/role-based-access-control/
  azure.subscription.authorization.roleDefinition.permission:
    fields:
      allowedActions: {}
      allowedDataActions: {}
      deniedActions: {}
      deniedDataActions: {}
      id: {}
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure RBAC documentation
      url: https://learn.microsoft.com/en-us/azure/role-based-access-control/
  azure.subscription.authorizationService:
    fields:
      managedIdentities:
        min_mondoo_version: 9.0.0
      roleAssignments:
        min_mondoo_version: 9.0.0
      roleDefinitions: {}
      roles:
        min_mondoo_version: 9.0.0
      subscriptionId: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure RBAC documentation
      url: https://learn.microsoft.com/en-us/azure/role-based-access-control/
  azure.subscription.authorizationService.roleAssignment:
    fields:
      condition: {}
      createdAt: {}
      description: {}
      id: {}
      name: {}
      principalId: {}
      principalType: {}
      role: {}
      scope: {}
      type: {}
      updatedAt: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - azure
  azure.subscription.authorizationService.roleDefinition:
    fields:
      description: {}
      id: {}
      isCustom: {}
      name: {}
      permissions: {}
      scopes: {}
      type:
        min_mondoo_version: 9.0.0
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure RBAC documentation
      url: https://learn.microsoft.com/en-us/azure/role-based-access-control/
  azure.subscription.authorizationService.roleDefinition.permission:
    fields:
      allowedActions: {}
      allowedDataActions: {}
      deniedActions: {}
      deniedDataActions: {}
      id: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure RBAC documentation
      url: https://learn.microsoft.com/en-us/azure/role-based-access-control/
  azure.subscription.cloudDefender:
    fields:
      defenderForAppServices: {}
      defenderForContainers: {}
      defenderForCosmosDb: {}
      defenderForKeyVaults: {}
      defenderForOpenSourceDatabases: {}
      defenderForResourceManager: {}
      defenderForServers: {}
      defenderForSqlDatabases: {}
      defenderForSqlServersOnMachines: {}
      defenderForStorageAccounts: {}
      monitoringAgentAutoProvision: {}
      securityContacts: {}
      subscriptionId: {}
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Microsoft Defender for Cloud Apps overview
      url: https://learn.microsoft.com/en-us/defender-cloud-apps/what-is-defender-for-cloud-apps
  azure.subscription.cloudDefender.securityContact:
    fields:
      alertNotifications: {}
      emails: {}
      id: {}
      name: {}
      notificationsByRole: {}
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Microsoft Defender for Cloud Apps overview
      url: https://learn.microsoft.com/en-us/defender-cloud-apps/what-is-defender-for-cloud-apps
  azure.subscription.cloudDefenderService:
    fields:
      defenderForAppServices:
        min_mondoo_version: 9.0.0
      defenderForContainers: {}
      defenderForCosmosDb:
        min_mondoo_version: 9.0.0
      defenderForKeyVaults:
        min_mondoo_version: 9.0.0
      defenderForOpenSourceDatabases:
        min_mondoo_version: 9.0.0
      defenderForResourceManager:
        min_mondoo_version: 9.0.0
      defenderForServers: {}
      defenderForSqlDatabases:
        min_mondoo_version: 9.0.0
      defenderForSqlServersOnMachines:
        min_mondoo_version: 9.0.0
      defenderForStorageAccounts:
        min_mondoo_version: 9.0.0
      monitoringAgentAutoProvision: {}
      securityContacts: {}
      settingsMCAS:
        min_mondoo_version: 9.0.0
      settingsSentinel:
        min_mondoo_version: 9.0.0
      settingsWDATP:
        min_mondoo_version: 9.0.0
      subscriptionId: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Microsoft Defender for Cloud Apps overview
      url: https://learn.microsoft.com/en-us/defender-cloud-apps/what-is-defender-for-cloud-apps
  azure.subscription.cloudDefenderService.securityContact:
    fields:
      alertNotifications: {}
      emails: {}
      id: {}
      name: {}
      notificationSources:
        min_mondoo_version: 9.0.0
      notificationsByRole: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Microsoft Defender for Cloud Apps overview
      url: https://learn.microsoft.com/en-us/defender-cloud-apps/what-is-defender-for-cloud-apps
  azure.subscription.cloudDefenderService.settings:
    fields:
      id: {}
      kind: {}
      name: {}
      properties: {}
      type: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - azure
  azure.subscription.compute:
    fields:
      disks: {}
      subscriptionId: {}
      vms: {}
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure Compute
      url: https://azure.microsoft.com/en-us/products/category/compute
  azure.subscription.compute.disk:
    fields:
      id: {}
      location: {}
      managedBy: {}
      managedByExtended: {}
      name: {}
      properties: {}
      sku: {}
      tags: {}
      type: {}
      zones: {}
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Introduction to Azure managed disks
      url: https://learn.microsoft.com/en-us/azure/virtual-machines/managed-disks-overview
  azure.subscription.compute.vm:
    fields:
      dataDisks: {}
      extensions: {}
      id: {}
      location: {}
      name: {}
      osDisk: {}
      properties: {}
      publicIpAddresses: {}
      tags: {}
      type: {}
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Virtual machines in Azure
      url: https://learn.microsoft.com/en-us/azure/virtual-machines/
  azure.subscription.computeService:
    fields:
      disks: {}
      subscriptionId: {}
      vms: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Virtual machines in Azure
      url: https://learn.microsoft.com/en-us/azure/virtual-machines/
  azure.subscription.computeService.disk:
    fields:
      id: {}
      location: {}
      managedBy: {}
      managedByExtended: {}
      name: {}
      properties: {}
      sku: {}
      tags: {}
      type: {}
      zones: {}
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Introduction to Azure managed disks
      url: https://learn.microsoft.com/en-us/azure/virtual-machines/managed-disks-overview
  azure.subscription.computeService.vm:
    fields:
      dataDisks: {}
      extensions: {}
      id: {}
      isRunning: {}
      location: {}
      name: {}
      osDisk: {}
      properties: {}
      publicIpAddresses: {}
      state: {}
      tags: {}
      type: {}
      zones: {}
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Virtual machines in Azure
      url: https://learn.microsoft.com/en-us/azure/virtual-machines/
  azure.subscription.cosmosDb:
    fields:
      accounts: {}
      subscriptionId: {}
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure Cosmos DB documentation
      url: https://learn.microsoft.com/en-us/azure/cosmos-db/
  azure.subscription.cosmosDb.account:
    fields:
      id: {}
      kind: {}
      location: {}
      name: {}
      properties: {}
      tags: {}
      type: {}
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure Cosmos DB documentation
      url: https://learn.microsoft.com/en-us/azure/cosmos-db/
  azure.subscription.cosmosDbService:
    fields:
      accounts: {}
      subscriptionId: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure Cosmos DB documentation
      url: https://learn.microsoft.com/en-us/azure/cosmos-db/
  azure.subscription.cosmosDbService.account:
    fields:
      id: {}
      kind: {}
      location: {}
      name: {}
      properties: {}
      tags: {}
      type: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure Cosmos DB documentation
      url: https://learn.microsoft.com/en-us/azure/cosmos-db/
  azure.subscription.function:
    fields:
      id: {}
      kind: {}
      name: {}
      properties: {}
      type: {}
    min_mondoo_version: 9.0.0
    platform:
      name:
      - azure
  azure.subscription.iotService:
    fields:
      hubs: {}
      subscriptionId: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - azure
  azure.subscription.keyVault:
    fields:
      subscriptionId: {}
      vaults: {}
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure Key Vault
      url: https://learn.microsoft.com/en-us/azure/key-vault/
  azure.subscription.keyVault.certificate:
    fields:
      certName: {}
      created: {}
      enabled: {}
      expires: {}
      id: {}
      notBefore: {}
      recoveryLevel: {}
      tags: {}
      updated: {}
      version: {}
      versions: {}
      x5t: {}
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure Key Vault
      url: https://learn.microsoft.com/en-us/azure/key-vault/
  azure.subscription.keyVault.key:
    fields:
      created: {}
      enabled: {}
      expires: {}
      keyName: {}
      kid: {}
      managed: {}
      notBefore: {}
      recoveryLevel: {}
      tags: {}
      updated: {}
      version: {}
      versions: {}
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure Key Vault
      url: https://learn.microsoft.com/en-us/azure/key-vault/
  azure.subscription.keyVault.secret:
    fields:
      contentType: {}
      created: {}
      enabled: {}
      expires: {}
      id: {}
      managed: {}
      notBefore: {}
      secretName: {}
      tags: {}
      updated: {}
      version: {}
      versions: {}
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure Key Vault
      url: https://learn.microsoft.com/en-us/azure/key-vault/
  azure.subscription.keyVault.vault:
    fields:
      certificates: {}
      diagnosticSettings: {}
      id: {}
      keys: {}
      location: {}
      properties: {}
      secrets: {}
      tags: {}
      type: {}
      vaultName: {}
      vaultUri: {}
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure Key Vault
      url: https://learn.microsoft.com/en-us/azure/key-vault/
  azure.subscription.keyVaultService:
    fields:
      subscriptionId: {}
      vaults: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure Key Vault
      url: https://learn.microsoft.com/en-us/azure/key-vault/
  azure.subscription.keyVaultService.certificate:
    fields:
      certName: {}
      created: {}
      enabled: {}
      expires: {}
      id: {}
      notBefore: {}
      recoveryLevel: {}
      tags: {}
      updated: {}
      version: {}
      versions: {}
      x5t: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure Key Vault
      url: https://learn.microsoft.com/en-us/azure/key-vault/
  azure.subscription.keyVaultService.key:
    fields:
      created: {}
      enabled: {}
      expires: {}
      keyName: {}
      kid: {}
      managed: {}
      notBefore: {}
      recoveryLevel: {}
      tags: {}
      updated: {}
      version: {}
      versions: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure Key Vault
      url: https://learn.microsoft.com/en-us/azure/key-vault/
  azure.subscription.keyVaultService.key.autorotation:
    fields:
      enabled: {}
      kid: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - azure
  azure.subscription.keyVaultService.secret:
    fields:
      contentType: {}
      created: {}
      enabled: {}
      expires: {}
      id: {}
      managed: {}
      notBefore: {}
      secretName: {}
      tags: {}
      updated: {}
      version: {}
      versions: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure Key Vault
      url: https://learn.microsoft.com/en-us/azure/key-vault/
  azure.subscription.keyVaultService.vault:
    fields:
      autorotation:
        min_mondoo_version: 9.0.0
      certificates: {}
      diagnosticSettings: {}
      id: {}
      keys: {}
      location: {}
      properties: {}
      rbacAuthorizationEnabled: {}
      secrets: {}
      tags: {}
      type: {}
      vaultName: {}
      vaultUri: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure Key Vault
      url: https://learn.microsoft.com/en-us/azure/key-vault/
  azure.subscription.managedIdentity:
    fields:
      clientId: {}
      id: {}
      name: {}
      principalId: {}
      roleAssignments: {}
      tenantId: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - azure
  azure.subscription.managedIdentityService:
    fields:
      managedIdentities: {}
      subscriptionId: {}
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: What are managed identities for Azure resources?
      url: https://learn.microsoft.com/en-us/entra/identity/managed-identities-azure-resources/overview
  azure.subscription.managedIdentityService.managedIdentity:
    fields:
      id: {}
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: What are managed identities for Azure resources?
      url: https://learn.microsoft.com/en-us/entra/identity/managed-identities-azure-resources/overview
  azure.subscription.mariaDb:
    fields:
      servers: {}
      subscriptionId: {}
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure Database for MariaDB documentation
      url: https://learn.microsoft.com/en-us/azure/mariadb/
  azure.subscription.mariaDb.database:
    fields:
      charset: {}
      collation: {}
      id: {}
      name: {}
      type: {}
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure Database for MariaDB documentation
      url: https://learn.microsoft.com/en-us/azure/mariadb/
  azure.subscription.mariaDb.server:
    fields:
      configuration: {}
      databases: {}
      firewallRules: {}
      id: {}
      location: {}
      name: {}
      properties: {}
      tags: {}
      type: {}
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure Database for MariaDB documentation
      url: https://learn.microsoft.com/en-us/azure/mariadb/
  azure.subscription.mariaDbService:
    fields:
      servers: {}
      subscriptionId: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure Database for MariaDB documentation
      url: https://learn.microsoft.com/en-us/azure/mariadb/
  azure.subscription.mariaDbService.database:
    fields:
      charset: {}
      collation: {}
      id: {}
      name: {}
      type: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure Database for MariaDB documentation
      url: https://learn.microsoft.com/en-us/azure/mariadb/
  azure.subscription.mariaDbService.server:
    fields:
      configuration: {}
      databases: {}
      firewallRules: {}
      id: {}
      location: {}
      name: {}
      properties: {}
      tags: {}
      type: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure Database for MariaDB documentation
      url: https://learn.microsoft.com/en-us/azure/mariadb/
  azure.subscription.monitor:
    fields:
      applicationInsights: {}
      diagnosticSettings: {}
      logProfiles: {}
      subscriptionId: {}
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure Monitor documentation
      url: https://learn.microsoft.com/en-us/azure/azure-monitor/
  azure.subscription.monitor.activityLog:
    fields:
      alerts: {}
      subscriptionId: {}
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure Monitor documentation
      url: https://learn.microsoft.com/en-us/azure/azure-monitor/
  azure.subscription.monitor.activityLog.alert:
    fields:
      actions: {}
      conditions: {}
      description: {}
      id: {}
      location: {}
      name: {}
      scopes: {}
      tags: {}
      type: {}
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure Database for MariaDB documentation
      url: https://learn.microsoft.com/en-us/azure/azure-monitor/
  azure.subscription.monitor.applicationInsight:
    fields:
      id: {}
      kind: {}
      location: {}
      name: {}
      properties: {}
      tags: {}
      type: {}
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure Monitor documentation
      url: https://learn.microsoft.com/en-us/azure/azure-monitor/
  azure.subscription.monitor.diagnosticsetting:
    fields:
      id: {}
      name: {}
      properties: {}
      storageAccount: {}
      storageAccountId: {}
      type: {}
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure Monitor documentation
      url: https://learn.microsoft.com/en-us/azure/azure-monitor/
  azure.subscription.monitor.logprofile:
    fields:
      id: {}
      location: {}
      name: {}
      properties: {}
      storageAccount: {}
      storageAccountId: {}
      tags: {}
      type: {}
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure Monitor documentation
      url: https://learn.microsoft.com/en-us/azure/azure-monitor/
  azure.subscription.monitorService:
    fields:
      activityLog: {}
      applicationInsights: {}
      diagnosticSettings: {}
      logProfiles: {}
      subscriptionId: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure Monitor documentation
      url: https://learn.microsoft.com/en-us/azure/azure-monitor/
  azure.subscription.monitorService.activityLog:
    fields:
      alerts: {}
      subscriptionId: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure Monitor documentation
      url: https://learn.microsoft.com/en-us/azure/azure-monitor/
  azure.subscription.monitorService.activityLog.alert:
    fields:
      actions: {}
      conditions: {}
      description: {}
      id: {}
      location: {}
      name: {}
      scopes: {}
      tags: {}
      type: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure Monitor documentation
      url: https://learn.microsoft.com/en-us/azure/azure-monitor/
  azure.subscription.monitorService.applicationInsight:
    fields:
      id: {}
      kind: {}
      location: {}
      name: {}
      properties: {}
      tags: {}
      type: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure Monitor documentation
      url: https://learn.microsoft.com/en-us/azure/azure-monitor/
  azure.subscription.monitorService.diagnosticsetting:
    fields:
      id: {}
      name: {}
      properties: {}
      storageAccount: {}
      storageAccountId: {}
      type: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure Monitor documentation
      url: https://learn.microsoft.com/en-us/azure/azure-monitor/
  azure.subscription.monitorService.logprofile:
    fields:
      id: {}
      location: {}
      name: {}
      properties: {}
      storageAccount: {}
      storageAccountId: {}
      tags: {}
      type: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure Database for MariaDB documentation
      url: https://learn.microsoft.com/en-us/azure/azure-monitor/
  azure.subscription.mySql:
    fields:
      flexibleServers: {}
      servers: {}
      subscriptionId: {}
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure Database for MySQL - Flexible Server documentation
      url: https://learn.microsoft.com/en-us/azure/mysql/
  azure.subscription.mySql.database:
    fields:
      charset: {}
      collation: {}
      id: {}
      name: {}
      type: {}
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure Database for MySQL - Flexible Server documentation
      url: https://learn.microsoft.com/en-us/azure/mysql/
  azure.subscription.mySql.flexibleServer:
    fields:
      configuration: {}
      databases: {}
      firewallRules: {}
      id: {}
      location: {}
      name: {}
      properties: {}
      tags: {}
      type: {}
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure Database for MySQL - Flexible Server documentation
      url: https://learn.microsoft.com/en-us/azure/mysql/
  azure.subscription.mySql.server:
    fields:
      configuration: {}
      databases: {}
      firewallRules: {}
      id: {}
      location: {}
      name: {}
      properties: {}
      tags: {}
      type: {}
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure Database for MySQL - Flexible Server documentation
      url: https://learn.microsoft.com/en-us/azure/mysql/
  azure.subscription.mySqlService:
    fields:
      flexibleServers: {}
      servers: {}
      subscriptionId: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure Database for MySQL - Flexible Server documentation
      url: https://learn.microsoft.com/en-us/azure/mysql/
  azure.subscription.mySqlService.database:
    fields:
      charset: {}
      collation: {}
      id: {}
      name: {}
      type: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure Database for MySQL - Flexible Server documentation
      url: https://learn.microsoft.com/en-us/azure/mysql/
  azure.subscription.mySqlService.flexibleServer:
    fields:
      configuration: {}
      databases: {}
      firewallRules: {}
      id: {}
      location: {}
      name: {}
      properties: {}
      tags: {}
      type: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure Database for MySQL - Flexible Server documentation
      url: https://learn.microsoft.com/en-us/azure/mysql/
  azure.subscription.mySqlService.server:
    fields:
      configuration: {}
      databases: {}
      firewallRules: {}
      id: {}
      location: {}
      name: {}
      properties: {}
      tags: {}
      type: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure Database for MySQL - Flexible Server documentation
      url: https://learn.microsoft.com/en-us/azure/mysql/
  azure.subscription.network:
    fields:
      bastionHosts: {}
      interfaces: {}
      publicIpAddresses: {}
      securityGroups: {}
      subscriptionId: {}
      watchers: {}
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Virtual Network documentation
      url: https://learn.microsoft.com/en-us/azure/virtual-network/
  azure.subscription.network.bastionHost:
    fields:
      id: {}
      location: {}
      name: {}
      properties: {}
      sku: {}
      tags: {}
      type: {}
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Virtual Network documentation
      url: https://learn.microsoft.com/en-us/azure/virtual-network/
  azure.subscription.network.interface:
    fields:
      etag: {}
      id: {}
      location: {}
      name: {}
      properties: {}
      tags: {}
      type: {}
      vm: {}
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Virtual Network documentation
      url: https://learn.microsoft.com/en-us/azure/virtual-network/
  azure.subscription.network.ipAddress:
    fields:
      id: {}
      ipAddress: {}
      location: {}
      name: {}
      tags: {}
      type: {}
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Virtual Network documentation
      url: https://learn.microsoft.com/en-us/azure/virtual-network/
  azure.subscription.network.securityGroup:
    fields:
      defaultSecurityRules: {}
      etag: {}
      id: {}
      interfaces: {}
      location: {}
      name: {}
      properties: {}
      securityRules: {}
      tags: {}
      type: {}
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Virtual Network documentation
      url: https://learn.microsoft.com/en-us/azure/virtual-network/
  azure.subscription.network.securityrule:
    fields:
      destinationPortRange: {}
      etag: {}
      id: {}
      name: {}
      properties: {}
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Virtual Network documentation
      url: https://learn.microsoft.com/en-us/azure/virtual-network/
  azure.subscription.network.watcher:
    fields:
      etag: {}
      flowLogs: {}
      id: {}
      location: {}
      name: {}
      properties: {}
      provisioningState: {}
      tags: {}
      type: {}
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Virtual Network documentation
      url: https://learn.microsoft.com/en-us/azure/virtual-network/
  azure.subscription.network.watcher.flowlog:
    fields:
      analytics: {}
      enabled: {}
      etag: {}
      format: {}
      id: {}
      location: {}
      name: {}
      provisioningState: {}
      retentionPolicy: {}
      storageAccountId: {}
      tags: {}
      targetResourceGuid: {}
      targetResourceId: {}
      type: {}
      version: {}
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Virtual Network documentation
      url: https://learn.microsoft.com/en-us/azure/virtual-network/
  azure.subscription.networkService:
    fields:
      applicationFirewallPolicies: {}
      applicationGateways: {}
      applicationSecurityGroups:
        min_mondoo_version: 9.0.0
      appplicationFirewallPolicies: {}
      bastionHosts: {}
      firewallPolicies:
        min_mondoo_version: 9.0.0
      firewalls:
        min_mondoo_version: 9.0.0
      interfaces: {}
      loadBalancers:
        min_mondoo_version: 9.0.0
      natGateways:
        min_mondoo_version: 9.0.0
      publicIpAddresses: {}
      securityGroups: {}
      subscriptionId: {}
      virtualNetworkGateways:
        min_mondoo_version: 9.0.0
      virtualNetworks:
        min_mondoo_version: 9.0.0
      watchers: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Virtual Network documentation
      url: https://learn.microsoft.com/en-us/azure/virtual-network/
  azure.subscription.networkService.appSecurityGroup:
    fields:
      etag: {}
      id: {}
      location: {}
      name: {}
      properties: {}
      tags: {}
      type: {}
    min_mondoo_version: 9.0.0
    platform:
      name:
      - azure
    refs:
    - title: Virtual Network documentation
      url: https://learn.microsoft.com/en-us/azure/virtual-network/
  azure.subscription.networkService.applicationFirewallPolicy:
    fields:
      etag: {}
      gateways: {}
      id: {}
      location: {}
      name: {}
      properties: {}
      tags: {}
      type: {}
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Virtual Network documentation
      url: https://learn.microsoft.com/en-us/azure/virtual-network/
  azure.subscription.networkService.applicationGateway:
    fields:
      etag: {}
      id: {}
      location: {}
      name: {}
      policy: {}
      properties: {}
      tags: {}
      type: {}
      wafConfiguration:
        min_mondoo_version: 9.0.0
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Virtual Network documentation
      url: https://learn.microsoft.com/en-us/azure/virtual-network/
  azure.subscription.networkService.backendAddressPool:
    fields:
      etag: {}
      id: {}
      name: {}
      properties: {}
      type: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - azure
    refs:
    - title: Virtual Network documentation
      url: https://learn.microsoft.com/en-us/azure/virtual-network/
  azure.subscription.networkService.bastionHost:
    fields:
      id: {}
      location: {}
      name: {}
      properties: {}
      sku: {}
      tags: {}
      type: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Virtual Network documentation
      url: https://learn.microsoft.com/en-us/azure/virtual-network/
  azure.subscription.networkService.bgpSettings:
    fields:
      asn: {}
      bgpPeeringAddress: {}
      bgpPeeringAddressesConfig: {}
      id: {}
      peerWeight: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - azure
    refs:
    - title: Virtual Network documentation
      url: https://learn.microsoft.com/en-us/azure/virtual-network/
  azure.subscription.networkService.bgpSettings.ipConfigurationBgpPeeringAddress:
    fields:
      customBgpIpAddresses: {}
      defaultBgpIpAddresses: {}
      id: {}
      ipConfigurationId: {}
      tunnelIpAddresses: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - azure
    refs:
    - title: Virtual Network documentation
      url: https://learn.microsoft.com/en-us/azure/virtual-network/
  azure.subscription.networkService.firewall:
    fields:
      applicationRules: {}
      etag: {}
      id: {}
      ipConfigurations: {}
      location: {}
      managementIpConfiguration: {}
      name: {}
      natRules: {}
      networkRules: {}
      policy: {}
      properties: {}
      provisioningState: {}
      skuName: {}
      skuTier: {}
      tags: {}
      threatIntelMode: {}
      type: {}
    min_mondoo_version: 9.0.0
    platform:
      name:
      - azure
  azure.subscription.networkService.firewall.applicationRule:
    fields:
      etag: {}
      id: {}
      name: {}
      properties: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - azure
    refs:
    - title: Virtual Network documentation
      url: https://learn.microsoft.com/en-us/azure/virtual-network/
  azure.subscription.networkService.firewall.ipConfig:
    fields:
      etag: {}
      id: {}
      name: {}
      privateIpAddress: {}
      properties: {}
      publicIpAddress: {}
      subnet: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - azure
    refs:
    - title: Virtual Network documentation
      url: https://learn.microsoft.com/en-us/azure/virtual-network/
  azure.subscription.networkService.firewall.natRule:
    fields:
      etag: {}
      id: {}
      name: {}
      properties: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - azure
    refs:
    - title: Virtual Network documentation
      url: https://learn.microsoft.com/en-us/azure/virtual-network/
  azure.subscription.networkService.firewall.networkRule:
    fields:
      etag: {}
      id: {}
      name: {}
      properties: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - azure
    refs:
    - title: Virtual Network documentation
      url: https://learn.microsoft.com/en-us/azure/virtual-network/
  azure.subscription.networkService.firewallPolicy:
    fields:
      basePolicy: {}
      childPolicies: {}
      etag: {}
      firewalls: {}
      id: {}
      location: {}
      name: {}
      properties: {}
      provisioningState: {}
      tags: {}
      type: {}
    min_mondoo_version: 9.0.0
    platform:
      name:
      - azure
    refs:
    - title: Virtual Network documentation
      url: https://learn.microsoft.com/en-us/azure/virtual-network/
  azure.subscription.networkService.frontendIpConfig:
    fields:
      etag: {}
      id: {}
      name: {}
      properties: {}
      type: {}
      zones: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - azure
    refs:
    - title: Virtual Network documentation
      url: https://learn.microsoft.com/en-us/azure/virtual-network/
  azure.subscription.networkService.inboundNatPool:
    fields:
      etag: {}
      id: {}
      name: {}
      properties: {}
      type: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - azure
    refs:
    - title: Virtual Network documentation
      url: https://learn.microsoft.com/en-us/azure/virtual-network/
  azure.subscription.networkService.inboundNatRule:
    fields:
      etag: {}
      id: {}
      name: {}
      properties: {}
      type: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - azure
    refs:
    - title: Virtual Network documentation
      url: https://learn.microsoft.com/en-us/azure/virtual-network/
  azure.subscription.networkService.interface:
    fields:
      etag: {}
      id: {}
      location: {}
      name: {}
      properties: {}
      tags: {}
      type: {}
      vm: {}
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Virtual Network documentation
      url: https://learn.microsoft.com/en-us/azure/virtual-network/
  azure.subscription.networkService.ipAddress:
    fields:
      id: {}
      ipAddress: {}
      location: {}
      name: {}
      tags: {}
      type: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Virtual Network documentation
      url: https://learn.microsoft.com/en-us/azure/virtual-network/
  azure.subscription.networkService.loadBalancer:
    fields:
      backendPools: {}
      etag: {}
      frontendIpConfigs: {}
      id: {}
      inboundNatPools: {}
      inboundNatRules: {}
      loadBalancerRules: {}
      location: {}
      name: {}
      outboundRules: {}
      probes: {}
      properties: {}
      sku: {}
      tags: {}
      type: {}
    min_mondoo_version: 9.0.0
    platform:
      name:
      - azure
    refs:
    - title: Load Balancer documentation
      url: https://learn.microsoft.com/en-us/azure/load-balancer/
  azure.subscription.networkService.loadBalancerRule:
    fields:
      etag: {}
      id: {}
      name: {}
      properties: {}
      type: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - azure
    refs:
    - title: Load Balancer documentation
      url: https://learn.microsoft.com/en-us/azure/load-balancer/
  azure.subscription.networkService.natGateway:
    fields:
      etag: {}
      id: {}
      location: {}
      name: {}
      properties: {}
      publicIpAddresses: {}
      subnets: {}
      tags: {}
      type: {}
      zones: {}
    min_mondoo_version: 9.0.0
    platform:
      name:
      - azure
    refs:
    - title: Azure NAT Gateway documentation
      url: https://learn.microsoft.com/en-us/azure/nat-gateway/
  azure.subscription.networkService.outboundRule:
    fields:
      etag: {}
      id: {}
      name: {}
      properties: {}
      type: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - azure
    refs:
    - title: Virtual Network documentation
      url: https://learn.microsoft.com/en-us/azure/virtual-network/
  azure.subscription.networkService.probe:
    fields:
      etag: {}
      id: {}
      name: {}
      properties: {}
      type: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - azure
    refs:
    - title: Virtual Network documentation
      url: https://learn.microsoft.com/en-us/azure/virtual-network/
  azure.subscription.networkService.securityGroup:
    fields:
      defaultSecurityRules: {}
      etag: {}
      id: {}
      interfaces: {}
      location: {}
      name: {}
      properties: {}
      securityRules: {}
      tags: {}
      type: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Virtual Network documentation
      url: https://learn.microsoft.com/en-us/azure/virtual-network/
  azure.subscription.networkService.securityrule:
    fields:
      destinationPortRange: {}
      direction: {}
      etag: {}
      id: {}
      name: {}
      properties: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Virtual Network documentation
      url: https://learn.microsoft.com/en-us/azure/virtual-network/
  azure.subscription.networkService.subnet:
    fields:
      addressPrefix: {}
      etag: {}
      id: {}
      ipConfigurations:
        min_mondoo_version: latest
      name: {}
      natGateway: {}
      properties: {}
      type: {}
    min_mondoo_version: 9.0.0
    platform:
      name:
      - azure
    refs:
    - title: Virtual Network documentation
      url: https://learn.microsoft.com/en-us/azure/virtual-network/
  azure.subscription.networkService.subnet.ipConfig:
    fields:
      etag: {}
      id: {}
      name: {}
      privateIpAddress: {}
      properties: {}
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Virtual Network documentation
      url: https://learn.microsoft.com/en-us/azure/virtual-network/
  azure.subscription.networkService.virtualNetwork:
    fields:
      dhcpOptions:
        min_mondoo_version: latest
      enableDdosProtection:
        min_mondoo_version: latest
      enableVmProtection:
        min_mondoo_version: latest
      etag: {}
      id: {}
      location: {}
      name: {}
      properties: {}
      subnets: {}
      tags: {}
      type: {}
    min_mondoo_version: 9.0.0
    platform:
      name:
      - azure
    refs:
    - title: Virtual Network documentation
      url: https://learn.microsoft.com/en-us/azure/virtual-network/
  azure.subscription.networkService.virtualNetwork.dhcpOptions:
    fields:
      dnsServers: {}
      id: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - azure
  azure.subscription.networkService.virtualNetworkGateway:
    fields:
      active: {}
      addressPrefixes: {}
      bgpSettings: {}
      connections: {}
      disableIPSecReplayProtection: {}
      enableBgp: {}
      enableBgpRouteTranslationForNat: {}
      enableDNSForwarding: {}
      enablePrivateIPAddress: {}
      etag: {}
      gatewayType: {}
      id: {}
      inboundDNSForwardingEndpoint: {}
      ipConfigurations: {}
      location: {}
      name: {}
      natRules: {}
      properties: {}
      provisioningState: {}
      skuCapacity: {}
      skuName: {}
      tags: {}
      type: {}
      vpnClientConfiguration: {}
      vpnGatewayGeneration: {}
      vpnType: {}
    min_mondoo_version: 9.0.0
    platform:
      name:
      - azure
    refs:
    - title: Virtual Network documentation
      url: https://learn.microsoft.com/en-us/azure/virtual-network/
  azure.subscription.networkService.virtualNetworkGateway.connection:
    fields:
      etag: {}
      id: {}
      name: {}
      properties: {}
      type: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - azure
    refs:
    - title: Virtual Network documentation
      url: https://learn.microsoft.com/en-us/azure/virtual-network/
  azure.subscription.networkService.virtualNetworkGateway.ipConfig:
    fields:
      etag: {}
      id: {}
      name: {}
      privateIpAddress: {}
      properties: {}
      publicIpAddress:
        min_mondoo_version: latest
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - azure
    refs:
    - title: Virtual Network documentation
      url: https://learn.microsoft.com/en-us/azure/virtual-network/
  azure.subscription.networkService.wafConfig:
    fields:
      id: {}
      kind: {}
      name: {}
      properties: {}
      type: {}
    min_mondoo_version: 9.0.0
    platform:
      name:
      - azure
  azure.subscription.networkService.watcher:
    fields:
      etag: {}
      flowLogs: {}
      id: {}
      location: {}
      name: {}
      properties: {}
      provisioningState: {}
      tags: {}
      type: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Virtual Network documentation
      url: https://learn.microsoft.com/en-us/azure/virtual-network/
  azure.subscription.networkService.watcher.flowlog:
    fields:
      analytics: {}
      enabled: {}
      etag: {}
      format: {}
      id: {}
      location: {}
      name: {}
      provisioningState: {}
      retentionPolicy: {}
      storageAccountId: {}
      tags: {}
      targetResourceGuid: {}
      targetResourceId: {}
      type: {}
      version: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Virtual Network documentation
      url: https://learn.microsoft.com/en-us/azure/virtual-network/
  azure.subscription.policy:
    fields:
      assignments: {}
      subscriptionId: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - azure
  azure.subscription.policy.assignment:
    fields:
      description: {}
      enforcementMode: {}
      id: {}
      name: {}
      parameters: {}
      scope: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - azure
  azure.subscription.postgreSql:
    fields:
      servers: {}
      subscriptionId: {}
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure Database for PostgreSQL - Flexible Server documentation
      url: https://learn.microsoft.com/en-us/azure/postgresql/
  azure.subscription.postgreSql.database:
    fields:
      charset: {}
      collation: {}
      id: {}
      name: {}
      type: {}
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure Database for PostgreSQL - Flexible Server documentation
      url: https://learn.microsoft.com/en-us/azure/postgresql/
  azure.subscription.postgreSql.server:
    fields:
      configuration: {}
      databases: {}
      firewallRules: {}
      id: {}
      location: {}
      name: {}
      properties: {}
      tags: {}
      type: {}
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure Database for PostgreSQL - Flexible Server documentation
      url: https://learn.microsoft.com/en-us/azure/postgresql/
  azure.subscription.postgreSqlService:
    fields:
      flexibleServers: {}
      servers: {}
      subscriptionId: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - azure
  azure.subscription.postgreSqlService.database:
    fields:
      charset: {}
      collation: {}
      id: {}
      name: {}
      type: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure Database for PostgreSQL - Flexible Server documentation
      url: https://learn.microsoft.com/en-us/azure/postgresql/
  azure.subscription.postgreSqlService.flexibleServer:
    fields:
      configuration: {}
      databases: {}
      firewallRules: {}
      id: {}
      location: {}
      name: {}
      properties: {}
      tags: {}
      type: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - azure
  azure.subscription.postgreSqlService.server:
    fields:
      configuration: {}
      databases: {}
      firewallRules: {}
      id: {}
      location: {}
      name: {}
      properties: {}
      tags: {}
      type: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure Database for PostgreSQL - Flexible Server documentation
      url: https://learn.microsoft.com/en-us/azure/postgresql/
  azure.subscription.resource:
    fields:
      changedTime: {}
      createdTime: {}
      id: {}
      identity: {}
      kind: {}
      location: {}
      managedBy: {}
      name: {}
      plan: {}
      provisioningState: {}
      sku: {}
      tags: {}
      type: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure Resource Manager documentation
      url: https://learn.microsoft.com/en-us/azure/azure-resource-manager/
  azure.subscription.resourcegroup:
    fields:
      id: {}
      location: {}
      managedBy: {}
      name: {}
      provisioningState: {}
      tags: {}
      type: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure Resource Manager documentation
      url: https://learn.microsoft.com/en-us/azure/azure-resource-manager/
  azure.subscription.sql:
    fields:
      servers: {}
      subscriptionId: {}
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure SQL documentation
      url: https://learn.microsoft.com/en-us/azure/azure-sql/
  azure.subscription.sql.configuration:
    fields:
      allowedValues: {}
      dataType: {}
      defaultValue: {}
      description: {}
      id: {}
      name: {}
      source: {}
      type: {}
      value: {}
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure SQL documentation
      url: https://learn.microsoft.com/en-us/azure/azure-sql/
  azure.subscription.sql.database:
    fields:
      advisor: {}
      auditingPolicy: {}
      collation: {}
      connectionPolicy: {}
      createMode: {}
      creationDate: {}
      databaseId: {}
      defaultSecondaryLocation: {}
      earliestRestoreDate: {}
      edition: {}
      elasticPoolName: {}
      failoverGroupId: {}
      id: {}
      maxSizeBytes: {}
      name: {}
      readScale: {}
      recoveryServicesRecoveryPointResourceId: {}
      requestedServiceObjectiveName: {}
      restorePointInTime: {}
      sampleName: {}
      serviceLevelObjective: {}
      sourceDatabaseDeletionDate: {}
      sourceDatabaseId: {}
      status: {}
      threatDetectionPolicy: {}
      transparentDataEncryption: {}
      type: {}
      usage: {}
      zoneRedundant: {}
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure SQL documentation
      url: https://learn.microsoft.com/en-us/azure/azure-sql/
  azure.subscription.sql.databaseusage:
    fields:
      currentValue: {}
      displayName: {}
      id: {}
      limit: {}
      name: {}
      resourceName: {}
      unit: {}
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure SQL documentation
      url: https://learn.microsoft.com/en-us/azure/azure-sql/
  azure.subscription.sql.firewallrule:
    fields:
      endIpAddress: {}
      id: {}
      name: {}
      startIpAddress: {}
      type: {}
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure SQL documentation
      url: https://learn.microsoft.com/en-us/azure/azure-sql/
  azure.subscription.sql.server:
    fields:
      auditingPolicy: {}
      azureAdAdministrators: {}
      connectionPolicy: {}
      databases: {}
      encryptionProtector: {}
      firewallRules: {}
      id: {}
      location: {}
      name: {}
      properties: {}
      securityAlertPolicy: {}
      tags: {}
      threatDetectionPolicy: {}
      type: {}
      virtualNetworkRules: {}
      vulnerabilityAssessmentSettings: {}
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure SQL documentation
      url: https://learn.microsoft.com/en-us/azure/azure-sql/
  azure.subscription.sql.server.administrator:
    fields:
      administratorType: {}
      id: {}
      login: {}
      name: {}
      sid: {}
      tenantId: {}
      type: {}
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure SQL documentation
      url: https://learn.microsoft.com/en-us/azure/azure-sql/
  azure.subscription.sql.server.vulnerabilityassessmentsettings:
    fields:
      id: {}
      mailSubscriptionAdmins: {}
      name: {}
      recurringScanEmails: {}
      recurringScanEnabled: {}
      storageAccountAccessKey: {}
      storageContainerPath: {}
      storageContainerSasKey: {}
      type: {}
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure SQL documentation
      url: https://learn.microsoft.com/en-us/azure/azure-sql/
  azure.subscription.sql.virtualNetworkRule:
    fields:
      id: {}
      name: {}
      properties: {}
      type: {}
      virtualNetworkSubnetId: {}
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure SQL documentation
      url: https://learn.microsoft.com/en-us/azure/azure-sql/
  azure.subscription.sqlService:
    fields:
      servers: {}
      subscriptionId: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure SQL documentation
      url: https://learn.microsoft.com/en-us/azure/azure-sql/
  azure.subscription.sqlService.configuration:
    fields:
      allowedValues: {}
      dataType: {}
      defaultValue: {}
      description: {}
      id: {}
      name: {}
      source: {}
      type: {}
      value: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure SQL documentation
      url: https://learn.microsoft.com/en-us/azure/azure-sql/
  azure.subscription.sqlService.database:
    fields:
      advisor: {}
      auditingPolicy: {}
      collation: {}
      connectionPolicy: {}
      createMode: {}
      creationDate: {}
      databaseId: {}
      defaultSecondaryLocation: {}
      earliestRestoreDate: {}
      edition: {}
      elasticPoolName: {}
      failoverGroupId: {}
      id: {}
      maxSizeBytes: {}
      name: {}
      readScale: {}
      recoveryServicesRecoveryPointResourceId: {}
      requestedServiceObjectiveName: {}
      restorePointInTime: {}
      sampleName: {}
      serviceLevelObjective: {}
      sourceDatabaseDeletionDate: {}
      sourceDatabaseId: {}
      status: {}
      threatDetectionPolicy: {}
      transparentDataEncryption: {}
      type: {}
      usage: {}
      zoneRedundant: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure SQL documentation
      url: https://learn.microsoft.com/en-us/azure/azure-sql/
  azure.subscription.sqlService.databaseusage:
    fields:
      currentValue: {}
      displayName: {}
      id: {}
      limit: {}
      name: {}
      resourceName: {}
      unit: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure SQL documentation
      url: https://learn.microsoft.com/en-us/azure/azure-sql/
  azure.subscription.sqlService.firewallrule:
    fields:
      endIpAddress: {}
      id: {}
      name: {}
      startIpAddress: {}
      type: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure SQL documentation
      url: https://learn.microsoft.com/en-us/azure/azure-sql/
  azure.subscription.sqlService.server:
    fields:
      auditingPolicy: {}
      azureAdAdministrators: {}
      connectionPolicy: {}
      databases: {}
      encryptionProtector: {}
      firewallRules: {}
      id: {}
      location: {}
      name: {}
      properties: {}
      securityAlertPolicy: {}
      tags: {}
      threatDetectionPolicy: {}
      type: {}
      virtualNetworkRules: {}
      vulnerabilityAssessmentSettings: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure SQL documentation
      url: https://learn.microsoft.com/en-us/azure/azure-sql/
  azure.subscription.sqlService.server.administrator:
    fields:
      administratorType: {}
      id: {}
      login: {}
      name: {}
      sid: {}
      tenantId: {}
      type: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure SQL documentation
      url: https://learn.microsoft.com/en-us/azure/azure-sql/
  azure.subscription.sqlService.server.vulnerabilityassessmentsettings:
    fields:
      id: {}
      mailSubscriptionAdmins: {}
      name: {}
      recurringScanEmails: {}
      recurringScanEnabled: {}
      storageAccountAccessKey: {}
      storageContainerPath: {}
      storageContainerSasKey: {}
      type: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure SQL documentation
      url: https://learn.microsoft.com/en-us/azure/azure-sql/
  azure.subscription.sqlService.virtualNetworkRule:
    fields:
      id: {}
      name: {}
      properties: {}
      type: {}
      virtualNetworkSubnetId: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure SQL documentation
      url: https://learn.microsoft.com/en-us/azure/azure-sql/
  azure.subscription.storage:
    fields:
      accounts: {}
      subscriptionId: {}
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure Storage documentation
      url: https://learn.microsoft.com/en-us/azure/storage/
  azure.subscription.storage.account:
    fields:
      blobProperties: {}
      containers: {}
      dataProtection: {}
      id: {}
      identity: {}
      kind: {}
      location: {}
      name: {}
      properties: {}
      queueProperties: {}
      sku: {}
      tableProperties: {}
      tags: {}
      type: {}
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure Storage documentation
      url: https://learn.microsoft.com/en-us/azure/storage/
  azure.subscription.storage.account.container:
    fields:
      etag: {}
      id: {}
      name: {}
      properties: {}
      type: {}
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure Storage documentation
      url: https://learn.microsoft.com/en-us/azure/storage/
  azure.subscription.storage.account.dataProtection:
    fields:
      blobRetentionDays: {}
      blobSoftDeletionEnabled: {}
      containerRetentionDays: {}
      containerSoftDeletionEnabled: {}
      storageAccountId: {}
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure Storage documentation
      url: https://learn.microsoft.com/en-us/azure/storage/
  azure.subscription.storage.account.service.properties:
    fields:
      hourMetrics: {}
      id: {}
      logging: {}
      minuteMetrics: {}
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure Storage documentation
      url: https://learn.microsoft.com/en-us/azure/storage/
  azure.subscription.storage.account.service.properties.logging:
    fields:
      delete: {}
      id: {}
      read: {}
      retentionPolicy: {}
      version: {}
      write: {}
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure Storage documentation
      url: https://learn.microsoft.com/en-us/azure/storage/
  azure.subscription.storage.account.service.properties.metrics:
    fields:
      enabled: {}
      id: {}
      includeAPIs: {}
      retentionPolicy: {}
      version: {}
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure Storage documentation
      url: https://learn.microsoft.com/en-us/azure/storage/
  azure.subscription.storage.account.service.properties.retentionPolicy:
    fields:
      enabled: {}
      id: {}
      retentionDays: {}
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure Storage documentation
      url: https://learn.microsoft.com/en-us/azure/storage/
  azure.subscription.storageService:
    fields:
      accounts: {}
      subscriptionId: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure Storage documentation
      url: https://learn.microsoft.com/en-us/azure/storage/
  azure.subscription.storageService.account:
    fields:
      blobProperties: {}
      containers: {}
      dataProtection: {}
      id: {}
      identity: {}
      kind: {}
      location: {}
      name: {}
      properties: {}
      queueProperties: {}
      sku: {}
      tableProperties: {}
      tags: {}
      type: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure Storage documentation
      url: https://learn.microsoft.com/en-us/azure/storage/
  azure.subscription.storageService.account.container:
    fields:
      etag: {}
      id: {}
      name: {}
      properties: {}
      type: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure Storage documentation
      url: https://learn.microsoft.com/en-us/azure/storage/
  azure.subscription.storageService.account.dataProtection:
    fields:
      blobRetentionDays: {}
      blobSoftDeletionEnabled: {}
      containerRetentionDays: {}
      containerSoftDeletionEnabled: {}
      storageAccountId: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure Storage documentation
      url: https://learn.microsoft.com/en-us/azure/storage/
  azure.subscription.storageService.account.service.properties:
    fields:
      hourMetrics: {}
      id: {}
      logging: {}
      minuteMetrics: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - azure
  azure.subscription.storageService.account.service.properties.logging:
    fields:
      delete: {}
      id: {}
      read: {}
      retentionPolicy: {}
      version: {}
      write: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure Storage documentation
      url: https://learn.microsoft.com/en-us/azure/storage/
  azure.subscription.storageService.account.service.properties.metrics:
    fields:
      enabled: {}
      id: {}
      includeAPIs: {}
      retentionPolicy: {}
      version: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure Storage documentation
      url: https://learn.microsoft.com/en-us/azure/storage/
  azure.subscription.storageService.account.service.properties.retentionPolicy:
    fields:
      enabled: {}
      id: {}
      retentionDays: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure Storage documentation
      url: https://learn.microsoft.com/en-us/azure/storage/
  azure.subscription.web:
    fields:
      apps: {}
      availableRuntimes: {}
      subscriptionId: {}
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure Web documentation
      url: https://learn.microsoft.com/en-us/azure/?product=web
  azure.subscription.web.appsite:
    fields:
      applicationSettings: {}
      authenticationSettings: {}
      configuration: {}
      connectionSettings: {}
      id: {}
      identity: {}
      kind: {}
      location: {}
      metadata: {}
      name: {}
      properties: {}
      stack: {}
      tags: {}
      type: {}
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure Web documentation
      url: https://learn.microsoft.com/en-us/azure/?product=web
  azure.subscription.web.appsiteauthsettings:
    fields:
      id: {}
      kind: {}
      name: {}
      properties: {}
      type: {}
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure Web documentation
      url: https://learn.microsoft.com/en-us/azure/?product=web
  azure.subscription.web.appsiteconfig:
    fields:
      id: {}
      kind: {}
      name: {}
      properties: {}
      type: {}
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure Web documentation
      url: https://learn.microsoft.com/en-us/azure/?product=web
  azure.subscription.webService:
    fields:
      apps: {}
      availableRuntimes: {}
      subscriptionId: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure Web documentation
      url: https://learn.microsoft.com/en-us/azure/?product=web
  azure.subscription.webService.appRuntimeStack:
    fields:
      autoUpdate: {}
      deprecated: {}
      endOfLifeDate: {}
      hidden: {}
      majorVersion: {}
      minorVersion: {}
      name: {}
      preferredOs: {}
      runtimeVersion: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - azure
  azure.subscription.webService.appsite:
    fields:
      applicationSettings: {}
      authenticationSettings: {}
      configuration: {}
      connectionSettings: {}
      diagnosticSettings:
        min_mondoo_version: 9.0.0
      ftp:
        min_mondoo_version: 9.0.0
      functions:
        min_mondoo_version: 9.0.0
      id: {}
      identity: {}
      kind: {}
      location: {}
      metadata: {}
      name: {}
      properties: {}
      scm:
        min_mondoo_version: 9.0.0
      stack: {}
      tags: {}
      type: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure Web documentation
      url: https://learn.microsoft.com/en-us/azure/?product=web
  azure.subscription.webService.appsite.basicPublishingCredentialsPolicies:
    fields:
      allow: {}
      id: {}
      name: {}
      type: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - azure
  azure.subscription.webService.appsiteauthsettings:
    fields:
      id: {}
      kind: {}
      name: {}
      properties: {}
      type: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure Web documentation
      url: https://learn.microsoft.com/en-us/azure/?product=web
  azure.subscription.webService.appsiteconfig:
    fields:
      id: {}
      kind: {}
      name: {}
      properties: {}
      type: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - azure
    refs:
    - title: Azure Web documentation
      url: https://learn.microsoft.com/en-us/azure/?product=web
  azure.subscription.webService.function:
    fields:
      id: {}
      kind: {}
      name: {}
      properties: {}
      type: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - azure
