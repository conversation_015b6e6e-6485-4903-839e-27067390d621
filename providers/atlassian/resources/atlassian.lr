// Copyright (c) Mondoo, Inc.
// SPDX-License-Identifier: BUSL-1.1

option provider = "go.mondoo.com/cnquery/v9/providers/atlassian"
option go_package = "go.mondoo.com/cnquery/providers/atlassian/resources"

// Cross-domain Identity Management (SCIM)
atlassian.scim {
  // SCIM users
  users() []atlassian.scim.user
  // SCIM groups
  groups() []atlassian.scim.group
}

// SCIM user
atlassian.scim.user @defaults("displayName") {
  // User ID
  id string
  // User name
  name string
  // User's display name
  displayName string
  // Organization the user belongs to
  organization string
  // User's title
  title string
}

// SCIM group
atlassian.scim.group @defaults("name") {
  // Group ID
  id string
  // Group name
  name string
}

// Organization
atlassian.admin.organization @defaults("name") {
  // Organization ID
  id string
  // Name
  name string
  // Organization type
  type string
  // Policies
  policies() []atlassian.admin.organization.policy
  // Domains
  domains() []atlassian.admin.organization.domain
  // Managed users
  managedUsers() []atlassian.admin.organization.managedUser
}

// Managed users
atlassian.admin.organization.managedUser @defaults("name") {
  // ID
  id string
  // Name
  name string
  // Type
  type string
  // Email
  email string
  // Status
  status string
  // Last active
  lastActive time
  // Product access
  productAccess []dict
}

// Policies (except authentication policies)
atlassian.admin.organization.policy @defaults("name") {
  // Policy ID
  id string
  // Object type (always "policy")
  type string
  // Policy name
  name string
  // Type of policy
  policyType string
  // Status
  status string
}

// Domains
atlassian.admin.organization.domain @defaults("name") {
  // Domain ID
  id string
  // Name
  name string
  // Type
  type string
}

// Jira
atlassian.jira {
  // Jira users
  users() []atlassian.jira.user
  // Jira projects
  projects() []atlassian.jira.project
  // Jira issues
  issues() []atlassian.jira.issue
  // Jira groups
  groups() []atlassian.jira.group
  // Jira server info
  serverInfos() atlassian.jira.serverInfo
}

// Jira issue
atlassian.jira.issue @defaults("id createdAt") {
  // Issue ID
  id string
  // Project
  project string
  // Project key
  projectKey string
  // Status
  status string
  // Description
  description string
  // Issue create time in UTC
  createdAt time
  // Issue creator
  creator atlassian.jira.user
  // Issue type name
  typeName string
}

// Jira server info
atlassian.jira.serverInfo @defaults("serverTitle") {
  // BaseUrl
  baseUrl string
  // Build number
  buildNumber int
  // Server title
  serverTitle string
  // Deployment type
  deploymentType string
}

// Jira user
atlassian.jira.user @defaults("name") {
  // Account ID
  id string
  // Name
  name string
  // Account type
  type string
  // Picture of the user
  picture string
  // Groups
  groups() []atlassian.jira.group
  // Roles
  applicationRoles() []atlassian.jira.applicationRole
}

// Jira application role
atlassian.jira.applicationRole @defaults("name") {
  // Role ID
  id string
  // Role name
  name string
}

// Jira project
atlassian.jira.project @defaults("name") {
  // Project ID
  id string
  // Project name
  name string
  // UUID
  uuid string
  // Key
  key string
  // URL
  url string
  // Email
  email string
  // Whether the group is private
  private bool
  // Whether the group has been deleted
  deleted bool
  // Whether the group has been archived
  archived bool
  // Properties
  properties() []atlassian.jira.project.property
}

// Jira project property
atlassian.jira.project.property @defaults("id") {
  // Property key
  id string
}

// Jira group
atlassian.jira.group @defaults("name") {
  // Group ID
  id string
  // Group name
  name string
}

// Confluence
atlassian.confluence {
  // Confluence users
  users() []atlassian.confluence.user
}

// Confluence user
atlassian.confluence.user @defaults("name") {
  // Account ID
  id string
  // Account name
  name string
  // Account type
  type string
}
