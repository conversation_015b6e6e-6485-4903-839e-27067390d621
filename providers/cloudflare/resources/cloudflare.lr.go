// Copyright (c) Mondoo, Inc.
// SPDX-License-Identifier: BUSL-1.1

// Code generated by resources. DO NOT EDIT.

package resources

import (
	"errors"
	"time"

	"go.mondoo.com/cnquery/v11/llx"
	"go.mondoo.com/cnquery/v11/providers-sdk/v1/plugin"
	"go.mondoo.com/cnquery/v11/types"
)

var resourceFactories map[string]plugin.ResourceFactory

func init() {
	resourceFactories = map[string]plugin.ResourceFactory {
		"cloudflare": {
			// to override args, implement: initCloudflare(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createCloudflare,
		},
		"cloudflare.zone": {
			Init: initCloudflareZone,
			Create: createCloudflareZone,
		},
		"cloudflare.zone.account": {
			// to override args, implement: initCloudflareZoneAccount(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createCloudflareZoneAccount,
		},
		"cloudflare.dns": {
			// to override args, implement: initCloudflareDns(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createCloudflareDns,
		},
		"cloudflare.dns.record": {
			// to override args, implement: initCloudflareDnsRecord(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createCloudflareDnsRecord,
		},
		"cloudflare.account": {
			Init: initCloudflareAccount,
			Create: createCloudflareAccount,
		},
		"cloudflare.account.settings": {
			// to override args, implement: initCloudflareAccountSettings(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createCloudflareAccountSettings,
		},
		"cloudflare.streams": {
			// to override args, implement: initCloudflareStreams(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createCloudflareStreams,
		},
		"cloudflare.streams.liveInput": {
			// to override args, implement: initCloudflareStreamsLiveInput(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createCloudflareStreamsLiveInput,
		},
		"cloudflare.streams.video": {
			// to override args, implement: initCloudflareStreamsVideo(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createCloudflareStreamsVideo,
		},
		"cloudflare.r2": {
			// to override args, implement: initCloudflareR2(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createCloudflareR2,
		},
		"cloudflare.r2.bucket": {
			// to override args, implement: initCloudflareR2Bucket(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createCloudflareR2Bucket,
		},
		"cloudflare.workers": {
			// to override args, implement: initCloudflareWorkers(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createCloudflareWorkers,
		},
		"cloudflare.workers.worker": {
			// to override args, implement: initCloudflareWorkersWorker(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createCloudflareWorkersWorker,
		},
		"cloudflare.workers.page": {
			// to override args, implement: initCloudflareWorkersPage(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createCloudflareWorkersPage,
		},
		"cloudflare.one": {
			// to override args, implement: initCloudflareOne(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createCloudflareOne,
		},
		"cloudflare.one.app": {
			// to override args, implement: initCloudflareOneApp(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createCloudflareOneApp,
		},
		"cloudflare.corsHeaders": {
			// to override args, implement: initCloudflareCorsHeaders(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createCloudflareCorsHeaders,
		},
		"cloudflare.one.idp": {
			// to override args, implement: initCloudflareOneIdp(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createCloudflareOneIdp,
		},
	}
}

// NewResource is used by the runtime of this plugin to create new resources.
// Its arguments may be provided by users. This function is generally not
// used by initializing resources from recordings or from lists.
func NewResource(runtime *plugin.Runtime, name string, args map[string]*llx.RawData) (plugin.Resource, error) {
	f, ok := resourceFactories[name]
	if !ok {
		return nil, errors.New("cannot find resource " + name + " in this provider")
	}

	if f.Init != nil {
		cargs, res, err := f.Init(runtime, args)
		if err != nil {
			return res, err
		}

		if res != nil {
			id := name+"\x00"+res.MqlID()
			if x, ok := runtime.Resources.Get(id); ok {
				return x, nil
			}
			runtime.Resources.Set(id, res)
			return res, nil
		}

		args = cargs
	}

	res, err := f.Create(runtime, args)
	if err != nil {
		return nil, err
	}

	id := name+"\x00"+res.MqlID()
	if x, ok := runtime.Resources.Get(id); ok {
		return x, nil
	}

	runtime.Resources.Set(id, res)
	return res, nil
}

// CreateResource is used by the runtime of this plugin to create resources.
// Its arguments must be complete and pre-processed. This method is used
// for initializing resources from recordings or from lists.
func CreateResource(runtime *plugin.Runtime, name string, args map[string]*llx.RawData) (plugin.Resource, error) {
	f, ok := resourceFactories[name]
	if !ok {
		return nil, errors.New("cannot find resource " + name + " in this provider")
	}

	res, err := f.Create(runtime, args)
	if err != nil {
		return nil, err
	}

	id := name+"\x00"+res.MqlID()
	if x, ok := runtime.Resources.Get(id); ok {
		return x, nil
	}

	runtime.Resources.Set(id, res)
	return res, nil
}

var getDataFields = map[string]func(r plugin.Resource) *plugin.DataRes{
	"cloudflare.zones": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflare).GetZones()).ToDataRes(types.Array(types.Resource("cloudflare.zone")))
	},
	"cloudflare.accounts": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflare).GetAccounts()).ToDataRes(types.Array(types.Resource("cloudflare.account")))
	},
	"cloudflare.zone.id": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareZone).GetId()).ToDataRes(types.String)
	},
	"cloudflare.zone.name": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareZone).GetName()).ToDataRes(types.String)
	},
	"cloudflare.zone.nameServers": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareZone).GetNameServers()).ToDataRes(types.Array(types.String))
	},
	"cloudflare.zone.originalNameServers": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareZone).GetOriginalNameServers()).ToDataRes(types.Array(types.String))
	},
	"cloudflare.zone.status": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareZone).GetStatus()).ToDataRes(types.String)
	},
	"cloudflare.zone.paused": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareZone).GetPaused()).ToDataRes(types.Bool)
	},
	"cloudflare.zone.type": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareZone).GetType()).ToDataRes(types.String)
	},
	"cloudflare.zone.createdOn": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareZone).GetCreatedOn()).ToDataRes(types.Time)
	},
	"cloudflare.zone.modifiedOn": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareZone).GetModifiedOn()).ToDataRes(types.Time)
	},
	"cloudflare.zone.account": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareZone).GetAccount()).ToDataRes(types.Resource("cloudflare.zone.account"))
	},
	"cloudflare.zone.dns": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareZone).GetDns()).ToDataRes(types.Resource("cloudflare.dns"))
	},
	"cloudflare.zone.liveInputs": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareZone).GetLiveInputs()).ToDataRes(types.Array(types.Resource("cloudflare.streams.liveInput")))
	},
	"cloudflare.zone.videos": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareZone).GetVideos()).ToDataRes(types.Array(types.Resource("cloudflare.streams.video")))
	},
	"cloudflare.zone.r2": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareZone).GetR2()).ToDataRes(types.Resource("cloudflare.r2"))
	},
	"cloudflare.zone.workers": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareZone).GetWorkers()).ToDataRes(types.Resource("cloudflare.workers"))
	},
	"cloudflare.zone.one": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareZone).GetOne()).ToDataRes(types.Resource("cloudflare.one"))
	},
	"cloudflare.zone.account.id": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareZoneAccount).GetId()).ToDataRes(types.String)
	},
	"cloudflare.zone.account.name": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareZoneAccount).GetName()).ToDataRes(types.String)
	},
	"cloudflare.zone.account.type": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareZoneAccount).GetType()).ToDataRes(types.String)
	},
	"cloudflare.zone.account.email": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareZoneAccount).GetEmail()).ToDataRes(types.String)
	},
	"cloudflare.dns.records": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareDns).GetRecords()).ToDataRes(types.Array(types.Resource("cloudflare.dns.record")))
	},
	"cloudflare.dns.record.id": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareDnsRecord).GetId()).ToDataRes(types.String)
	},
	"cloudflare.dns.record.name": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareDnsRecord).GetName()).ToDataRes(types.String)
	},
	"cloudflare.dns.record.comment": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareDnsRecord).GetComment()).ToDataRes(types.String)
	},
	"cloudflare.dns.record.tags": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareDnsRecord).GetTags()).ToDataRes(types.Array(types.String))
	},
	"cloudflare.dns.record.proxied": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareDnsRecord).GetProxied()).ToDataRes(types.Bool)
	},
	"cloudflare.dns.record.proxiable": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareDnsRecord).GetProxiable()).ToDataRes(types.Bool)
	},
	"cloudflare.dns.record.type": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareDnsRecord).GetType()).ToDataRes(types.String)
	},
	"cloudflare.dns.record.content": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareDnsRecord).GetContent()).ToDataRes(types.String)
	},
	"cloudflare.dns.record.ttl": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareDnsRecord).GetTtl()).ToDataRes(types.Int)
	},
	"cloudflare.dns.record.createdOn": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareDnsRecord).GetCreatedOn()).ToDataRes(types.Time)
	},
	"cloudflare.dns.record.modifiedOn": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareDnsRecord).GetModifiedOn()).ToDataRes(types.Time)
	},
	"cloudflare.account.id": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareAccount).GetId()).ToDataRes(types.String)
	},
	"cloudflare.account.name": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareAccount).GetName()).ToDataRes(types.String)
	},
	"cloudflare.account.settings": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareAccount).GetSettings()).ToDataRes(types.Resource("cloudflare.account.settings"))
	},
	"cloudflare.account.createdOn": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareAccount).GetCreatedOn()).ToDataRes(types.Time)
	},
	"cloudflare.account.liveInputs": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareAccount).GetLiveInputs()).ToDataRes(types.Array(types.Resource("cloudflare.streams.liveInput")))
	},
	"cloudflare.account.videos": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareAccount).GetVideos()).ToDataRes(types.Array(types.Resource("cloudflare.streams.video")))
	},
	"cloudflare.account.settings.enforceTwoFactor": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareAccountSettings).GetEnforceTwoFactor()).ToDataRes(types.Bool)
	},
	"cloudflare.streams.liveInput.id": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareStreamsLiveInput).GetId()).ToDataRes(types.String)
	},
	"cloudflare.streams.liveInput.uid": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareStreamsLiveInput).GetUid()).ToDataRes(types.String)
	},
	"cloudflare.streams.liveInput.name": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareStreamsLiveInput).GetName()).ToDataRes(types.String)
	},
	"cloudflare.streams.liveInput.deleteRecordingAfterDays": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareStreamsLiveInput).GetDeleteRecordingAfterDays()).ToDataRes(types.Int)
	},
	"cloudflare.streams.video.id": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareStreamsVideo).GetId()).ToDataRes(types.String)
	},
	"cloudflare.streams.video.uid": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareStreamsVideo).GetUid()).ToDataRes(types.String)
	},
	"cloudflare.streams.video.name": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareStreamsVideo).GetName()).ToDataRes(types.String)
	},
	"cloudflare.streams.video.creator": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareStreamsVideo).GetCreator()).ToDataRes(types.String)
	},
	"cloudflare.streams.video.duration": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareStreamsVideo).GetDuration()).ToDataRes(types.Float)
	},
	"cloudflare.streams.video.height": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareStreamsVideo).GetHeight()).ToDataRes(types.Int)
	},
	"cloudflare.streams.video.width": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareStreamsVideo).GetWidth()).ToDataRes(types.Int)
	},
	"cloudflare.streams.video.liveInput": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareStreamsVideo).GetLiveInput()).ToDataRes(types.String)
	},
	"cloudflare.streams.video.dash": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareStreamsVideo).GetDash()).ToDataRes(types.String)
	},
	"cloudflare.streams.video.hls": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareStreamsVideo).GetHls()).ToDataRes(types.String)
	},
	"cloudflare.streams.video.preview": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareStreamsVideo).GetPreview()).ToDataRes(types.String)
	},
	"cloudflare.streams.video.ready": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareStreamsVideo).GetReady()).ToDataRes(types.Bool)
	},
	"cloudflare.streams.video.requireSignedUrls": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareStreamsVideo).GetRequireSignedUrls()).ToDataRes(types.Bool)
	},
	"cloudflare.streams.video.scheduledDeletion": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareStreamsVideo).GetScheduledDeletion()).ToDataRes(types.Time)
	},
	"cloudflare.streams.video.size": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareStreamsVideo).GetSize()).ToDataRes(types.Int)
	},
	"cloudflare.streams.video.thumbnail": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareStreamsVideo).GetThumbnail()).ToDataRes(types.String)
	},
	"cloudflare.streams.video.thumbnailTimestampPct": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareStreamsVideo).GetThumbnailTimestampPct()).ToDataRes(types.Float)
	},
	"cloudflare.streams.video.uploaded": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareStreamsVideo).GetUploaded()).ToDataRes(types.Time)
	},
	"cloudflare.r2.buckets": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareR2).GetBuckets()).ToDataRes(types.Array(types.Resource("cloudflare.r2.bucket")))
	},
	"cloudflare.r2.bucket.name": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareR2Bucket).GetName()).ToDataRes(types.String)
	},
	"cloudflare.r2.bucket.location": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareR2Bucket).GetLocation()).ToDataRes(types.String)
	},
	"cloudflare.r2.bucket.createdOn": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareR2Bucket).GetCreatedOn()).ToDataRes(types.Time)
	},
	"cloudflare.workers.workers": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareWorkers).GetWorkers()).ToDataRes(types.Array(types.Resource("cloudflare.workers.worker")))
	},
	"cloudflare.workers.pages": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareWorkers).GetPages()).ToDataRes(types.Array(types.Resource("cloudflare.workers.page")))
	},
	"cloudflare.workers.worker.id": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareWorkersWorker).GetId()).ToDataRes(types.String)
	},
	"cloudflare.workers.worker.etag": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareWorkersWorker).GetEtag()).ToDataRes(types.String)
	},
	"cloudflare.workers.worker.size": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareWorkersWorker).GetSize()).ToDataRes(types.Int)
	},
	"cloudflare.workers.worker.deploymentId": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareWorkersWorker).GetDeploymentId()).ToDataRes(types.String)
	},
	"cloudflare.workers.worker.pipelineHash": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareWorkersWorker).GetPipelineHash()).ToDataRes(types.String)
	},
	"cloudflare.workers.worker.placementMode": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareWorkersWorker).GetPlacementMode()).ToDataRes(types.String)
	},
	"cloudflare.workers.worker.lastDeployedFrom": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareWorkersWorker).GetLastDeployedFrom()).ToDataRes(types.String)
	},
	"cloudflare.workers.worker.logPush": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareWorkersWorker).GetLogPush()).ToDataRes(types.Bool)
	},
	"cloudflare.workers.worker.createdOn": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareWorkersWorker).GetCreatedOn()).ToDataRes(types.Time)
	},
	"cloudflare.workers.worker.modifiedOn": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareWorkersWorker).GetModifiedOn()).ToDataRes(types.Time)
	},
	"cloudflare.workers.page.id": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareWorkersPage).GetId()).ToDataRes(types.String)
	},
	"cloudflare.workers.page.shortId": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareWorkersPage).GetShortId()).ToDataRes(types.String)
	},
	"cloudflare.workers.page.projectId": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareWorkersPage).GetProjectId()).ToDataRes(types.String)
	},
	"cloudflare.workers.page.projectName": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareWorkersPage).GetProjectName()).ToDataRes(types.String)
	},
	"cloudflare.workers.page.environment": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareWorkersPage).GetEnvironment()).ToDataRes(types.String)
	},
	"cloudflare.workers.page.url": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareWorkersPage).GetUrl()).ToDataRes(types.String)
	},
	"cloudflare.workers.page.aliases": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareWorkersPage).GetAliases()).ToDataRes(types.Array(types.String))
	},
	"cloudflare.workers.page.productionBranch": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareWorkersPage).GetProductionBranch()).ToDataRes(types.String)
	},
	"cloudflare.workers.page.createdOn": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareWorkersPage).GetCreatedOn()).ToDataRes(types.Time)
	},
	"cloudflare.workers.page.modifiedOn": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareWorkersPage).GetModifiedOn()).ToDataRes(types.Time)
	},
	"cloudflare.one.apps": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareOne).GetApps()).ToDataRes(types.Array(types.Resource("cloudflare.one.app")))
	},
	"cloudflare.one.identityProviders": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareOne).GetIdentityProviders()).ToDataRes(types.Array(types.Resource("cloudflare.one.idp")))
	},
	"cloudflare.one.app.id": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareOneApp).GetId()).ToDataRes(types.String)
	},
	"cloudflare.one.app.aud": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareOneApp).GetAud()).ToDataRes(types.String)
	},
	"cloudflare.one.app.name": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareOneApp).GetName()).ToDataRes(types.String)
	},
	"cloudflare.one.app.domain": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareOneApp).GetDomain()).ToDataRes(types.String)
	},
	"cloudflare.one.app.allowedIdentityProviders": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareOneApp).GetAllowedIdentityProviders()).ToDataRes(types.Array(types.String))
	},
	"cloudflare.one.app.appLauncherVisible": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareOneApp).GetAppLauncherVisible()).ToDataRes(types.Bool)
	},
	"cloudflare.one.app.autoRedirectToIdentity": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareOneApp).GetAutoRedirectToIdentity()).ToDataRes(types.Bool)
	},
	"cloudflare.one.app.corsHeaders": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareOneApp).GetCorsHeaders()).ToDataRes(types.Resource("cloudflare.corsHeaders"))
	},
	"cloudflare.one.app.optionsPreflightBypass": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareOneApp).GetOptionsPreflightBypass()).ToDataRes(types.Bool)
	},
	"cloudflare.one.app.customDenyMessage": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareOneApp).GetCustomDenyMessage()).ToDataRes(types.String)
	},
	"cloudflare.one.app.customDenyUrl": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareOneApp).GetCustomDenyUrl()).ToDataRes(types.String)
	},
	"cloudflare.one.app.serviceAuth401Redirect": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareOneApp).GetServiceAuth401Redirect()).ToDataRes(types.Bool)
	},
	"cloudflare.one.app.enableBindingCookie": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareOneApp).GetEnableBindingCookie()).ToDataRes(types.Bool)
	},
	"cloudflare.one.app.httpOnlyCookieAttribute": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareOneApp).GetHttpOnlyCookieAttribute()).ToDataRes(types.Bool)
	},
	"cloudflare.one.app.sameSiteCookieAttribute": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareOneApp).GetSameSiteCookieAttribute()).ToDataRes(types.String)
	},
	"cloudflare.one.app.logoUrl": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareOneApp).GetLogoUrl()).ToDataRes(types.String)
	},
	"cloudflare.one.app.sessionDuration": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareOneApp).GetSessionDuration()).ToDataRes(types.String)
	},
	"cloudflare.one.app.skipInterstitial": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareOneApp).GetSkipInterstitial()).ToDataRes(types.Bool)
	},
	"cloudflare.one.app.type": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareOneApp).GetType()).ToDataRes(types.String)
	},
	"cloudflare.one.app.createdAt": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareOneApp).GetCreatedAt()).ToDataRes(types.Time)
	},
	"cloudflare.one.app.updatedAt": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareOneApp).GetUpdatedAt()).ToDataRes(types.Time)
	},
	"cloudflare.corsHeaders.allowAllHeaders": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareCorsHeaders).GetAllowAllHeaders()).ToDataRes(types.Bool)
	},
	"cloudflare.corsHeaders.allowAllMethods": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareCorsHeaders).GetAllowAllMethods()).ToDataRes(types.Bool)
	},
	"cloudflare.corsHeaders.allowAllOrigins": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareCorsHeaders).GetAllowAllOrigins()).ToDataRes(types.Bool)
	},
	"cloudflare.corsHeaders.allowCredentials": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareCorsHeaders).GetAllowCredentials()).ToDataRes(types.Bool)
	},
	"cloudflare.corsHeaders.allowedHeaders": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareCorsHeaders).GetAllowedHeaders()).ToDataRes(types.Array(types.String))
	},
	"cloudflare.corsHeaders.allowedMethods": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareCorsHeaders).GetAllowedMethods()).ToDataRes(types.Array(types.String))
	},
	"cloudflare.corsHeaders.allowedOrigins": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareCorsHeaders).GetAllowedOrigins()).ToDataRes(types.Array(types.String))
	},
	"cloudflare.corsHeaders.maxAge": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareCorsHeaders).GetMaxAge()).ToDataRes(types.Int)
	},
	"cloudflare.one.idp.id": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareOneIdp).GetId()).ToDataRes(types.String)
	},
	"cloudflare.one.idp.name": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareOneIdp).GetName()).ToDataRes(types.String)
	},
	"cloudflare.one.idp.type": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlCloudflareOneIdp).GetType()).ToDataRes(types.String)
	},
}

func GetData(resource plugin.Resource, field string, args map[string]*llx.RawData) *plugin.DataRes {
	f, ok := getDataFields[resource.MqlName()+"."+field]
	if !ok {
		return &plugin.DataRes{Error: "cannot find '" + field + "' in resource '" + resource.MqlName() + "'"}
	}

	return f(resource)
}

var setDataFields = map[string]func(r plugin.Resource, v *llx.RawData) bool {
	"cloudflare.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlCloudflare).__id, ok = v.Value.(string)
			return
		},
	"cloudflare.zones": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflare).Zones, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"cloudflare.accounts": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflare).Accounts, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"cloudflare.zone.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlCloudflareZone).__id, ok = v.Value.(string)
			return
		},
	"cloudflare.zone.id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareZone).Id, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"cloudflare.zone.name": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareZone).Name, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"cloudflare.zone.nameServers": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareZone).NameServers, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"cloudflare.zone.originalNameServers": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareZone).OriginalNameServers, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"cloudflare.zone.status": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareZone).Status, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"cloudflare.zone.paused": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareZone).Paused, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"cloudflare.zone.type": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareZone).Type, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"cloudflare.zone.createdOn": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareZone).CreatedOn, ok = plugin.RawToTValue[*time.Time](v.Value, v.Error)
		return
	},
	"cloudflare.zone.modifiedOn": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareZone).ModifiedOn, ok = plugin.RawToTValue[*time.Time](v.Value, v.Error)
		return
	},
	"cloudflare.zone.account": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareZone).Account, ok = plugin.RawToTValue[*mqlCloudflareZoneAccount](v.Value, v.Error)
		return
	},
	"cloudflare.zone.dns": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareZone).Dns, ok = plugin.RawToTValue[*mqlCloudflareDns](v.Value, v.Error)
		return
	},
	"cloudflare.zone.liveInputs": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareZone).LiveInputs, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"cloudflare.zone.videos": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareZone).Videos, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"cloudflare.zone.r2": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareZone).R2, ok = plugin.RawToTValue[*mqlCloudflareR2](v.Value, v.Error)
		return
	},
	"cloudflare.zone.workers": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareZone).Workers, ok = plugin.RawToTValue[*mqlCloudflareWorkers](v.Value, v.Error)
		return
	},
	"cloudflare.zone.one": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareZone).One, ok = plugin.RawToTValue[*mqlCloudflareOne](v.Value, v.Error)
		return
	},
	"cloudflare.zone.account.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlCloudflareZoneAccount).__id, ok = v.Value.(string)
			return
		},
	"cloudflare.zone.account.id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareZoneAccount).Id, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"cloudflare.zone.account.name": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareZoneAccount).Name, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"cloudflare.zone.account.type": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareZoneAccount).Type, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"cloudflare.zone.account.email": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareZoneAccount).Email, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"cloudflare.dns.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlCloudflareDns).__id, ok = v.Value.(string)
			return
		},
	"cloudflare.dns.records": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareDns).Records, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"cloudflare.dns.record.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlCloudflareDnsRecord).__id, ok = v.Value.(string)
			return
		},
	"cloudflare.dns.record.id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareDnsRecord).Id, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"cloudflare.dns.record.name": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareDnsRecord).Name, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"cloudflare.dns.record.comment": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareDnsRecord).Comment, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"cloudflare.dns.record.tags": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareDnsRecord).Tags, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"cloudflare.dns.record.proxied": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareDnsRecord).Proxied, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"cloudflare.dns.record.proxiable": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareDnsRecord).Proxiable, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"cloudflare.dns.record.type": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareDnsRecord).Type, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"cloudflare.dns.record.content": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareDnsRecord).Content, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"cloudflare.dns.record.ttl": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareDnsRecord).Ttl, ok = plugin.RawToTValue[int64](v.Value, v.Error)
		return
	},
	"cloudflare.dns.record.createdOn": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareDnsRecord).CreatedOn, ok = plugin.RawToTValue[*time.Time](v.Value, v.Error)
		return
	},
	"cloudflare.dns.record.modifiedOn": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareDnsRecord).ModifiedOn, ok = plugin.RawToTValue[*time.Time](v.Value, v.Error)
		return
	},
	"cloudflare.account.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlCloudflareAccount).__id, ok = v.Value.(string)
			return
		},
	"cloudflare.account.id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareAccount).Id, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"cloudflare.account.name": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareAccount).Name, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"cloudflare.account.settings": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareAccount).Settings, ok = plugin.RawToTValue[*mqlCloudflareAccountSettings](v.Value, v.Error)
		return
	},
	"cloudflare.account.createdOn": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareAccount).CreatedOn, ok = plugin.RawToTValue[*time.Time](v.Value, v.Error)
		return
	},
	"cloudflare.account.liveInputs": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareAccount).LiveInputs, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"cloudflare.account.videos": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareAccount).Videos, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"cloudflare.account.settings.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlCloudflareAccountSettings).__id, ok = v.Value.(string)
			return
		},
	"cloudflare.account.settings.enforceTwoFactor": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareAccountSettings).EnforceTwoFactor, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"cloudflare.streams.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlCloudflareStreams).__id, ok = v.Value.(string)
			return
		},
	"cloudflare.streams.liveInput.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlCloudflareStreamsLiveInput).__id, ok = v.Value.(string)
			return
		},
	"cloudflare.streams.liveInput.id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareStreamsLiveInput).Id, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"cloudflare.streams.liveInput.uid": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareStreamsLiveInput).Uid, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"cloudflare.streams.liveInput.name": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareStreamsLiveInput).Name, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"cloudflare.streams.liveInput.deleteRecordingAfterDays": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareStreamsLiveInput).DeleteRecordingAfterDays, ok = plugin.RawToTValue[int64](v.Value, v.Error)
		return
	},
	"cloudflare.streams.video.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlCloudflareStreamsVideo).__id, ok = v.Value.(string)
			return
		},
	"cloudflare.streams.video.id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareStreamsVideo).Id, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"cloudflare.streams.video.uid": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareStreamsVideo).Uid, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"cloudflare.streams.video.name": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareStreamsVideo).Name, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"cloudflare.streams.video.creator": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareStreamsVideo).Creator, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"cloudflare.streams.video.duration": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareStreamsVideo).Duration, ok = plugin.RawToTValue[float64](v.Value, v.Error)
		return
	},
	"cloudflare.streams.video.height": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareStreamsVideo).Height, ok = plugin.RawToTValue[int64](v.Value, v.Error)
		return
	},
	"cloudflare.streams.video.width": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareStreamsVideo).Width, ok = plugin.RawToTValue[int64](v.Value, v.Error)
		return
	},
	"cloudflare.streams.video.liveInput": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareStreamsVideo).LiveInput, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"cloudflare.streams.video.dash": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareStreamsVideo).Dash, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"cloudflare.streams.video.hls": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareStreamsVideo).Hls, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"cloudflare.streams.video.preview": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareStreamsVideo).Preview, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"cloudflare.streams.video.ready": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareStreamsVideo).Ready, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"cloudflare.streams.video.requireSignedUrls": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareStreamsVideo).RequireSignedUrls, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"cloudflare.streams.video.scheduledDeletion": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareStreamsVideo).ScheduledDeletion, ok = plugin.RawToTValue[*time.Time](v.Value, v.Error)
		return
	},
	"cloudflare.streams.video.size": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareStreamsVideo).Size, ok = plugin.RawToTValue[int64](v.Value, v.Error)
		return
	},
	"cloudflare.streams.video.thumbnail": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareStreamsVideo).Thumbnail, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"cloudflare.streams.video.thumbnailTimestampPct": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareStreamsVideo).ThumbnailTimestampPct, ok = plugin.RawToTValue[float64](v.Value, v.Error)
		return
	},
	"cloudflare.streams.video.uploaded": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareStreamsVideo).Uploaded, ok = plugin.RawToTValue[*time.Time](v.Value, v.Error)
		return
	},
	"cloudflare.r2.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlCloudflareR2).__id, ok = v.Value.(string)
			return
		},
	"cloudflare.r2.buckets": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareR2).Buckets, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"cloudflare.r2.bucket.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlCloudflareR2Bucket).__id, ok = v.Value.(string)
			return
		},
	"cloudflare.r2.bucket.name": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareR2Bucket).Name, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"cloudflare.r2.bucket.location": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareR2Bucket).Location, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"cloudflare.r2.bucket.createdOn": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareR2Bucket).CreatedOn, ok = plugin.RawToTValue[*time.Time](v.Value, v.Error)
		return
	},
	"cloudflare.workers.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlCloudflareWorkers).__id, ok = v.Value.(string)
			return
		},
	"cloudflare.workers.workers": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareWorkers).Workers, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"cloudflare.workers.pages": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareWorkers).Pages, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"cloudflare.workers.worker.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlCloudflareWorkersWorker).__id, ok = v.Value.(string)
			return
		},
	"cloudflare.workers.worker.id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareWorkersWorker).Id, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"cloudflare.workers.worker.etag": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareWorkersWorker).Etag, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"cloudflare.workers.worker.size": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareWorkersWorker).Size, ok = plugin.RawToTValue[int64](v.Value, v.Error)
		return
	},
	"cloudflare.workers.worker.deploymentId": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareWorkersWorker).DeploymentId, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"cloudflare.workers.worker.pipelineHash": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareWorkersWorker).PipelineHash, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"cloudflare.workers.worker.placementMode": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareWorkersWorker).PlacementMode, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"cloudflare.workers.worker.lastDeployedFrom": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareWorkersWorker).LastDeployedFrom, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"cloudflare.workers.worker.logPush": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareWorkersWorker).LogPush, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"cloudflare.workers.worker.createdOn": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareWorkersWorker).CreatedOn, ok = plugin.RawToTValue[*time.Time](v.Value, v.Error)
		return
	},
	"cloudflare.workers.worker.modifiedOn": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareWorkersWorker).ModifiedOn, ok = plugin.RawToTValue[*time.Time](v.Value, v.Error)
		return
	},
	"cloudflare.workers.page.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlCloudflareWorkersPage).__id, ok = v.Value.(string)
			return
		},
	"cloudflare.workers.page.id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareWorkersPage).Id, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"cloudflare.workers.page.shortId": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareWorkersPage).ShortId, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"cloudflare.workers.page.projectId": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareWorkersPage).ProjectId, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"cloudflare.workers.page.projectName": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareWorkersPage).ProjectName, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"cloudflare.workers.page.environment": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareWorkersPage).Environment, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"cloudflare.workers.page.url": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareWorkersPage).Url, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"cloudflare.workers.page.aliases": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareWorkersPage).Aliases, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"cloudflare.workers.page.productionBranch": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareWorkersPage).ProductionBranch, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"cloudflare.workers.page.createdOn": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareWorkersPage).CreatedOn, ok = plugin.RawToTValue[*time.Time](v.Value, v.Error)
		return
	},
	"cloudflare.workers.page.modifiedOn": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareWorkersPage).ModifiedOn, ok = plugin.RawToTValue[*time.Time](v.Value, v.Error)
		return
	},
	"cloudflare.one.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlCloudflareOne).__id, ok = v.Value.(string)
			return
		},
	"cloudflare.one.apps": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareOne).Apps, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"cloudflare.one.identityProviders": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareOne).IdentityProviders, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"cloudflare.one.app.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlCloudflareOneApp).__id, ok = v.Value.(string)
			return
		},
	"cloudflare.one.app.id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareOneApp).Id, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"cloudflare.one.app.aud": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareOneApp).Aud, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"cloudflare.one.app.name": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareOneApp).Name, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"cloudflare.one.app.domain": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareOneApp).Domain, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"cloudflare.one.app.allowedIdentityProviders": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareOneApp).AllowedIdentityProviders, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"cloudflare.one.app.appLauncherVisible": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareOneApp).AppLauncherVisible, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"cloudflare.one.app.autoRedirectToIdentity": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareOneApp).AutoRedirectToIdentity, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"cloudflare.one.app.corsHeaders": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareOneApp).CorsHeaders, ok = plugin.RawToTValue[*mqlCloudflareCorsHeaders](v.Value, v.Error)
		return
	},
	"cloudflare.one.app.optionsPreflightBypass": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareOneApp).OptionsPreflightBypass, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"cloudflare.one.app.customDenyMessage": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareOneApp).CustomDenyMessage, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"cloudflare.one.app.customDenyUrl": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareOneApp).CustomDenyUrl, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"cloudflare.one.app.serviceAuth401Redirect": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareOneApp).ServiceAuth401Redirect, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"cloudflare.one.app.enableBindingCookie": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareOneApp).EnableBindingCookie, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"cloudflare.one.app.httpOnlyCookieAttribute": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareOneApp).HttpOnlyCookieAttribute, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"cloudflare.one.app.sameSiteCookieAttribute": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareOneApp).SameSiteCookieAttribute, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"cloudflare.one.app.logoUrl": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareOneApp).LogoUrl, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"cloudflare.one.app.sessionDuration": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareOneApp).SessionDuration, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"cloudflare.one.app.skipInterstitial": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareOneApp).SkipInterstitial, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"cloudflare.one.app.type": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareOneApp).Type, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"cloudflare.one.app.createdAt": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareOneApp).CreatedAt, ok = plugin.RawToTValue[*time.Time](v.Value, v.Error)
		return
	},
	"cloudflare.one.app.updatedAt": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareOneApp).UpdatedAt, ok = plugin.RawToTValue[*time.Time](v.Value, v.Error)
		return
	},
	"cloudflare.corsHeaders.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlCloudflareCorsHeaders).__id, ok = v.Value.(string)
			return
		},
	"cloudflare.corsHeaders.allowAllHeaders": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareCorsHeaders).AllowAllHeaders, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"cloudflare.corsHeaders.allowAllMethods": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareCorsHeaders).AllowAllMethods, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"cloudflare.corsHeaders.allowAllOrigins": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareCorsHeaders).AllowAllOrigins, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"cloudflare.corsHeaders.allowCredentials": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareCorsHeaders).AllowCredentials, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"cloudflare.corsHeaders.allowedHeaders": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareCorsHeaders).AllowedHeaders, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"cloudflare.corsHeaders.allowedMethods": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareCorsHeaders).AllowedMethods, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"cloudflare.corsHeaders.allowedOrigins": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareCorsHeaders).AllowedOrigins, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"cloudflare.corsHeaders.maxAge": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareCorsHeaders).MaxAge, ok = plugin.RawToTValue[int64](v.Value, v.Error)
		return
	},
	"cloudflare.one.idp.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlCloudflareOneIdp).__id, ok = v.Value.(string)
			return
		},
	"cloudflare.one.idp.id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareOneIdp).Id, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"cloudflare.one.idp.name": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareOneIdp).Name, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"cloudflare.one.idp.type": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlCloudflareOneIdp).Type, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
}

func SetData(resource plugin.Resource, field string, val *llx.RawData) error {
	f, ok := setDataFields[resource.MqlName() + "." + field]
	if !ok {
		return errors.New("[cloudflare] cannot set '"+field+"' in resource '"+resource.MqlName()+"', field not found")
	}

	if ok := f(resource, val); !ok {
		return errors.New("[cloudflare] cannot set '"+field+"' in resource '"+resource.MqlName()+"', type does not match")
	}
	return nil
}

func SetAllData(resource plugin.Resource, args map[string]*llx.RawData) error {
	var err error
	for k, v := range args {
		if err = SetData(resource, k, v); err != nil {
			return err
		}
	}
	return nil
}

// mqlCloudflare for the cloudflare resource
type mqlCloudflare struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlCloudflareInternal it will be used here
	Zones plugin.TValue[[]interface{}]
	Accounts plugin.TValue[[]interface{}]
}

// createCloudflare creates a new instance of this resource
func createCloudflare(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlCloudflare{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	if res.__id == "" {
	res.__id, err = res.id()
		if err != nil {
			return nil, err
		}
	}

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("cloudflare", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlCloudflare) MqlName() string {
	return "cloudflare"
}

func (c *mqlCloudflare) MqlID() string {
	return c.__id
}

func (c *mqlCloudflare) GetZones() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.Zones, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("cloudflare", c.__id, "zones")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.zones()
	})
}

func (c *mqlCloudflare) GetAccounts() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.Accounts, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("cloudflare", c.__id, "accounts")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.accounts()
	})
}

// mqlCloudflareZone for the cloudflare.zone resource
type mqlCloudflareZone struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlCloudflareZoneInternal it will be used here
	Id plugin.TValue[string]
	Name plugin.TValue[string]
	NameServers plugin.TValue[[]interface{}]
	OriginalNameServers plugin.TValue[[]interface{}]
	Status plugin.TValue[string]
	Paused plugin.TValue[bool]
	Type plugin.TValue[string]
	CreatedOn plugin.TValue[*time.Time]
	ModifiedOn plugin.TValue[*time.Time]
	Account plugin.TValue[*mqlCloudflareZoneAccount]
	Dns plugin.TValue[*mqlCloudflareDns]
	LiveInputs plugin.TValue[[]interface{}]
	Videos plugin.TValue[[]interface{}]
	R2 plugin.TValue[*mqlCloudflareR2]
	Workers plugin.TValue[*mqlCloudflareWorkers]
	One plugin.TValue[*mqlCloudflareOne]
}

// createCloudflareZone creates a new instance of this resource
func createCloudflareZone(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlCloudflareZone{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	if res.__id == "" {
	res.__id, err = res.id()
		if err != nil {
			return nil, err
		}
	}

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("cloudflare.zone", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlCloudflareZone) MqlName() string {
	return "cloudflare.zone"
}

func (c *mqlCloudflareZone) MqlID() string {
	return c.__id
}

func (c *mqlCloudflareZone) GetId() *plugin.TValue[string] {
	return &c.Id
}

func (c *mqlCloudflareZone) GetName() *plugin.TValue[string] {
	return &c.Name
}

func (c *mqlCloudflareZone) GetNameServers() *plugin.TValue[[]interface{}] {
	return &c.NameServers
}

func (c *mqlCloudflareZone) GetOriginalNameServers() *plugin.TValue[[]interface{}] {
	return &c.OriginalNameServers
}

func (c *mqlCloudflareZone) GetStatus() *plugin.TValue[string] {
	return &c.Status
}

func (c *mqlCloudflareZone) GetPaused() *plugin.TValue[bool] {
	return &c.Paused
}

func (c *mqlCloudflareZone) GetType() *plugin.TValue[string] {
	return &c.Type
}

func (c *mqlCloudflareZone) GetCreatedOn() *plugin.TValue[*time.Time] {
	return &c.CreatedOn
}

func (c *mqlCloudflareZone) GetModifiedOn() *plugin.TValue[*time.Time] {
	return &c.ModifiedOn
}

func (c *mqlCloudflareZone) GetAccount() *plugin.TValue[*mqlCloudflareZoneAccount] {
	return &c.Account
}

func (c *mqlCloudflareZone) GetDns() *plugin.TValue[*mqlCloudflareDns] {
	return plugin.GetOrCompute[*mqlCloudflareDns](&c.Dns, func() (*mqlCloudflareDns, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("cloudflare.zone", c.__id, "dns")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.(*mqlCloudflareDns), nil
			}
		}

		return c.dns()
	})
}

func (c *mqlCloudflareZone) GetLiveInputs() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.LiveInputs, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("cloudflare.zone", c.__id, "liveInputs")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.liveInputs()
	})
}

func (c *mqlCloudflareZone) GetVideos() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.Videos, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("cloudflare.zone", c.__id, "videos")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.videos()
	})
}

func (c *mqlCloudflareZone) GetR2() *plugin.TValue[*mqlCloudflareR2] {
	return plugin.GetOrCompute[*mqlCloudflareR2](&c.R2, func() (*mqlCloudflareR2, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("cloudflare.zone", c.__id, "r2")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.(*mqlCloudflareR2), nil
			}
		}

		return c.r2()
	})
}

func (c *mqlCloudflareZone) GetWorkers() *plugin.TValue[*mqlCloudflareWorkers] {
	return plugin.GetOrCompute[*mqlCloudflareWorkers](&c.Workers, func() (*mqlCloudflareWorkers, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("cloudflare.zone", c.__id, "workers")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.(*mqlCloudflareWorkers), nil
			}
		}

		return c.workers()
	})
}

func (c *mqlCloudflareZone) GetOne() *plugin.TValue[*mqlCloudflareOne] {
	return plugin.GetOrCompute[*mqlCloudflareOne](&c.One, func() (*mqlCloudflareOne, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("cloudflare.zone", c.__id, "one")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.(*mqlCloudflareOne), nil
			}
		}

		return c.one()
	})
}

// mqlCloudflareZoneAccount for the cloudflare.zone.account resource
type mqlCloudflareZoneAccount struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlCloudflareZoneAccountInternal it will be used here
	Id plugin.TValue[string]
	Name plugin.TValue[string]
	Type plugin.TValue[string]
	Email plugin.TValue[string]
}

// createCloudflareZoneAccount creates a new instance of this resource
func createCloudflareZoneAccount(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlCloudflareZoneAccount{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	if res.__id == "" {
	res.__id, err = res.id()
		if err != nil {
			return nil, err
		}
	}

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("cloudflare.zone.account", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlCloudflareZoneAccount) MqlName() string {
	return "cloudflare.zone.account"
}

func (c *mqlCloudflareZoneAccount) MqlID() string {
	return c.__id
}

func (c *mqlCloudflareZoneAccount) GetId() *plugin.TValue[string] {
	return &c.Id
}

func (c *mqlCloudflareZoneAccount) GetName() *plugin.TValue[string] {
	return &c.Name
}

func (c *mqlCloudflareZoneAccount) GetType() *plugin.TValue[string] {
	return &c.Type
}

func (c *mqlCloudflareZoneAccount) GetEmail() *plugin.TValue[string] {
	return &c.Email
}

// mqlCloudflareDns for the cloudflare.dns resource
type mqlCloudflareDns struct {
	MqlRuntime *plugin.Runtime
	__id string
	mqlCloudflareDnsInternal
	Records plugin.TValue[[]interface{}]
}

// createCloudflareDns creates a new instance of this resource
func createCloudflareDns(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlCloudflareDns{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	// to override __id implement: id() (string, error)

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("cloudflare.dns", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlCloudflareDns) MqlName() string {
	return "cloudflare.dns"
}

func (c *mqlCloudflareDns) MqlID() string {
	return c.__id
}

func (c *mqlCloudflareDns) GetRecords() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.Records, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("cloudflare.dns", c.__id, "records")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.records()
	})
}

// mqlCloudflareDnsRecord for the cloudflare.dns.record resource
type mqlCloudflareDnsRecord struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlCloudflareDnsRecordInternal it will be used here
	Id plugin.TValue[string]
	Name plugin.TValue[string]
	Comment plugin.TValue[string]
	Tags plugin.TValue[[]interface{}]
	Proxied plugin.TValue[bool]
	Proxiable plugin.TValue[bool]
	Type plugin.TValue[string]
	Content plugin.TValue[string]
	Ttl plugin.TValue[int64]
	CreatedOn plugin.TValue[*time.Time]
	ModifiedOn plugin.TValue[*time.Time]
}

// createCloudflareDnsRecord creates a new instance of this resource
func createCloudflareDnsRecord(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlCloudflareDnsRecord{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	if res.__id == "" {
	res.__id, err = res.id()
		if err != nil {
			return nil, err
		}
	}

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("cloudflare.dns.record", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlCloudflareDnsRecord) MqlName() string {
	return "cloudflare.dns.record"
}

func (c *mqlCloudflareDnsRecord) MqlID() string {
	return c.__id
}

func (c *mqlCloudflareDnsRecord) GetId() *plugin.TValue[string] {
	return &c.Id
}

func (c *mqlCloudflareDnsRecord) GetName() *plugin.TValue[string] {
	return &c.Name
}

func (c *mqlCloudflareDnsRecord) GetComment() *plugin.TValue[string] {
	return &c.Comment
}

func (c *mqlCloudflareDnsRecord) GetTags() *plugin.TValue[[]interface{}] {
	return &c.Tags
}

func (c *mqlCloudflareDnsRecord) GetProxied() *plugin.TValue[bool] {
	return &c.Proxied
}

func (c *mqlCloudflareDnsRecord) GetProxiable() *plugin.TValue[bool] {
	return &c.Proxiable
}

func (c *mqlCloudflareDnsRecord) GetType() *plugin.TValue[string] {
	return &c.Type
}

func (c *mqlCloudflareDnsRecord) GetContent() *plugin.TValue[string] {
	return &c.Content
}

func (c *mqlCloudflareDnsRecord) GetTtl() *plugin.TValue[int64] {
	return &c.Ttl
}

func (c *mqlCloudflareDnsRecord) GetCreatedOn() *plugin.TValue[*time.Time] {
	return &c.CreatedOn
}

func (c *mqlCloudflareDnsRecord) GetModifiedOn() *plugin.TValue[*time.Time] {
	return &c.ModifiedOn
}

// mqlCloudflareAccount for the cloudflare.account resource
type mqlCloudflareAccount struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlCloudflareAccountInternal it will be used here
	Id plugin.TValue[string]
	Name plugin.TValue[string]
	Settings plugin.TValue[*mqlCloudflareAccountSettings]
	CreatedOn plugin.TValue[*time.Time]
	LiveInputs plugin.TValue[[]interface{}]
	Videos plugin.TValue[[]interface{}]
}

// createCloudflareAccount creates a new instance of this resource
func createCloudflareAccount(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlCloudflareAccount{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	if res.__id == "" {
	res.__id, err = res.id()
		if err != nil {
			return nil, err
		}
	}

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("cloudflare.account", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlCloudflareAccount) MqlName() string {
	return "cloudflare.account"
}

func (c *mqlCloudflareAccount) MqlID() string {
	return c.__id
}

func (c *mqlCloudflareAccount) GetId() *plugin.TValue[string] {
	return &c.Id
}

func (c *mqlCloudflareAccount) GetName() *plugin.TValue[string] {
	return &c.Name
}

func (c *mqlCloudflareAccount) GetSettings() *plugin.TValue[*mqlCloudflareAccountSettings] {
	return &c.Settings
}

func (c *mqlCloudflareAccount) GetCreatedOn() *plugin.TValue[*time.Time] {
	return &c.CreatedOn
}

func (c *mqlCloudflareAccount) GetLiveInputs() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.LiveInputs, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("cloudflare.account", c.__id, "liveInputs")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.liveInputs()
	})
}

func (c *mqlCloudflareAccount) GetVideos() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.Videos, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("cloudflare.account", c.__id, "videos")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.videos()
	})
}

// mqlCloudflareAccountSettings for the cloudflare.account.settings resource
type mqlCloudflareAccountSettings struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlCloudflareAccountSettingsInternal it will be used here
	EnforceTwoFactor plugin.TValue[bool]
}

// createCloudflareAccountSettings creates a new instance of this resource
func createCloudflareAccountSettings(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlCloudflareAccountSettings{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	// to override __id implement: id() (string, error)

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("cloudflare.account.settings", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlCloudflareAccountSettings) MqlName() string {
	return "cloudflare.account.settings"
}

func (c *mqlCloudflareAccountSettings) MqlID() string {
	return c.__id
}

func (c *mqlCloudflareAccountSettings) GetEnforceTwoFactor() *plugin.TValue[bool] {
	return &c.EnforceTwoFactor
}

// mqlCloudflareStreams for the cloudflare.streams resource
type mqlCloudflareStreams struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlCloudflareStreamsInternal it will be used here
}

// createCloudflareStreams creates a new instance of this resource
func createCloudflareStreams(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlCloudflareStreams{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	// to override __id implement: id() (string, error)

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("cloudflare.streams", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlCloudflareStreams) MqlName() string {
	return "cloudflare.streams"
}

func (c *mqlCloudflareStreams) MqlID() string {
	return c.__id
}

// mqlCloudflareStreamsLiveInput for the cloudflare.streams.liveInput resource
type mqlCloudflareStreamsLiveInput struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlCloudflareStreamsLiveInputInternal it will be used here
	Id plugin.TValue[string]
	Uid plugin.TValue[string]
	Name plugin.TValue[string]
	DeleteRecordingAfterDays plugin.TValue[int64]
}

// createCloudflareStreamsLiveInput creates a new instance of this resource
func createCloudflareStreamsLiveInput(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlCloudflareStreamsLiveInput{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	if res.__id == "" {
	res.__id, err = res.id()
		if err != nil {
			return nil, err
		}
	}

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("cloudflare.streams.liveInput", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlCloudflareStreamsLiveInput) MqlName() string {
	return "cloudflare.streams.liveInput"
}

func (c *mqlCloudflareStreamsLiveInput) MqlID() string {
	return c.__id
}

func (c *mqlCloudflareStreamsLiveInput) GetId() *plugin.TValue[string] {
	return &c.Id
}

func (c *mqlCloudflareStreamsLiveInput) GetUid() *plugin.TValue[string] {
	return &c.Uid
}

func (c *mqlCloudflareStreamsLiveInput) GetName() *plugin.TValue[string] {
	return &c.Name
}

func (c *mqlCloudflareStreamsLiveInput) GetDeleteRecordingAfterDays() *plugin.TValue[int64] {
	return &c.DeleteRecordingAfterDays
}

// mqlCloudflareStreamsVideo for the cloudflare.streams.video resource
type mqlCloudflareStreamsVideo struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlCloudflareStreamsVideoInternal it will be used here
	Id plugin.TValue[string]
	Uid plugin.TValue[string]
	Name plugin.TValue[string]
	Creator plugin.TValue[string]
	Duration plugin.TValue[float64]
	Height plugin.TValue[int64]
	Width plugin.TValue[int64]
	LiveInput plugin.TValue[string]
	Dash plugin.TValue[string]
	Hls plugin.TValue[string]
	Preview plugin.TValue[string]
	Ready plugin.TValue[bool]
	RequireSignedUrls plugin.TValue[bool]
	ScheduledDeletion plugin.TValue[*time.Time]
	Size plugin.TValue[int64]
	Thumbnail plugin.TValue[string]
	ThumbnailTimestampPct plugin.TValue[float64]
	Uploaded plugin.TValue[*time.Time]
}

// createCloudflareStreamsVideo creates a new instance of this resource
func createCloudflareStreamsVideo(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlCloudflareStreamsVideo{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	if res.__id == "" {
	res.__id, err = res.id()
		if err != nil {
			return nil, err
		}
	}

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("cloudflare.streams.video", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlCloudflareStreamsVideo) MqlName() string {
	return "cloudflare.streams.video"
}

func (c *mqlCloudflareStreamsVideo) MqlID() string {
	return c.__id
}

func (c *mqlCloudflareStreamsVideo) GetId() *plugin.TValue[string] {
	return &c.Id
}

func (c *mqlCloudflareStreamsVideo) GetUid() *plugin.TValue[string] {
	return &c.Uid
}

func (c *mqlCloudflareStreamsVideo) GetName() *plugin.TValue[string] {
	return &c.Name
}

func (c *mqlCloudflareStreamsVideo) GetCreator() *plugin.TValue[string] {
	return &c.Creator
}

func (c *mqlCloudflareStreamsVideo) GetDuration() *plugin.TValue[float64] {
	return &c.Duration
}

func (c *mqlCloudflareStreamsVideo) GetHeight() *plugin.TValue[int64] {
	return &c.Height
}

func (c *mqlCloudflareStreamsVideo) GetWidth() *plugin.TValue[int64] {
	return &c.Width
}

func (c *mqlCloudflareStreamsVideo) GetLiveInput() *plugin.TValue[string] {
	return &c.LiveInput
}

func (c *mqlCloudflareStreamsVideo) GetDash() *plugin.TValue[string] {
	return &c.Dash
}

func (c *mqlCloudflareStreamsVideo) GetHls() *plugin.TValue[string] {
	return &c.Hls
}

func (c *mqlCloudflareStreamsVideo) GetPreview() *plugin.TValue[string] {
	return &c.Preview
}

func (c *mqlCloudflareStreamsVideo) GetReady() *plugin.TValue[bool] {
	return &c.Ready
}

func (c *mqlCloudflareStreamsVideo) GetRequireSignedUrls() *plugin.TValue[bool] {
	return &c.RequireSignedUrls
}

func (c *mqlCloudflareStreamsVideo) GetScheduledDeletion() *plugin.TValue[*time.Time] {
	return &c.ScheduledDeletion
}

func (c *mqlCloudflareStreamsVideo) GetSize() *plugin.TValue[int64] {
	return &c.Size
}

func (c *mqlCloudflareStreamsVideo) GetThumbnail() *plugin.TValue[string] {
	return &c.Thumbnail
}

func (c *mqlCloudflareStreamsVideo) GetThumbnailTimestampPct() *plugin.TValue[float64] {
	return &c.ThumbnailTimestampPct
}

func (c *mqlCloudflareStreamsVideo) GetUploaded() *plugin.TValue[*time.Time] {
	return &c.Uploaded
}

// mqlCloudflareR2 for the cloudflare.r2 resource
type mqlCloudflareR2 struct {
	MqlRuntime *plugin.Runtime
	__id string
	mqlCloudflareR2Internal
	Buckets plugin.TValue[[]interface{}]
}

// createCloudflareR2 creates a new instance of this resource
func createCloudflareR2(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlCloudflareR2{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	if res.__id == "" {
	res.__id, err = res.id()
		if err != nil {
			return nil, err
		}
	}

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("cloudflare.r2", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlCloudflareR2) MqlName() string {
	return "cloudflare.r2"
}

func (c *mqlCloudflareR2) MqlID() string {
	return c.__id
}

func (c *mqlCloudflareR2) GetBuckets() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.Buckets, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("cloudflare.r2", c.__id, "buckets")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.buckets()
	})
}

// mqlCloudflareR2Bucket for the cloudflare.r2.bucket resource
type mqlCloudflareR2Bucket struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlCloudflareR2BucketInternal it will be used here
	Name plugin.TValue[string]
	Location plugin.TValue[string]
	CreatedOn plugin.TValue[*time.Time]
}

// createCloudflareR2Bucket creates a new instance of this resource
func createCloudflareR2Bucket(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlCloudflareR2Bucket{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	if res.__id == "" {
	res.__id, err = res.id()
		if err != nil {
			return nil, err
		}
	}

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("cloudflare.r2.bucket", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlCloudflareR2Bucket) MqlName() string {
	return "cloudflare.r2.bucket"
}

func (c *mqlCloudflareR2Bucket) MqlID() string {
	return c.__id
}

func (c *mqlCloudflareR2Bucket) GetName() *plugin.TValue[string] {
	return &c.Name
}

func (c *mqlCloudflareR2Bucket) GetLocation() *plugin.TValue[string] {
	return &c.Location
}

func (c *mqlCloudflareR2Bucket) GetCreatedOn() *plugin.TValue[*time.Time] {
	return &c.CreatedOn
}

// mqlCloudflareWorkers for the cloudflare.workers resource
type mqlCloudflareWorkers struct {
	MqlRuntime *plugin.Runtime
	__id string
	mqlCloudflareWorkersInternal
	Workers plugin.TValue[[]interface{}]
	Pages plugin.TValue[[]interface{}]
}

// createCloudflareWorkers creates a new instance of this resource
func createCloudflareWorkers(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlCloudflareWorkers{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	// to override __id implement: id() (string, error)

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("cloudflare.workers", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlCloudflareWorkers) MqlName() string {
	return "cloudflare.workers"
}

func (c *mqlCloudflareWorkers) MqlID() string {
	return c.__id
}

func (c *mqlCloudflareWorkers) GetWorkers() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.Workers, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("cloudflare.workers", c.__id, "workers")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.workers()
	})
}

func (c *mqlCloudflareWorkers) GetPages() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.Pages, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("cloudflare.workers", c.__id, "pages")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.pages()
	})
}

// mqlCloudflareWorkersWorker for the cloudflare.workers.worker resource
type mqlCloudflareWorkersWorker struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlCloudflareWorkersWorkerInternal it will be used here
	Id plugin.TValue[string]
	Etag plugin.TValue[string]
	Size plugin.TValue[int64]
	DeploymentId plugin.TValue[string]
	PipelineHash plugin.TValue[string]
	PlacementMode plugin.TValue[string]
	LastDeployedFrom plugin.TValue[string]
	LogPush plugin.TValue[bool]
	CreatedOn plugin.TValue[*time.Time]
	ModifiedOn plugin.TValue[*time.Time]
}

// createCloudflareWorkersWorker creates a new instance of this resource
func createCloudflareWorkersWorker(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlCloudflareWorkersWorker{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	// to override __id implement: id() (string, error)

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("cloudflare.workers.worker", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlCloudflareWorkersWorker) MqlName() string {
	return "cloudflare.workers.worker"
}

func (c *mqlCloudflareWorkersWorker) MqlID() string {
	return c.__id
}

func (c *mqlCloudflareWorkersWorker) GetId() *plugin.TValue[string] {
	return &c.Id
}

func (c *mqlCloudflareWorkersWorker) GetEtag() *plugin.TValue[string] {
	return &c.Etag
}

func (c *mqlCloudflareWorkersWorker) GetSize() *plugin.TValue[int64] {
	return &c.Size
}

func (c *mqlCloudflareWorkersWorker) GetDeploymentId() *plugin.TValue[string] {
	return &c.DeploymentId
}

func (c *mqlCloudflareWorkersWorker) GetPipelineHash() *plugin.TValue[string] {
	return &c.PipelineHash
}

func (c *mqlCloudflareWorkersWorker) GetPlacementMode() *plugin.TValue[string] {
	return &c.PlacementMode
}

func (c *mqlCloudflareWorkersWorker) GetLastDeployedFrom() *plugin.TValue[string] {
	return &c.LastDeployedFrom
}

func (c *mqlCloudflareWorkersWorker) GetLogPush() *plugin.TValue[bool] {
	return &c.LogPush
}

func (c *mqlCloudflareWorkersWorker) GetCreatedOn() *plugin.TValue[*time.Time] {
	return &c.CreatedOn
}

func (c *mqlCloudflareWorkersWorker) GetModifiedOn() *plugin.TValue[*time.Time] {
	return &c.ModifiedOn
}

// mqlCloudflareWorkersPage for the cloudflare.workers.page resource
type mqlCloudflareWorkersPage struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlCloudflareWorkersPageInternal it will be used here
	Id plugin.TValue[string]
	ShortId plugin.TValue[string]
	ProjectId plugin.TValue[string]
	ProjectName plugin.TValue[string]
	Environment plugin.TValue[string]
	Url plugin.TValue[string]
	Aliases plugin.TValue[[]interface{}]
	ProductionBranch plugin.TValue[string]
	CreatedOn plugin.TValue[*time.Time]
	ModifiedOn plugin.TValue[*time.Time]
}

// createCloudflareWorkersPage creates a new instance of this resource
func createCloudflareWorkersPage(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlCloudflareWorkersPage{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	// to override __id implement: id() (string, error)

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("cloudflare.workers.page", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlCloudflareWorkersPage) MqlName() string {
	return "cloudflare.workers.page"
}

func (c *mqlCloudflareWorkersPage) MqlID() string {
	return c.__id
}

func (c *mqlCloudflareWorkersPage) GetId() *plugin.TValue[string] {
	return &c.Id
}

func (c *mqlCloudflareWorkersPage) GetShortId() *plugin.TValue[string] {
	return &c.ShortId
}

func (c *mqlCloudflareWorkersPage) GetProjectId() *plugin.TValue[string] {
	return &c.ProjectId
}

func (c *mqlCloudflareWorkersPage) GetProjectName() *plugin.TValue[string] {
	return &c.ProjectName
}

func (c *mqlCloudflareWorkersPage) GetEnvironment() *plugin.TValue[string] {
	return &c.Environment
}

func (c *mqlCloudflareWorkersPage) GetUrl() *plugin.TValue[string] {
	return &c.Url
}

func (c *mqlCloudflareWorkersPage) GetAliases() *plugin.TValue[[]interface{}] {
	return &c.Aliases
}

func (c *mqlCloudflareWorkersPage) GetProductionBranch() *plugin.TValue[string] {
	return &c.ProductionBranch
}

func (c *mqlCloudflareWorkersPage) GetCreatedOn() *plugin.TValue[*time.Time] {
	return &c.CreatedOn
}

func (c *mqlCloudflareWorkersPage) GetModifiedOn() *plugin.TValue[*time.Time] {
	return &c.ModifiedOn
}

// mqlCloudflareOne for the cloudflare.one resource
type mqlCloudflareOne struct {
	MqlRuntime *plugin.Runtime
	__id string
	mqlCloudflareOneInternal
	Apps plugin.TValue[[]interface{}]
	IdentityProviders plugin.TValue[[]interface{}]
}

// createCloudflareOne creates a new instance of this resource
func createCloudflareOne(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlCloudflareOne{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	// to override __id implement: id() (string, error)

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("cloudflare.one", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlCloudflareOne) MqlName() string {
	return "cloudflare.one"
}

func (c *mqlCloudflareOne) MqlID() string {
	return c.__id
}

func (c *mqlCloudflareOne) GetApps() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.Apps, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("cloudflare.one", c.__id, "apps")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.apps()
	})
}

func (c *mqlCloudflareOne) GetIdentityProviders() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.IdentityProviders, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("cloudflare.one", c.__id, "identityProviders")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.identityProviders()
	})
}

// mqlCloudflareOneApp for the cloudflare.one.app resource
type mqlCloudflareOneApp struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlCloudflareOneAppInternal it will be used here
	Id plugin.TValue[string]
	Aud plugin.TValue[string]
	Name plugin.TValue[string]
	Domain plugin.TValue[string]
	AllowedIdentityProviders plugin.TValue[[]interface{}]
	AppLauncherVisible plugin.TValue[bool]
	AutoRedirectToIdentity plugin.TValue[bool]
	CorsHeaders plugin.TValue[*mqlCloudflareCorsHeaders]
	OptionsPreflightBypass plugin.TValue[bool]
	CustomDenyMessage plugin.TValue[string]
	CustomDenyUrl plugin.TValue[string]
	ServiceAuth401Redirect plugin.TValue[bool]
	EnableBindingCookie plugin.TValue[bool]
	HttpOnlyCookieAttribute plugin.TValue[bool]
	SameSiteCookieAttribute plugin.TValue[string]
	LogoUrl plugin.TValue[string]
	SessionDuration plugin.TValue[string]
	SkipInterstitial plugin.TValue[bool]
	Type plugin.TValue[string]
	CreatedAt plugin.TValue[*time.Time]
	UpdatedAt plugin.TValue[*time.Time]
}

// createCloudflareOneApp creates a new instance of this resource
func createCloudflareOneApp(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlCloudflareOneApp{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	if res.__id == "" {
	res.__id, err = res.id()
		if err != nil {
			return nil, err
		}
	}

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("cloudflare.one.app", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlCloudflareOneApp) MqlName() string {
	return "cloudflare.one.app"
}

func (c *mqlCloudflareOneApp) MqlID() string {
	return c.__id
}

func (c *mqlCloudflareOneApp) GetId() *plugin.TValue[string] {
	return &c.Id
}

func (c *mqlCloudflareOneApp) GetAud() *plugin.TValue[string] {
	return &c.Aud
}

func (c *mqlCloudflareOneApp) GetName() *plugin.TValue[string] {
	return &c.Name
}

func (c *mqlCloudflareOneApp) GetDomain() *plugin.TValue[string] {
	return &c.Domain
}

func (c *mqlCloudflareOneApp) GetAllowedIdentityProviders() *plugin.TValue[[]interface{}] {
	return &c.AllowedIdentityProviders
}

func (c *mqlCloudflareOneApp) GetAppLauncherVisible() *plugin.TValue[bool] {
	return &c.AppLauncherVisible
}

func (c *mqlCloudflareOneApp) GetAutoRedirectToIdentity() *plugin.TValue[bool] {
	return &c.AutoRedirectToIdentity
}

func (c *mqlCloudflareOneApp) GetCorsHeaders() *plugin.TValue[*mqlCloudflareCorsHeaders] {
	return &c.CorsHeaders
}

func (c *mqlCloudflareOneApp) GetOptionsPreflightBypass() *plugin.TValue[bool] {
	return &c.OptionsPreflightBypass
}

func (c *mqlCloudflareOneApp) GetCustomDenyMessage() *plugin.TValue[string] {
	return &c.CustomDenyMessage
}

func (c *mqlCloudflareOneApp) GetCustomDenyUrl() *plugin.TValue[string] {
	return &c.CustomDenyUrl
}

func (c *mqlCloudflareOneApp) GetServiceAuth401Redirect() *plugin.TValue[bool] {
	return &c.ServiceAuth401Redirect
}

func (c *mqlCloudflareOneApp) GetEnableBindingCookie() *plugin.TValue[bool] {
	return &c.EnableBindingCookie
}

func (c *mqlCloudflareOneApp) GetHttpOnlyCookieAttribute() *plugin.TValue[bool] {
	return &c.HttpOnlyCookieAttribute
}

func (c *mqlCloudflareOneApp) GetSameSiteCookieAttribute() *plugin.TValue[string] {
	return &c.SameSiteCookieAttribute
}

func (c *mqlCloudflareOneApp) GetLogoUrl() *plugin.TValue[string] {
	return &c.LogoUrl
}

func (c *mqlCloudflareOneApp) GetSessionDuration() *plugin.TValue[string] {
	return &c.SessionDuration
}

func (c *mqlCloudflareOneApp) GetSkipInterstitial() *plugin.TValue[bool] {
	return &c.SkipInterstitial
}

func (c *mqlCloudflareOneApp) GetType() *plugin.TValue[string] {
	return &c.Type
}

func (c *mqlCloudflareOneApp) GetCreatedAt() *plugin.TValue[*time.Time] {
	return &c.CreatedAt
}

func (c *mqlCloudflareOneApp) GetUpdatedAt() *plugin.TValue[*time.Time] {
	return &c.UpdatedAt
}

// mqlCloudflareCorsHeaders for the cloudflare.corsHeaders resource
type mqlCloudflareCorsHeaders struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlCloudflareCorsHeadersInternal it will be used here
	AllowAllHeaders plugin.TValue[bool]
	AllowAllMethods plugin.TValue[bool]
	AllowAllOrigins plugin.TValue[bool]
	AllowCredentials plugin.TValue[bool]
	AllowedHeaders plugin.TValue[[]interface{}]
	AllowedMethods plugin.TValue[[]interface{}]
	AllowedOrigins plugin.TValue[[]interface{}]
	MaxAge plugin.TValue[int64]
}

// createCloudflareCorsHeaders creates a new instance of this resource
func createCloudflareCorsHeaders(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlCloudflareCorsHeaders{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	// to override __id implement: id() (string, error)

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("cloudflare.corsHeaders", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlCloudflareCorsHeaders) MqlName() string {
	return "cloudflare.corsHeaders"
}

func (c *mqlCloudflareCorsHeaders) MqlID() string {
	return c.__id
}

func (c *mqlCloudflareCorsHeaders) GetAllowAllHeaders() *plugin.TValue[bool] {
	return &c.AllowAllHeaders
}

func (c *mqlCloudflareCorsHeaders) GetAllowAllMethods() *plugin.TValue[bool] {
	return &c.AllowAllMethods
}

func (c *mqlCloudflareCorsHeaders) GetAllowAllOrigins() *plugin.TValue[bool] {
	return &c.AllowAllOrigins
}

func (c *mqlCloudflareCorsHeaders) GetAllowCredentials() *plugin.TValue[bool] {
	return &c.AllowCredentials
}

func (c *mqlCloudflareCorsHeaders) GetAllowedHeaders() *plugin.TValue[[]interface{}] {
	return &c.AllowedHeaders
}

func (c *mqlCloudflareCorsHeaders) GetAllowedMethods() *plugin.TValue[[]interface{}] {
	return &c.AllowedMethods
}

func (c *mqlCloudflareCorsHeaders) GetAllowedOrigins() *plugin.TValue[[]interface{}] {
	return &c.AllowedOrigins
}

func (c *mqlCloudflareCorsHeaders) GetMaxAge() *plugin.TValue[int64] {
	return &c.MaxAge
}

// mqlCloudflareOneIdp for the cloudflare.one.idp resource
type mqlCloudflareOneIdp struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlCloudflareOneIdpInternal it will be used here
	Id plugin.TValue[string]
	Name plugin.TValue[string]
	Type plugin.TValue[string]
}

// createCloudflareOneIdp creates a new instance of this resource
func createCloudflareOneIdp(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlCloudflareOneIdp{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	if res.__id == "" {
	res.__id, err = res.id()
		if err != nil {
			return nil, err
		}
	}

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("cloudflare.one.idp", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlCloudflareOneIdp) MqlName() string {
	return "cloudflare.one.idp"
}

func (c *mqlCloudflareOneIdp) MqlID() string {
	return c.__id
}

func (c *mqlCloudflareOneIdp) GetId() *plugin.TValue[string] {
	return &c.Id
}

func (c *mqlCloudflareOneIdp) GetName() *plugin.TValue[string] {
	return &c.Name
}

func (c *mqlCloudflareOneIdp) GetType() *plugin.TValue[string] {
	return &c.Type
}
