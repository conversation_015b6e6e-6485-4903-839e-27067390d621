// Copyright (c) Mondoo, Inc.
// SPDX-License-Identifier: BUSL-1.1

// Code generated by resources. DO NOT EDIT.

package resources

import (
	"errors"
	"time"

	"go.mondoo.com/cnquery/v11/llx"
	"go.mondoo.com/cnquery/v11/providers-sdk/v1/plugin"
	"go.mondoo.com/cnquery/v11/types"
)

var resourceFactories map[string]plugin.ResourceFactory

func init() {
	resourceFactories = map[string]plugin.ResourceFactory {
		"googleworkspace": {
			// to override args, implement: initGoogleworkspace(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createGoogleworkspace,
		},
		"googleworkspace.calendar": {
			// to override args, implement: initGoogleworkspaceCalendar(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createGoogleworkspaceCalendar,
		},
		"googleworkspace.calendar.aclRule": {
			// to override args, implement: initGoogleworkspaceCalendarAclRule(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createGoogleworkspaceCalendarAclRule,
		},
		"googleworkspace.calendar.aclRule.scope": {
			// to override args, implement: initGoogleworkspaceCalendarAclRuleScope(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createGoogleworkspaceCalendarAclRuleScope,
		},
		"googleworkspace.orgUnit": {
			// to override args, implement: initGoogleworkspaceOrgUnit(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createGoogleworkspaceOrgUnit,
		},
		"googleworkspace.domain": {
			// to override args, implement: initGoogleworkspaceDomain(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createGoogleworkspaceDomain,
		},
		"googleworkspace.user": {
			// to override args, implement: initGoogleworkspaceUser(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createGoogleworkspaceUser,
		},
		"googleworkspace.token": {
			// to override args, implement: initGoogleworkspaceToken(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createGoogleworkspaceToken,
		},
		"googleworkspace.connectedApp": {
			// to override args, implement: initGoogleworkspaceConnectedApp(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createGoogleworkspaceConnectedApp,
		},
		"googleworkspace.group": {
			// to override args, implement: initGoogleworkspaceGroup(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createGoogleworkspaceGroup,
		},
		"googleworkspace.member": {
			// to override args, implement: initGoogleworkspaceMember(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createGoogleworkspaceMember,
		},
		"googleworkspace.role": {
			// to override args, implement: initGoogleworkspaceRole(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createGoogleworkspaceRole,
		},
		"googleworkspace.report.apps": {
			// to override args, implement: initGoogleworkspaceReportApps(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createGoogleworkspaceReportApps,
		},
		"googleworkspace.report.activity": {
			// to override args, implement: initGoogleworkspaceReportActivity(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createGoogleworkspaceReportActivity,
		},
		"googleworkspace.report.users": {
			// to override args, implement: initGoogleworkspaceReportUsers(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createGoogleworkspaceReportUsers,
		},
		"googleworkspace.report.usage": {
			// to override args, implement: initGoogleworkspaceReportUsage(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createGoogleworkspaceReportUsage,
		},
	}
}

// NewResource is used by the runtime of this plugin to create new resources.
// Its arguments may be provided by users. This function is generally not
// used by initializing resources from recordings or from lists.
func NewResource(runtime *plugin.Runtime, name string, args map[string]*llx.RawData) (plugin.Resource, error) {
	f, ok := resourceFactories[name]
	if !ok {
		return nil, errors.New("cannot find resource " + name + " in this provider")
	}

	if f.Init != nil {
		cargs, res, err := f.Init(runtime, args)
		if err != nil {
			return res, err
		}

		if res != nil {
			id := name+"\x00"+res.MqlID()
			if x, ok := runtime.Resources.Get(id); ok {
				return x, nil
			}
			runtime.Resources.Set(id, res)
			return res, nil
		}

		args = cargs
	}

	res, err := f.Create(runtime, args)
	if err != nil {
		return nil, err
	}

	id := name+"\x00"+res.MqlID()
	if x, ok := runtime.Resources.Get(id); ok {
		return x, nil
	}

	runtime.Resources.Set(id, res)
	return res, nil
}

// CreateResource is used by the runtime of this plugin to create resources.
// Its arguments must be complete and pre-processed. This method is used
// for initializing resources from recordings or from lists.
func CreateResource(runtime *plugin.Runtime, name string, args map[string]*llx.RawData) (plugin.Resource, error) {
	f, ok := resourceFactories[name]
	if !ok {
		return nil, errors.New("cannot find resource " + name + " in this provider")
	}

	res, err := f.Create(runtime, args)
	if err != nil {
		return nil, err
	}

	id := name+"\x00"+res.MqlID()
	if x, ok := runtime.Resources.Get(id); ok {
		return x, nil
	}

	runtime.Resources.Set(id, res)
	return res, nil
}

var getDataFields = map[string]func(r plugin.Resource) *plugin.DataRes{
	"googleworkspace.orgUnits": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspace).GetOrgUnits()).ToDataRes(types.Array(types.Resource("googleworkspace.orgUnit")))
	},
	"googleworkspace.users": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspace).GetUsers()).ToDataRes(types.Array(types.Resource("googleworkspace.user")))
	},
	"googleworkspace.domains": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspace).GetDomains()).ToDataRes(types.Array(types.Resource("googleworkspace.domain")))
	},
	"googleworkspace.groups": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspace).GetGroups()).ToDataRes(types.Array(types.Resource("googleworkspace.group")))
	},
	"googleworkspace.roles": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspace).GetRoles()).ToDataRes(types.Array(types.Resource("googleworkspace.role")))
	},
	"googleworkspace.connectedApps": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspace).GetConnectedApps()).ToDataRes(types.Array(types.Resource("googleworkspace.connectedApp")))
	},
	"googleworkspace.calendars": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspace).GetCalendars()).ToDataRes(types.Array(types.Resource("googleworkspace.calendar")))
	},
	"googleworkspace.calendar.summary": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspaceCalendar).GetSummary()).ToDataRes(types.String)
	},
	"googleworkspace.calendar.summaryOverride": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspaceCalendar).GetSummaryOverride()).ToDataRes(types.String)
	},
	"googleworkspace.calendar.primary": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspaceCalendar).GetPrimary()).ToDataRes(types.Bool)
	},
	"googleworkspace.calendar.acl": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspaceCalendar).GetAcl()).ToDataRes(types.Array(types.Resource("googleworkspace.calendar.aclRule")))
	},
	"googleworkspace.calendar.aclRule.role": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspaceCalendarAclRule).GetRole()).ToDataRes(types.String)
	},
	"googleworkspace.calendar.aclRule.scope": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspaceCalendarAclRule).GetScope()).ToDataRes(types.Resource("googleworkspace.calendar.aclRule.scope"))
	},
	"googleworkspace.calendar.aclRule.scope.type": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspaceCalendarAclRuleScope).GetType()).ToDataRes(types.String)
	},
	"googleworkspace.calendar.aclRule.scope.value": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspaceCalendarAclRuleScope).GetValue()).ToDataRes(types.String)
	},
	"googleworkspace.orgUnit.id": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspaceOrgUnit).GetId()).ToDataRes(types.String)
	},
	"googleworkspace.orgUnit.name": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspaceOrgUnit).GetName()).ToDataRes(types.String)
	},
	"googleworkspace.orgUnit.description": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspaceOrgUnit).GetDescription()).ToDataRes(types.String)
	},
	"googleworkspace.domain.domainName": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspaceDomain).GetDomainName()).ToDataRes(types.String)
	},
	"googleworkspace.domain.isPrimary": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspaceDomain).GetIsPrimary()).ToDataRes(types.Bool)
	},
	"googleworkspace.domain.verified": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspaceDomain).GetVerified()).ToDataRes(types.Bool)
	},
	"googleworkspace.domain.creationTime": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspaceDomain).GetCreationTime()).ToDataRes(types.Time)
	},
	"googleworkspace.user.id": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspaceUser).GetId()).ToDataRes(types.String)
	},
	"googleworkspace.user.familyName": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspaceUser).GetFamilyName()).ToDataRes(types.String)
	},
	"googleworkspace.user.givenName": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspaceUser).GetGivenName()).ToDataRes(types.String)
	},
	"googleworkspace.user.fullName": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspaceUser).GetFullName()).ToDataRes(types.String)
	},
	"googleworkspace.user.primaryEmail": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspaceUser).GetPrimaryEmail()).ToDataRes(types.String)
	},
	"googleworkspace.user.recoveryEmail": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspaceUser).GetRecoveryEmail()).ToDataRes(types.String)
	},
	"googleworkspace.user.recoveryPhone": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspaceUser).GetRecoveryPhone()).ToDataRes(types.String)
	},
	"googleworkspace.user.agreedToTerms": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspaceUser).GetAgreedToTerms()).ToDataRes(types.Bool)
	},
	"googleworkspace.user.aliases": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspaceUser).GetAliases()).ToDataRes(types.Array(types.String))
	},
	"googleworkspace.user.suspended": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspaceUser).GetSuspended()).ToDataRes(types.Bool)
	},
	"googleworkspace.user.suspensionReason": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspaceUser).GetSuspensionReason()).ToDataRes(types.String)
	},
	"googleworkspace.user.archived": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspaceUser).GetArchived()).ToDataRes(types.Bool)
	},
	"googleworkspace.user.isAdmin": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspaceUser).GetIsAdmin()).ToDataRes(types.Bool)
	},
	"googleworkspace.user.isDelegatedAdmin": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspaceUser).GetIsDelegatedAdmin()).ToDataRes(types.Bool)
	},
	"googleworkspace.user.isEnforcedIn2Sv": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspaceUser).GetIsEnforcedIn2Sv()).ToDataRes(types.Bool)
	},
	"googleworkspace.user.isEnrolledIn2Sv": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspaceUser).GetIsEnrolledIn2Sv()).ToDataRes(types.Bool)
	},
	"googleworkspace.user.isMailboxSetup": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspaceUser).GetIsMailboxSetup()).ToDataRes(types.Bool)
	},
	"googleworkspace.user.lastLoginTime": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspaceUser).GetLastLoginTime()).ToDataRes(types.Time)
	},
	"googleworkspace.user.creationTime": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspaceUser).GetCreationTime()).ToDataRes(types.Time)
	},
	"googleworkspace.user.usageReport": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspaceUser).GetUsageReport()).ToDataRes(types.Resource("googleworkspace.report.usage"))
	},
	"googleworkspace.user.tokens": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspaceUser).GetTokens()).ToDataRes(types.Array(types.Resource("googleworkspace.token")))
	},
	"googleworkspace.token.anonymous": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspaceToken).GetAnonymous()).ToDataRes(types.Bool)
	},
	"googleworkspace.token.clientId": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspaceToken).GetClientId()).ToDataRes(types.String)
	},
	"googleworkspace.token.displayText": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspaceToken).GetDisplayText()).ToDataRes(types.String)
	},
	"googleworkspace.token.nativeApp": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspaceToken).GetNativeApp()).ToDataRes(types.Bool)
	},
	"googleworkspace.token.scopes": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspaceToken).GetScopes()).ToDataRes(types.Array(types.String))
	},
	"googleworkspace.token.userKey": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspaceToken).GetUserKey()).ToDataRes(types.String)
	},
	"googleworkspace.connectedApp.clientId": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspaceConnectedApp).GetClientId()).ToDataRes(types.String)
	},
	"googleworkspace.connectedApp.name": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspaceConnectedApp).GetName()).ToDataRes(types.String)
	},
	"googleworkspace.connectedApp.scopes": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspaceConnectedApp).GetScopes()).ToDataRes(types.Array(types.String))
	},
	"googleworkspace.connectedApp.users": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspaceConnectedApp).GetUsers()).ToDataRes(types.Array(types.Resource("googleworkspace.user")))
	},
	"googleworkspace.connectedApp.tokens": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspaceConnectedApp).GetTokens()).ToDataRes(types.Array(types.Resource("googleworkspace.token")))
	},
	"googleworkspace.group.id": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspaceGroup).GetId()).ToDataRes(types.String)
	},
	"googleworkspace.group.name": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspaceGroup).GetName()).ToDataRes(types.String)
	},
	"googleworkspace.group.email": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspaceGroup).GetEmail()).ToDataRes(types.String)
	},
	"googleworkspace.group.description": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspaceGroup).GetDescription()).ToDataRes(types.String)
	},
	"googleworkspace.group.aliases": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspaceGroup).GetAliases()).ToDataRes(types.Array(types.String))
	},
	"googleworkspace.group.directMembersCount": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspaceGroup).GetDirectMembersCount()).ToDataRes(types.Int)
	},
	"googleworkspace.group.adminCreated": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspaceGroup).GetAdminCreated()).ToDataRes(types.Bool)
	},
	"googleworkspace.group.members": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspaceGroup).GetMembers()).ToDataRes(types.Array(types.Resource("googleworkspace.member")))
	},
	"googleworkspace.group.settings": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspaceGroup).GetSettings()).ToDataRes(types.Dict)
	},
	"googleworkspace.group.securitySettings": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspaceGroup).GetSecuritySettings()).ToDataRes(types.Dict)
	},
	"googleworkspace.member.id": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspaceMember).GetId()).ToDataRes(types.String)
	},
	"googleworkspace.member.email": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspaceMember).GetEmail()).ToDataRes(types.String)
	},
	"googleworkspace.member.status": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspaceMember).GetStatus()).ToDataRes(types.String)
	},
	"googleworkspace.member.type": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspaceMember).GetType()).ToDataRes(types.String)
	},
	"googleworkspace.member.user": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspaceMember).GetUser()).ToDataRes(types.Resource("googleworkspace.user"))
	},
	"googleworkspace.role.id": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspaceRole).GetId()).ToDataRes(types.Int)
	},
	"googleworkspace.role.name": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspaceRole).GetName()).ToDataRes(types.String)
	},
	"googleworkspace.role.description": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspaceRole).GetDescription()).ToDataRes(types.String)
	},
	"googleworkspace.role.isSystemRole": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspaceRole).GetIsSystemRole()).ToDataRes(types.Bool)
	},
	"googleworkspace.role.isSuperAdminRole": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspaceRole).GetIsSuperAdminRole()).ToDataRes(types.Bool)
	},
	"googleworkspace.role.privileges": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspaceRole).GetPrivileges()).ToDataRes(types.Array(types.Dict))
	},
	"googleworkspace.report.apps.drive": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspaceReportApps).GetDrive()).ToDataRes(types.Array(types.Resource("googleworkspace.report.activity")))
	},
	"googleworkspace.report.apps.admin": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspaceReportApps).GetAdmin()).ToDataRes(types.Array(types.Resource("googleworkspace.report.activity")))
	},
	"googleworkspace.report.activity.id": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspaceReportActivity).GetId()).ToDataRes(types.Int)
	},
	"googleworkspace.report.activity.ipAddress": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspaceReportActivity).GetIpAddress()).ToDataRes(types.String)
	},
	"googleworkspace.report.activity.ownerDomain": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspaceReportActivity).GetOwnerDomain()).ToDataRes(types.String)
	},
	"googleworkspace.report.activity.actor": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspaceReportActivity).GetActor()).ToDataRes(types.Dict)
	},
	"googleworkspace.report.activity.events": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspaceReportActivity).GetEvents()).ToDataRes(types.Array(types.Dict))
	},
	"googleworkspace.report.users.list": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspaceReportUsers).GetList()).ToDataRes(types.Array(types.Resource("googleworkspace.report.usage")))
	},
	"googleworkspace.report.usage.customerId": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspaceReportUsage).GetCustomerId()).ToDataRes(types.String)
	},
	"googleworkspace.report.usage.entityId": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspaceReportUsage).GetEntityId()).ToDataRes(types.String)
	},
	"googleworkspace.report.usage.profileId": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspaceReportUsage).GetProfileId()).ToDataRes(types.String)
	},
	"googleworkspace.report.usage.type": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspaceReportUsage).GetType()).ToDataRes(types.String)
	},
	"googleworkspace.report.usage.userEmail": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspaceReportUsage).GetUserEmail()).ToDataRes(types.String)
	},
	"googleworkspace.report.usage.date": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspaceReportUsage).GetDate()).ToDataRes(types.Time)
	},
	"googleworkspace.report.usage.parameters": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspaceReportUsage).GetParameters()).ToDataRes(types.Array(types.Dict))
	},
	"googleworkspace.report.usage.account": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspaceReportUsage).GetAccount()).ToDataRes(types.Dict)
	},
	"googleworkspace.report.usage.security": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspaceReportUsage).GetSecurity()).ToDataRes(types.Dict)
	},
	"googleworkspace.report.usage.appUsage": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGoogleworkspaceReportUsage).GetAppUsage()).ToDataRes(types.Dict)
	},
}

func GetData(resource plugin.Resource, field string, args map[string]*llx.RawData) *plugin.DataRes {
	f, ok := getDataFields[resource.MqlName()+"."+field]
	if !ok {
		return &plugin.DataRes{Error: "cannot find '" + field + "' in resource '" + resource.MqlName() + "'"}
	}

	return f(resource)
}

var setDataFields = map[string]func(r plugin.Resource, v *llx.RawData) bool {
	"googleworkspace.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlGoogleworkspace).__id, ok = v.Value.(string)
			return
		},
	"googleworkspace.orgUnits": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspace).OrgUnits, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"googleworkspace.users": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspace).Users, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"googleworkspace.domains": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspace).Domains, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"googleworkspace.groups": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspace).Groups, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"googleworkspace.roles": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspace).Roles, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"googleworkspace.connectedApps": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspace).ConnectedApps, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"googleworkspace.calendars": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspace).Calendars, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"googleworkspace.calendar.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlGoogleworkspaceCalendar).__id, ok = v.Value.(string)
			return
		},
	"googleworkspace.calendar.summary": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspaceCalendar).Summary, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"googleworkspace.calendar.summaryOverride": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspaceCalendar).SummaryOverride, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"googleworkspace.calendar.primary": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspaceCalendar).Primary, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"googleworkspace.calendar.acl": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspaceCalendar).Acl, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"googleworkspace.calendar.aclRule.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlGoogleworkspaceCalendarAclRule).__id, ok = v.Value.(string)
			return
		},
	"googleworkspace.calendar.aclRule.role": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspaceCalendarAclRule).Role, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"googleworkspace.calendar.aclRule.scope": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspaceCalendarAclRule).Scope, ok = plugin.RawToTValue[*mqlGoogleworkspaceCalendarAclRuleScope](v.Value, v.Error)
		return
	},
	"googleworkspace.calendar.aclRule.scope.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlGoogleworkspaceCalendarAclRuleScope).__id, ok = v.Value.(string)
			return
		},
	"googleworkspace.calendar.aclRule.scope.type": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspaceCalendarAclRuleScope).Type, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"googleworkspace.calendar.aclRule.scope.value": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspaceCalendarAclRuleScope).Value, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"googleworkspace.orgUnit.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlGoogleworkspaceOrgUnit).__id, ok = v.Value.(string)
			return
		},
	"googleworkspace.orgUnit.id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspaceOrgUnit).Id, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"googleworkspace.orgUnit.name": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspaceOrgUnit).Name, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"googleworkspace.orgUnit.description": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspaceOrgUnit).Description, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"googleworkspace.domain.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlGoogleworkspaceDomain).__id, ok = v.Value.(string)
			return
		},
	"googleworkspace.domain.domainName": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspaceDomain).DomainName, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"googleworkspace.domain.isPrimary": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspaceDomain).IsPrimary, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"googleworkspace.domain.verified": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspaceDomain).Verified, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"googleworkspace.domain.creationTime": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspaceDomain).CreationTime, ok = plugin.RawToTValue[*time.Time](v.Value, v.Error)
		return
	},
	"googleworkspace.user.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlGoogleworkspaceUser).__id, ok = v.Value.(string)
			return
		},
	"googleworkspace.user.id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspaceUser).Id, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"googleworkspace.user.familyName": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspaceUser).FamilyName, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"googleworkspace.user.givenName": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspaceUser).GivenName, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"googleworkspace.user.fullName": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspaceUser).FullName, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"googleworkspace.user.primaryEmail": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspaceUser).PrimaryEmail, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"googleworkspace.user.recoveryEmail": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspaceUser).RecoveryEmail, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"googleworkspace.user.recoveryPhone": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspaceUser).RecoveryPhone, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"googleworkspace.user.agreedToTerms": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspaceUser).AgreedToTerms, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"googleworkspace.user.aliases": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspaceUser).Aliases, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"googleworkspace.user.suspended": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspaceUser).Suspended, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"googleworkspace.user.suspensionReason": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspaceUser).SuspensionReason, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"googleworkspace.user.archived": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspaceUser).Archived, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"googleworkspace.user.isAdmin": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspaceUser).IsAdmin, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"googleworkspace.user.isDelegatedAdmin": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspaceUser).IsDelegatedAdmin, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"googleworkspace.user.isEnforcedIn2Sv": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspaceUser).IsEnforcedIn2Sv, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"googleworkspace.user.isEnrolledIn2Sv": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspaceUser).IsEnrolledIn2Sv, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"googleworkspace.user.isMailboxSetup": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspaceUser).IsMailboxSetup, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"googleworkspace.user.lastLoginTime": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspaceUser).LastLoginTime, ok = plugin.RawToTValue[*time.Time](v.Value, v.Error)
		return
	},
	"googleworkspace.user.creationTime": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspaceUser).CreationTime, ok = plugin.RawToTValue[*time.Time](v.Value, v.Error)
		return
	},
	"googleworkspace.user.usageReport": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspaceUser).UsageReport, ok = plugin.RawToTValue[*mqlGoogleworkspaceReportUsage](v.Value, v.Error)
		return
	},
	"googleworkspace.user.tokens": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspaceUser).Tokens, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"googleworkspace.token.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlGoogleworkspaceToken).__id, ok = v.Value.(string)
			return
		},
	"googleworkspace.token.anonymous": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspaceToken).Anonymous, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"googleworkspace.token.clientId": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspaceToken).ClientId, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"googleworkspace.token.displayText": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspaceToken).DisplayText, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"googleworkspace.token.nativeApp": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspaceToken).NativeApp, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"googleworkspace.token.scopes": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspaceToken).Scopes, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"googleworkspace.token.userKey": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspaceToken).UserKey, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"googleworkspace.connectedApp.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlGoogleworkspaceConnectedApp).__id, ok = v.Value.(string)
			return
		},
	"googleworkspace.connectedApp.clientId": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspaceConnectedApp).ClientId, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"googleworkspace.connectedApp.name": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspaceConnectedApp).Name, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"googleworkspace.connectedApp.scopes": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspaceConnectedApp).Scopes, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"googleworkspace.connectedApp.users": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspaceConnectedApp).Users, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"googleworkspace.connectedApp.tokens": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspaceConnectedApp).Tokens, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"googleworkspace.group.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlGoogleworkspaceGroup).__id, ok = v.Value.(string)
			return
		},
	"googleworkspace.group.id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspaceGroup).Id, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"googleworkspace.group.name": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspaceGroup).Name, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"googleworkspace.group.email": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspaceGroup).Email, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"googleworkspace.group.description": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspaceGroup).Description, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"googleworkspace.group.aliases": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspaceGroup).Aliases, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"googleworkspace.group.directMembersCount": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspaceGroup).DirectMembersCount, ok = plugin.RawToTValue[int64](v.Value, v.Error)
		return
	},
	"googleworkspace.group.adminCreated": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspaceGroup).AdminCreated, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"googleworkspace.group.members": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspaceGroup).Members, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"googleworkspace.group.settings": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspaceGroup).Settings, ok = plugin.RawToTValue[interface{}](v.Value, v.Error)
		return
	},
	"googleworkspace.group.securitySettings": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspaceGroup).SecuritySettings, ok = plugin.RawToTValue[interface{}](v.Value, v.Error)
		return
	},
	"googleworkspace.member.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlGoogleworkspaceMember).__id, ok = v.Value.(string)
			return
		},
	"googleworkspace.member.id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspaceMember).Id, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"googleworkspace.member.email": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspaceMember).Email, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"googleworkspace.member.status": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspaceMember).Status, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"googleworkspace.member.type": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspaceMember).Type, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"googleworkspace.member.user": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspaceMember).User, ok = plugin.RawToTValue[*mqlGoogleworkspaceUser](v.Value, v.Error)
		return
	},
	"googleworkspace.role.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlGoogleworkspaceRole).__id, ok = v.Value.(string)
			return
		},
	"googleworkspace.role.id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspaceRole).Id, ok = plugin.RawToTValue[int64](v.Value, v.Error)
		return
	},
	"googleworkspace.role.name": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspaceRole).Name, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"googleworkspace.role.description": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspaceRole).Description, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"googleworkspace.role.isSystemRole": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspaceRole).IsSystemRole, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"googleworkspace.role.isSuperAdminRole": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspaceRole).IsSuperAdminRole, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"googleworkspace.role.privileges": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspaceRole).Privileges, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"googleworkspace.report.apps.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlGoogleworkspaceReportApps).__id, ok = v.Value.(string)
			return
		},
	"googleworkspace.report.apps.drive": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspaceReportApps).Drive, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"googleworkspace.report.apps.admin": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspaceReportApps).Admin, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"googleworkspace.report.activity.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlGoogleworkspaceReportActivity).__id, ok = v.Value.(string)
			return
		},
	"googleworkspace.report.activity.id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspaceReportActivity).Id, ok = plugin.RawToTValue[int64](v.Value, v.Error)
		return
	},
	"googleworkspace.report.activity.ipAddress": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspaceReportActivity).IpAddress, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"googleworkspace.report.activity.ownerDomain": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspaceReportActivity).OwnerDomain, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"googleworkspace.report.activity.actor": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspaceReportActivity).Actor, ok = plugin.RawToTValue[interface{}](v.Value, v.Error)
		return
	},
	"googleworkspace.report.activity.events": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspaceReportActivity).Events, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"googleworkspace.report.users.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlGoogleworkspaceReportUsers).__id, ok = v.Value.(string)
			return
		},
	"googleworkspace.report.users.list": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspaceReportUsers).List, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"googleworkspace.report.usage.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlGoogleworkspaceReportUsage).__id, ok = v.Value.(string)
			return
		},
	"googleworkspace.report.usage.customerId": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspaceReportUsage).CustomerId, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"googleworkspace.report.usage.entityId": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspaceReportUsage).EntityId, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"googleworkspace.report.usage.profileId": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspaceReportUsage).ProfileId, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"googleworkspace.report.usage.type": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspaceReportUsage).Type, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"googleworkspace.report.usage.userEmail": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspaceReportUsage).UserEmail, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"googleworkspace.report.usage.date": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspaceReportUsage).Date, ok = plugin.RawToTValue[*time.Time](v.Value, v.Error)
		return
	},
	"googleworkspace.report.usage.parameters": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspaceReportUsage).Parameters, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"googleworkspace.report.usage.account": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspaceReportUsage).Account, ok = plugin.RawToTValue[interface{}](v.Value, v.Error)
		return
	},
	"googleworkspace.report.usage.security": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspaceReportUsage).Security, ok = plugin.RawToTValue[interface{}](v.Value, v.Error)
		return
	},
	"googleworkspace.report.usage.appUsage": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGoogleworkspaceReportUsage).AppUsage, ok = plugin.RawToTValue[interface{}](v.Value, v.Error)
		return
	},
}

func SetData(resource plugin.Resource, field string, val *llx.RawData) error {
	f, ok := setDataFields[resource.MqlName() + "." + field]
	if !ok {
		return errors.New("[google-workspace] cannot set '"+field+"' in resource '"+resource.MqlName()+"', field not found")
	}

	if ok := f(resource, val); !ok {
		return errors.New("[google-workspace] cannot set '"+field+"' in resource '"+resource.MqlName()+"', type does not match")
	}
	return nil
}

func SetAllData(resource plugin.Resource, args map[string]*llx.RawData) error {
	var err error
	for k, v := range args {
		if err = SetData(resource, k, v); err != nil {
			return err
		}
	}
	return nil
}

// mqlGoogleworkspace for the googleworkspace resource
type mqlGoogleworkspace struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlGoogleworkspaceInternal it will be used here
	OrgUnits plugin.TValue[[]interface{}]
	Users plugin.TValue[[]interface{}]
	Domains plugin.TValue[[]interface{}]
	Groups plugin.TValue[[]interface{}]
	Roles plugin.TValue[[]interface{}]
	ConnectedApps plugin.TValue[[]interface{}]
	Calendars plugin.TValue[[]interface{}]
}

// createGoogleworkspace creates a new instance of this resource
func createGoogleworkspace(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlGoogleworkspace{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	if res.__id == "" {
	res.__id, err = res.id()
		if err != nil {
			return nil, err
		}
	}

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("googleworkspace", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlGoogleworkspace) MqlName() string {
	return "googleworkspace"
}

func (c *mqlGoogleworkspace) MqlID() string {
	return c.__id
}

func (c *mqlGoogleworkspace) GetOrgUnits() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.OrgUnits, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("googleworkspace", c.__id, "orgUnits")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.orgUnits()
	})
}

func (c *mqlGoogleworkspace) GetUsers() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.Users, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("googleworkspace", c.__id, "users")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.users()
	})
}

func (c *mqlGoogleworkspace) GetDomains() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.Domains, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("googleworkspace", c.__id, "domains")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.domains()
	})
}

func (c *mqlGoogleworkspace) GetGroups() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.Groups, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("googleworkspace", c.__id, "groups")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.groups()
	})
}

func (c *mqlGoogleworkspace) GetRoles() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.Roles, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("googleworkspace", c.__id, "roles")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.roles()
	})
}

func (c *mqlGoogleworkspace) GetConnectedApps() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.ConnectedApps, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("googleworkspace", c.__id, "connectedApps")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.connectedApps()
	})
}

func (c *mqlGoogleworkspace) GetCalendars() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.Calendars, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("googleworkspace", c.__id, "calendars")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.calendars()
	})
}

// mqlGoogleworkspaceCalendar for the googleworkspace.calendar resource
type mqlGoogleworkspaceCalendar struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlGoogleworkspaceCalendarInternal it will be used here
	Summary plugin.TValue[string]
	SummaryOverride plugin.TValue[string]
	Primary plugin.TValue[bool]
	Acl plugin.TValue[[]interface{}]
}

// createGoogleworkspaceCalendar creates a new instance of this resource
func createGoogleworkspaceCalendar(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlGoogleworkspaceCalendar{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	// to override __id implement: id() (string, error)

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("googleworkspace.calendar", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlGoogleworkspaceCalendar) MqlName() string {
	return "googleworkspace.calendar"
}

func (c *mqlGoogleworkspaceCalendar) MqlID() string {
	return c.__id
}

func (c *mqlGoogleworkspaceCalendar) GetSummary() *plugin.TValue[string] {
	return &c.Summary
}

func (c *mqlGoogleworkspaceCalendar) GetSummaryOverride() *plugin.TValue[string] {
	return &c.SummaryOverride
}

func (c *mqlGoogleworkspaceCalendar) GetPrimary() *plugin.TValue[bool] {
	return &c.Primary
}

func (c *mqlGoogleworkspaceCalendar) GetAcl() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.Acl, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("googleworkspace.calendar", c.__id, "acl")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.acl()
	})
}

// mqlGoogleworkspaceCalendarAclRule for the googleworkspace.calendar.aclRule resource
type mqlGoogleworkspaceCalendarAclRule struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlGoogleworkspaceCalendarAclRuleInternal it will be used here
	Role plugin.TValue[string]
	Scope plugin.TValue[*mqlGoogleworkspaceCalendarAclRuleScope]
}

// createGoogleworkspaceCalendarAclRule creates a new instance of this resource
func createGoogleworkspaceCalendarAclRule(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlGoogleworkspaceCalendarAclRule{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	// to override __id implement: id() (string, error)

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("googleworkspace.calendar.aclRule", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlGoogleworkspaceCalendarAclRule) MqlName() string {
	return "googleworkspace.calendar.aclRule"
}

func (c *mqlGoogleworkspaceCalendarAclRule) MqlID() string {
	return c.__id
}

func (c *mqlGoogleworkspaceCalendarAclRule) GetRole() *plugin.TValue[string] {
	return &c.Role
}

func (c *mqlGoogleworkspaceCalendarAclRule) GetScope() *plugin.TValue[*mqlGoogleworkspaceCalendarAclRuleScope] {
	return &c.Scope
}

// mqlGoogleworkspaceCalendarAclRuleScope for the googleworkspace.calendar.aclRule.scope resource
type mqlGoogleworkspaceCalendarAclRuleScope struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlGoogleworkspaceCalendarAclRuleScopeInternal it will be used here
	Type plugin.TValue[string]
	Value plugin.TValue[string]
}

// createGoogleworkspaceCalendarAclRuleScope creates a new instance of this resource
func createGoogleworkspaceCalendarAclRuleScope(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlGoogleworkspaceCalendarAclRuleScope{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	// to override __id implement: id() (string, error)

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("googleworkspace.calendar.aclRule.scope", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlGoogleworkspaceCalendarAclRuleScope) MqlName() string {
	return "googleworkspace.calendar.aclRule.scope"
}

func (c *mqlGoogleworkspaceCalendarAclRuleScope) MqlID() string {
	return c.__id
}

func (c *mqlGoogleworkspaceCalendarAclRuleScope) GetType() *plugin.TValue[string] {
	return &c.Type
}

func (c *mqlGoogleworkspaceCalendarAclRuleScope) GetValue() *plugin.TValue[string] {
	return &c.Value
}

// mqlGoogleworkspaceOrgUnit for the googleworkspace.orgUnit resource
type mqlGoogleworkspaceOrgUnit struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlGoogleworkspaceOrgUnitInternal it will be used here
	Id plugin.TValue[string]
	Name plugin.TValue[string]
	Description plugin.TValue[string]
}

// createGoogleworkspaceOrgUnit creates a new instance of this resource
func createGoogleworkspaceOrgUnit(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlGoogleworkspaceOrgUnit{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	if res.__id == "" {
	res.__id, err = res.id()
		if err != nil {
			return nil, err
		}
	}

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("googleworkspace.orgUnit", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlGoogleworkspaceOrgUnit) MqlName() string {
	return "googleworkspace.orgUnit"
}

func (c *mqlGoogleworkspaceOrgUnit) MqlID() string {
	return c.__id
}

func (c *mqlGoogleworkspaceOrgUnit) GetId() *plugin.TValue[string] {
	return &c.Id
}

func (c *mqlGoogleworkspaceOrgUnit) GetName() *plugin.TValue[string] {
	return &c.Name
}

func (c *mqlGoogleworkspaceOrgUnit) GetDescription() *plugin.TValue[string] {
	return &c.Description
}

// mqlGoogleworkspaceDomain for the googleworkspace.domain resource
type mqlGoogleworkspaceDomain struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlGoogleworkspaceDomainInternal it will be used here
	DomainName plugin.TValue[string]
	IsPrimary plugin.TValue[bool]
	Verified plugin.TValue[bool]
	CreationTime plugin.TValue[*time.Time]
}

// createGoogleworkspaceDomain creates a new instance of this resource
func createGoogleworkspaceDomain(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlGoogleworkspaceDomain{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	if res.__id == "" {
	res.__id, err = res.id()
		if err != nil {
			return nil, err
		}
	}

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("googleworkspace.domain", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlGoogleworkspaceDomain) MqlName() string {
	return "googleworkspace.domain"
}

func (c *mqlGoogleworkspaceDomain) MqlID() string {
	return c.__id
}

func (c *mqlGoogleworkspaceDomain) GetDomainName() *plugin.TValue[string] {
	return &c.DomainName
}

func (c *mqlGoogleworkspaceDomain) GetIsPrimary() *plugin.TValue[bool] {
	return &c.IsPrimary
}

func (c *mqlGoogleworkspaceDomain) GetVerified() *plugin.TValue[bool] {
	return &c.Verified
}

func (c *mqlGoogleworkspaceDomain) GetCreationTime() *plugin.TValue[*time.Time] {
	return &c.CreationTime
}

// mqlGoogleworkspaceUser for the googleworkspace.user resource
type mqlGoogleworkspaceUser struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlGoogleworkspaceUserInternal it will be used here
	Id plugin.TValue[string]
	FamilyName plugin.TValue[string]
	GivenName plugin.TValue[string]
	FullName plugin.TValue[string]
	PrimaryEmail plugin.TValue[string]
	RecoveryEmail plugin.TValue[string]
	RecoveryPhone plugin.TValue[string]
	AgreedToTerms plugin.TValue[bool]
	Aliases plugin.TValue[[]interface{}]
	Suspended plugin.TValue[bool]
	SuspensionReason plugin.TValue[string]
	Archived plugin.TValue[bool]
	IsAdmin plugin.TValue[bool]
	IsDelegatedAdmin plugin.TValue[bool]
	IsEnforcedIn2Sv plugin.TValue[bool]
	IsEnrolledIn2Sv plugin.TValue[bool]
	IsMailboxSetup plugin.TValue[bool]
	LastLoginTime plugin.TValue[*time.Time]
	CreationTime plugin.TValue[*time.Time]
	UsageReport plugin.TValue[*mqlGoogleworkspaceReportUsage]
	Tokens plugin.TValue[[]interface{}]
}

// createGoogleworkspaceUser creates a new instance of this resource
func createGoogleworkspaceUser(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlGoogleworkspaceUser{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	if res.__id == "" {
	res.__id, err = res.id()
		if err != nil {
			return nil, err
		}
	}

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("googleworkspace.user", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlGoogleworkspaceUser) MqlName() string {
	return "googleworkspace.user"
}

func (c *mqlGoogleworkspaceUser) MqlID() string {
	return c.__id
}

func (c *mqlGoogleworkspaceUser) GetId() *plugin.TValue[string] {
	return &c.Id
}

func (c *mqlGoogleworkspaceUser) GetFamilyName() *plugin.TValue[string] {
	return &c.FamilyName
}

func (c *mqlGoogleworkspaceUser) GetGivenName() *plugin.TValue[string] {
	return &c.GivenName
}

func (c *mqlGoogleworkspaceUser) GetFullName() *plugin.TValue[string] {
	return &c.FullName
}

func (c *mqlGoogleworkspaceUser) GetPrimaryEmail() *plugin.TValue[string] {
	return &c.PrimaryEmail
}

func (c *mqlGoogleworkspaceUser) GetRecoveryEmail() *plugin.TValue[string] {
	return &c.RecoveryEmail
}

func (c *mqlGoogleworkspaceUser) GetRecoveryPhone() *plugin.TValue[string] {
	return &c.RecoveryPhone
}

func (c *mqlGoogleworkspaceUser) GetAgreedToTerms() *plugin.TValue[bool] {
	return &c.AgreedToTerms
}

func (c *mqlGoogleworkspaceUser) GetAliases() *plugin.TValue[[]interface{}] {
	return &c.Aliases
}

func (c *mqlGoogleworkspaceUser) GetSuspended() *plugin.TValue[bool] {
	return &c.Suspended
}

func (c *mqlGoogleworkspaceUser) GetSuspensionReason() *plugin.TValue[string] {
	return &c.SuspensionReason
}

func (c *mqlGoogleworkspaceUser) GetArchived() *plugin.TValue[bool] {
	return &c.Archived
}

func (c *mqlGoogleworkspaceUser) GetIsAdmin() *plugin.TValue[bool] {
	return &c.IsAdmin
}

func (c *mqlGoogleworkspaceUser) GetIsDelegatedAdmin() *plugin.TValue[bool] {
	return &c.IsDelegatedAdmin
}

func (c *mqlGoogleworkspaceUser) GetIsEnforcedIn2Sv() *plugin.TValue[bool] {
	return &c.IsEnforcedIn2Sv
}

func (c *mqlGoogleworkspaceUser) GetIsEnrolledIn2Sv() *plugin.TValue[bool] {
	return &c.IsEnrolledIn2Sv
}

func (c *mqlGoogleworkspaceUser) GetIsMailboxSetup() *plugin.TValue[bool] {
	return &c.IsMailboxSetup
}

func (c *mqlGoogleworkspaceUser) GetLastLoginTime() *plugin.TValue[*time.Time] {
	return &c.LastLoginTime
}

func (c *mqlGoogleworkspaceUser) GetCreationTime() *plugin.TValue[*time.Time] {
	return &c.CreationTime
}

func (c *mqlGoogleworkspaceUser) GetUsageReport() *plugin.TValue[*mqlGoogleworkspaceReportUsage] {
	return plugin.GetOrCompute[*mqlGoogleworkspaceReportUsage](&c.UsageReport, func() (*mqlGoogleworkspaceReportUsage, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("googleworkspace.user", c.__id, "usageReport")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.(*mqlGoogleworkspaceReportUsage), nil
			}
		}

		return c.usageReport()
	})
}

func (c *mqlGoogleworkspaceUser) GetTokens() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.Tokens, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("googleworkspace.user", c.__id, "tokens")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.tokens()
	})
}

// mqlGoogleworkspaceToken for the googleworkspace.token resource
type mqlGoogleworkspaceToken struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlGoogleworkspaceTokenInternal it will be used here
	Anonymous plugin.TValue[bool]
	ClientId plugin.TValue[string]
	DisplayText plugin.TValue[string]
	NativeApp plugin.TValue[bool]
	Scopes plugin.TValue[[]interface{}]
	UserKey plugin.TValue[string]
}

// createGoogleworkspaceToken creates a new instance of this resource
func createGoogleworkspaceToken(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlGoogleworkspaceToken{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	if res.__id == "" {
	res.__id, err = res.id()
		if err != nil {
			return nil, err
		}
	}

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("googleworkspace.token", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlGoogleworkspaceToken) MqlName() string {
	return "googleworkspace.token"
}

func (c *mqlGoogleworkspaceToken) MqlID() string {
	return c.__id
}

func (c *mqlGoogleworkspaceToken) GetAnonymous() *plugin.TValue[bool] {
	return &c.Anonymous
}

func (c *mqlGoogleworkspaceToken) GetClientId() *plugin.TValue[string] {
	return &c.ClientId
}

func (c *mqlGoogleworkspaceToken) GetDisplayText() *plugin.TValue[string] {
	return &c.DisplayText
}

func (c *mqlGoogleworkspaceToken) GetNativeApp() *plugin.TValue[bool] {
	return &c.NativeApp
}

func (c *mqlGoogleworkspaceToken) GetScopes() *plugin.TValue[[]interface{}] {
	return &c.Scopes
}

func (c *mqlGoogleworkspaceToken) GetUserKey() *plugin.TValue[string] {
	return &c.UserKey
}

// mqlGoogleworkspaceConnectedApp for the googleworkspace.connectedApp resource
type mqlGoogleworkspaceConnectedApp struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlGoogleworkspaceConnectedAppInternal it will be used here
	ClientId plugin.TValue[string]
	Name plugin.TValue[string]
	Scopes plugin.TValue[[]interface{}]
	Users plugin.TValue[[]interface{}]
	Tokens plugin.TValue[[]interface{}]
}

// createGoogleworkspaceConnectedApp creates a new instance of this resource
func createGoogleworkspaceConnectedApp(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlGoogleworkspaceConnectedApp{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	if res.__id == "" {
	res.__id, err = res.id()
		if err != nil {
			return nil, err
		}
	}

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("googleworkspace.connectedApp", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlGoogleworkspaceConnectedApp) MqlName() string {
	return "googleworkspace.connectedApp"
}

func (c *mqlGoogleworkspaceConnectedApp) MqlID() string {
	return c.__id
}

func (c *mqlGoogleworkspaceConnectedApp) GetClientId() *plugin.TValue[string] {
	return &c.ClientId
}

func (c *mqlGoogleworkspaceConnectedApp) GetName() *plugin.TValue[string] {
	return &c.Name
}

func (c *mqlGoogleworkspaceConnectedApp) GetScopes() *plugin.TValue[[]interface{}] {
	return &c.Scopes
}

func (c *mqlGoogleworkspaceConnectedApp) GetUsers() *plugin.TValue[[]interface{}] {
	return &c.Users
}

func (c *mqlGoogleworkspaceConnectedApp) GetTokens() *plugin.TValue[[]interface{}] {
	return &c.Tokens
}

// mqlGoogleworkspaceGroup for the googleworkspace.group resource
type mqlGoogleworkspaceGroup struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlGoogleworkspaceGroupInternal it will be used here
	Id plugin.TValue[string]
	Name plugin.TValue[string]
	Email plugin.TValue[string]
	Description plugin.TValue[string]
	Aliases plugin.TValue[[]interface{}]
	DirectMembersCount plugin.TValue[int64]
	AdminCreated plugin.TValue[bool]
	Members plugin.TValue[[]interface{}]
	Settings plugin.TValue[interface{}]
	SecuritySettings plugin.TValue[interface{}]
}

// createGoogleworkspaceGroup creates a new instance of this resource
func createGoogleworkspaceGroup(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlGoogleworkspaceGroup{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	if res.__id == "" {
	res.__id, err = res.id()
		if err != nil {
			return nil, err
		}
	}

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("googleworkspace.group", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlGoogleworkspaceGroup) MqlName() string {
	return "googleworkspace.group"
}

func (c *mqlGoogleworkspaceGroup) MqlID() string {
	return c.__id
}

func (c *mqlGoogleworkspaceGroup) GetId() *plugin.TValue[string] {
	return &c.Id
}

func (c *mqlGoogleworkspaceGroup) GetName() *plugin.TValue[string] {
	return &c.Name
}

func (c *mqlGoogleworkspaceGroup) GetEmail() *plugin.TValue[string] {
	return &c.Email
}

func (c *mqlGoogleworkspaceGroup) GetDescription() *plugin.TValue[string] {
	return &c.Description
}

func (c *mqlGoogleworkspaceGroup) GetAliases() *plugin.TValue[[]interface{}] {
	return &c.Aliases
}

func (c *mqlGoogleworkspaceGroup) GetDirectMembersCount() *plugin.TValue[int64] {
	return &c.DirectMembersCount
}

func (c *mqlGoogleworkspaceGroup) GetAdminCreated() *plugin.TValue[bool] {
	return &c.AdminCreated
}

func (c *mqlGoogleworkspaceGroup) GetMembers() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.Members, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("googleworkspace.group", c.__id, "members")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.members()
	})
}

func (c *mqlGoogleworkspaceGroup) GetSettings() *plugin.TValue[interface{}] {
	return plugin.GetOrCompute[interface{}](&c.Settings, func() (interface{}, error) {
		return c.settings()
	})
}

func (c *mqlGoogleworkspaceGroup) GetSecuritySettings() *plugin.TValue[interface{}] {
	return plugin.GetOrCompute[interface{}](&c.SecuritySettings, func() (interface{}, error) {
		return c.securitySettings()
	})
}

// mqlGoogleworkspaceMember for the googleworkspace.member resource
type mqlGoogleworkspaceMember struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlGoogleworkspaceMemberInternal it will be used here
	Id plugin.TValue[string]
	Email plugin.TValue[string]
	Status plugin.TValue[string]
	Type plugin.TValue[string]
	User plugin.TValue[*mqlGoogleworkspaceUser]
}

// createGoogleworkspaceMember creates a new instance of this resource
func createGoogleworkspaceMember(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlGoogleworkspaceMember{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	if res.__id == "" {
	res.__id, err = res.id()
		if err != nil {
			return nil, err
		}
	}

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("googleworkspace.member", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlGoogleworkspaceMember) MqlName() string {
	return "googleworkspace.member"
}

func (c *mqlGoogleworkspaceMember) MqlID() string {
	return c.__id
}

func (c *mqlGoogleworkspaceMember) GetId() *plugin.TValue[string] {
	return &c.Id
}

func (c *mqlGoogleworkspaceMember) GetEmail() *plugin.TValue[string] {
	return &c.Email
}

func (c *mqlGoogleworkspaceMember) GetStatus() *plugin.TValue[string] {
	return &c.Status
}

func (c *mqlGoogleworkspaceMember) GetType() *plugin.TValue[string] {
	return &c.Type
}

func (c *mqlGoogleworkspaceMember) GetUser() *plugin.TValue[*mqlGoogleworkspaceUser] {
	return plugin.GetOrCompute[*mqlGoogleworkspaceUser](&c.User, func() (*mqlGoogleworkspaceUser, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("googleworkspace.member", c.__id, "user")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.(*mqlGoogleworkspaceUser), nil
			}
		}

		return c.user()
	})
}

// mqlGoogleworkspaceRole for the googleworkspace.role resource
type mqlGoogleworkspaceRole struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlGoogleworkspaceRoleInternal it will be used here
	Id plugin.TValue[int64]
	Name plugin.TValue[string]
	Description plugin.TValue[string]
	IsSystemRole plugin.TValue[bool]
	IsSuperAdminRole plugin.TValue[bool]
	Privileges plugin.TValue[[]interface{}]
}

// createGoogleworkspaceRole creates a new instance of this resource
func createGoogleworkspaceRole(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlGoogleworkspaceRole{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	if res.__id == "" {
	res.__id, err = res.id()
		if err != nil {
			return nil, err
		}
	}

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("googleworkspace.role", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlGoogleworkspaceRole) MqlName() string {
	return "googleworkspace.role"
}

func (c *mqlGoogleworkspaceRole) MqlID() string {
	return c.__id
}

func (c *mqlGoogleworkspaceRole) GetId() *plugin.TValue[int64] {
	return &c.Id
}

func (c *mqlGoogleworkspaceRole) GetName() *plugin.TValue[string] {
	return &c.Name
}

func (c *mqlGoogleworkspaceRole) GetDescription() *plugin.TValue[string] {
	return &c.Description
}

func (c *mqlGoogleworkspaceRole) GetIsSystemRole() *plugin.TValue[bool] {
	return &c.IsSystemRole
}

func (c *mqlGoogleworkspaceRole) GetIsSuperAdminRole() *plugin.TValue[bool] {
	return &c.IsSuperAdminRole
}

func (c *mqlGoogleworkspaceRole) GetPrivileges() *plugin.TValue[[]interface{}] {
	return &c.Privileges
}

// mqlGoogleworkspaceReportApps for the googleworkspace.report.apps resource
type mqlGoogleworkspaceReportApps struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlGoogleworkspaceReportAppsInternal it will be used here
	Drive plugin.TValue[[]interface{}]
	Admin plugin.TValue[[]interface{}]
}

// createGoogleworkspaceReportApps creates a new instance of this resource
func createGoogleworkspaceReportApps(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlGoogleworkspaceReportApps{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	if res.__id == "" {
	res.__id, err = res.id()
		if err != nil {
			return nil, err
		}
	}

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("googleworkspace.report.apps", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlGoogleworkspaceReportApps) MqlName() string {
	return "googleworkspace.report.apps"
}

func (c *mqlGoogleworkspaceReportApps) MqlID() string {
	return c.__id
}

func (c *mqlGoogleworkspaceReportApps) GetDrive() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.Drive, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("googleworkspace.report.apps", c.__id, "drive")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.drive()
	})
}

func (c *mqlGoogleworkspaceReportApps) GetAdmin() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.Admin, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("googleworkspace.report.apps", c.__id, "admin")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.admin()
	})
}

// mqlGoogleworkspaceReportActivity for the googleworkspace.report.activity resource
type mqlGoogleworkspaceReportActivity struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlGoogleworkspaceReportActivityInternal it will be used here
	Id plugin.TValue[int64]
	IpAddress plugin.TValue[string]
	OwnerDomain plugin.TValue[string]
	Actor plugin.TValue[interface{}]
	Events plugin.TValue[[]interface{}]
}

// createGoogleworkspaceReportActivity creates a new instance of this resource
func createGoogleworkspaceReportActivity(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlGoogleworkspaceReportActivity{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	if res.__id == "" {
	res.__id, err = res.id()
		if err != nil {
			return nil, err
		}
	}

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("googleworkspace.report.activity", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlGoogleworkspaceReportActivity) MqlName() string {
	return "googleworkspace.report.activity"
}

func (c *mqlGoogleworkspaceReportActivity) MqlID() string {
	return c.__id
}

func (c *mqlGoogleworkspaceReportActivity) GetId() *plugin.TValue[int64] {
	return &c.Id
}

func (c *mqlGoogleworkspaceReportActivity) GetIpAddress() *plugin.TValue[string] {
	return &c.IpAddress
}

func (c *mqlGoogleworkspaceReportActivity) GetOwnerDomain() *plugin.TValue[string] {
	return &c.OwnerDomain
}

func (c *mqlGoogleworkspaceReportActivity) GetActor() *plugin.TValue[interface{}] {
	return &c.Actor
}

func (c *mqlGoogleworkspaceReportActivity) GetEvents() *plugin.TValue[[]interface{}] {
	return &c.Events
}

// mqlGoogleworkspaceReportUsers for the googleworkspace.report.users resource
type mqlGoogleworkspaceReportUsers struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlGoogleworkspaceReportUsersInternal it will be used here
	List plugin.TValue[[]interface{}]
}

// createGoogleworkspaceReportUsers creates a new instance of this resource
func createGoogleworkspaceReportUsers(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlGoogleworkspaceReportUsers{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	if res.__id == "" {
	res.__id, err = res.id()
		if err != nil {
			return nil, err
		}
	}

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("googleworkspace.report.users", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlGoogleworkspaceReportUsers) MqlName() string {
	return "googleworkspace.report.users"
}

func (c *mqlGoogleworkspaceReportUsers) MqlID() string {
	return c.__id
}

func (c *mqlGoogleworkspaceReportUsers) GetList() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.List, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("googleworkspace.report.users", c.__id, "list")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.list()
	})
}

// mqlGoogleworkspaceReportUsage for the googleworkspace.report.usage resource
type mqlGoogleworkspaceReportUsage struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlGoogleworkspaceReportUsageInternal it will be used here
	CustomerId plugin.TValue[string]
	EntityId plugin.TValue[string]
	ProfileId plugin.TValue[string]
	Type plugin.TValue[string]
	UserEmail plugin.TValue[string]
	Date plugin.TValue[*time.Time]
	Parameters plugin.TValue[[]interface{}]
	Account plugin.TValue[interface{}]
	Security plugin.TValue[interface{}]
	AppUsage plugin.TValue[interface{}]
}

// createGoogleworkspaceReportUsage creates a new instance of this resource
func createGoogleworkspaceReportUsage(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlGoogleworkspaceReportUsage{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	if res.__id == "" {
	res.__id, err = res.id()
		if err != nil {
			return nil, err
		}
	}

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("googleworkspace.report.usage", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlGoogleworkspaceReportUsage) MqlName() string {
	return "googleworkspace.report.usage"
}

func (c *mqlGoogleworkspaceReportUsage) MqlID() string {
	return c.__id
}

func (c *mqlGoogleworkspaceReportUsage) GetCustomerId() *plugin.TValue[string] {
	return &c.CustomerId
}

func (c *mqlGoogleworkspaceReportUsage) GetEntityId() *plugin.TValue[string] {
	return &c.EntityId
}

func (c *mqlGoogleworkspaceReportUsage) GetProfileId() *plugin.TValue[string] {
	return &c.ProfileId
}

func (c *mqlGoogleworkspaceReportUsage) GetType() *plugin.TValue[string] {
	return &c.Type
}

func (c *mqlGoogleworkspaceReportUsage) GetUserEmail() *plugin.TValue[string] {
	return &c.UserEmail
}

func (c *mqlGoogleworkspaceReportUsage) GetDate() *plugin.TValue[*time.Time] {
	return &c.Date
}

func (c *mqlGoogleworkspaceReportUsage) GetParameters() *plugin.TValue[[]interface{}] {
	return &c.Parameters
}

func (c *mqlGoogleworkspaceReportUsage) GetAccount() *plugin.TValue[interface{}] {
	return plugin.GetOrCompute[interface{}](&c.Account, func() (interface{}, error) {
		return c.account()
	})
}

func (c *mqlGoogleworkspaceReportUsage) GetSecurity() *plugin.TValue[interface{}] {
	return plugin.GetOrCompute[interface{}](&c.Security, func() (interface{}, error) {
		return c.security()
	})
}

func (c *mqlGoogleworkspaceReportUsage) GetAppUsage() *plugin.TValue[interface{}] {
	return plugin.GetOrCompute[interface{}](&c.AppUsage, func() (interface{}, error) {
		return c.appUsage()
	})
}
