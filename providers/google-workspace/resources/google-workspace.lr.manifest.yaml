# Copyright (c) Mondoo, Inc.
# SPDX-License-Identifier: BUSL-1.1

resources:
  googleworkspace:
    fields:
      calendars: {}
      connectedApps: {}
      domains: {}
      groups: {}
      orgUnits: {}
      roles: {}
      users: {}
    min_mondoo_version: latest
    refs:
    - title: Google Workspace Knowledge Center
      url: https://knowledge.workspace.google.com/
  googleworkspace.calendar:
    fields:
      acl: {}
      primary: {}
      summary: {}
      summaryOverride: {}
    is_private: true
    min_mondoo_version: latest
  googleworkspace.calendar.aclRule:
    fields:
      role: {}
      scope: {}
    is_private: true
    min_mondoo_version: latest
  googleworkspace.calendar.aclRule.scope:
    fields:
      type: {}
      value: {}
    is_private: true
    min_mondoo_version: latest
  googleworkspace.connectedApp:
    fields:
      clientId: {}
      name: {}
      scopes: {}
      tokens: {}
      users: {}
    is_private: true
    min_mondoo_version: latest
  googleworkspace.domain:
    fields:
      creationTime: {}
      domainName: {}
      isPrimary: {}
      verified: {}
    is_private: true
    min_mondoo_version: latest
  googleworkspace.group:
    fields:
      adminCreated: {}
      aliases: {}
      description: {}
      directMembersCount: {}
      email: {}
      id: {}
      members: {}
      name: {}
      securitySettings: {}
      settings: {}
    is_private: true
    min_mondoo_version: latest
  googleworkspace.member:
    fields:
      email: {}
      id: {}
      status: {}
      type: {}
      user: {}
    is_private: true
    min_mondoo_version: latest
  googleworkspace.orgUnit:
    fields:
      description: {}
      id: {}
      name: {}
    is_private: true
    min_mondoo_version: latest
  googleworkspace.report.activity:
    fields:
      actor: {}
      events: {}
      id: {}
      ipAddress: {}
      ownerDomain: {}
    is_private: true
    min_mondoo_version: latest
  googleworkspace.report.apps:
    fields:
      admin: {}
      drive: {}
    is_private: true
    min_mondoo_version: 9.0.0
  googleworkspace.report.usage:
    fields:
      account: {}
      appUsage: {}
      customerId: {}
      date: {}
      entityId: {}
      parameters: {}
      profileId: {}
      security: {}
      type: {}
      userEmail: {}
    is_private: true
    min_mondoo_version: latest
  googleworkspace.report.users:
    fields:
      list: {}
    is_private: true
    min_mondoo_version: latest
  googleworkspace.role:
    fields:
      description: {}
      id: {}
      isSuperAdminRole: {}
      isSystemRole: {}
      name: {}
      privileges: {}
    is_private: true
    min_mondoo_version: latest
  googleworkspace.token:
    fields:
      anonymous: {}
      clientId: {}
      displayText: {}
      nativeApp: {}
      scopes: {}
      userKey: {}
    is_private: true
    min_mondoo_version: latest
  googleworkspace.user:
    fields:
      agreedToTerms: {}
      aliases: {}
      archived: {}
      creationTime: {}
      familyName: {}
      fullName: {}
      givenName: {}
      id: {}
      isAdmin: {}
      isDelegatedAdmin: {}
      isEnforcedIn2Sv: {}
      isEnrolledIn2Sv: {}
      isMailboxSetup: {}
      lastLoginTime: {}
      primaryEmail: {}
      recoveryEmail: {}
      recoveryPhone: {}
      suspended: {}
      suspensionReason: {}
      tokens: {}
      usageReport: {}
    is_private: true
    min_mondoo_version: latest
