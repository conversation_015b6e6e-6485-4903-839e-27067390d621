// Copyright (c) Mondoo, Inc.
// SPDX-License-Identifier: BUSL-1.1

import "../../network/resources/network.lr"

option provider = "go.mondoo.com/cnquery/v9/providers/aws"
option go_package = "go.mondoo.com/cnquery/v11/providers/aws/resources"

alias aws.accessAnalyzer = aws.iam.accessAnalyzer
alias aws.accessAnalyzer.analyzer = aws.iam.accessanalyzer.analyzer

// AWS resource
aws @defaults("account.id") {
  // List of `aws.vpc` objects representing all VPCs in the account across all enabled regions
  vpcs() []aws.vpc
  // List of all enabled regions in the account
  regions() []string
}

// AWS Account
aws.account @defaults("id") {
  // Account ID
  id string
  // Account aliases
  aliases() []string
  // Information about the associated organization, if any
  organization() aws.organization
  // Tags on the account
  // Note: This operation can only be called from the organization's management
  // account or by a member account that is a delegated administrator for an
  // Amazon Web Services service.
  tags() map[string]string
}

// AWS Organization resource
aws.organization @defaults("arn masterAccountEmail") {
  // ARN of the organization
  arn string
  // Functionality available to org: ALL or CONSOLIDATED_BILLING
  featureSet string
  // ID of the organization's master account
  masterAccountId string
  // Email owner of the organization's master account
  masterAccountEmail string
  // List of accounts that belong to the organization, if available to the caller
  accounts() []aws.account
}

// Amazon Virtual Private Cloud (VPC)
private aws.vpc @defaults("id isDefault cidrBlock region") {
  // ARN of the VPC
  arn string
  // ID of the VPC
  id string
  // Name of the VPC
  name string
  // IPv4 CIDR block of the VPC
  cidrBlock string
  // State of the VPC: pending or available
  state string
  // Whether the VPC is the default VPC
  isDefault bool
  // How instance hardware tenancy settings are enforced on instances launched in this VPC
  instanceTenancy string
  // Region in which the VPC exists
  region string
  // List of endpoints for the VPC
  endpoints() []aws.vpc.endpoint
  // List of flow logs for the VPC
  flowLogs() []aws.vpc.flowlog
  // List of route tables for the VPC
  routeTables() []aws.vpc.routetable
  // List of subnets for the VPC
  subnets() []aws.vpc.subnet
  // Tags on the VPC
  tags map[string]string
  // NAT gateways
  natGateways() []aws.vpc.natgateway
  // List of service endpoints associated with the VPC
  serviceEndpoints() []aws.vpc.serviceEndpoint
  // List of peering connections associated with the VPC
  peeringConnections() []aws.vpc.peeringConnection
  // Internet gateway blocking mode: block-bidirectional, block-ingress, or off
  internetGatewayBlockMode string
}

// Amazon Virtual Private Cloud (VPC) route table
private aws.vpc.routetable @defaults("id routes.length") {
  // A list of association descriptions
  associations() []aws.vpc.routetable.association
  // Unique ID of the route table
  id string
  // A list of route descriptions
  routes []dict
  // Tags on the route table
  tags map[string]string
}

// Amazon Virtual Private Cloud (VPC) route table association
private aws.vpc.routetable.association @defaults("routeTableAssociationId gatewayId") {
  // Unique ID of the association
  routeTableAssociationId string
  // Association state
  associationsState dict
  // Unique ID of the associated gateway
  gatewayId string
  // Whether this is the main association
  main bool
  // Unique ID of the route table
  routeTableId string
  // Subnet of the route table association
  subnet() aws.vpc.subnet
}

// Amazon Virtual Private Cloud (VPC) subnet
private aws.vpc.subnet @defaults("id cidrs region availabilityZone defaultForAvailabilityZone") {
  // ARN of the subnet
  arn string
  // Unique ID of the subnet
  id string
  // List of CIDR descriptions
  cidrs string
  // Whether instances launched in this subnet receive public IPv4 addresses
  mapPublicIpOnLaunch bool
  // Availability zone where this subnet is located
  availabilityZone string
  // Whether this is the default subnet for the availability zone
  defaultForAvailabilityZone bool
  // Whether a network interface created in this subnet (including a network interface created by RunInstances ) receives an IPv6 address
  assignIpv6AddressOnCreation bool
  // State of the subnet: pending or available
  state string
  // Region in which the VPC subnet exists
  region string
  // The number of available IP addresses in the subnet
  availableIpAddressCount int
  // Internet gateway blocking mode: block-bidirectional, block-ingress, or off
  internetGatewayBlockMode string
}

// Amazon Virtual Private Cloud (VPC) endpoint
private aws.vpc.endpoint @defaults("id type region") {
  // Unique ID of the endpoint
  id string
  // Type of the endpoint
  type string
  // VPC in which the endpoint exists
  vpc string
  // Region in which the VPC endpoint exists
  region string
  // Endpoint service name
  serviceName string
  // Policy document associated with the endpoint, if applicable
  policyDocument string
  // Subnets for the (interface) endpoint
  subnets []string
  // Whether to associate a private hosted zone with the specified VPC
  privateDnsEnabled bool
  // VPC endpoint state
  state string
  // Creation timestamp
  createdAt time
}

// Amazon Virtual Private Cloud (VPC) flow log
private aws.vpc.flowlog @defaults("id region status") {
  // Unique ID of the flow log
  id string
  // VPC in which the flow log exists
  vpc string
  // Region in which the VPC flow log exists
  region string
  // Status of the flow log
  status string
  // Tags on the flow log
  tags map[string]string
  // Creation timestamp
  createdAt time
  // Destination for the flow log data
  destination string
  // Destination type for the flow log data
  destinationType string
   // Delivery log status for the flow log data
  deliverLogsStatus string
  // Maximum interval of time during which a flow of packets is captured and aggregated into a flow log record: 60 seconds (1 minute) or 600 seconds (10 minutes)
  maxAggregationInterval int
  // Type of traffic to monitor: ACCEPT, ALL, and REJECT
  trafficType string
}

// Amazon WAF v2
aws.waf {
  // List of WAF ACLs
  acls() []aws.waf.acl
  // List of WAF rules
  ruleGroups() []aws.waf.rulegroup
  // List of WAF IP sets
  ipSets() []aws.waf.ipset
  // Scope either REGIONAL or CLOUDFRONT
  scope string
}

// Amazon WAF v2 ACL
private aws.waf.acl @defaults("name") {
  // ARN of the ACL
  arn string
  // ID of the ACL
  id string
  // Name of the ACL
  name string
  // Description of the ACL
  description string
  // Whether the ACL is managed by Firewall Manager
  managedByFirewallManager bool
  // List of WAF rules
  rules() []aws.waf.rule
  // Scope either REGIONAL or CLOUDFRONT
  scope string
}

// Amazon WAF v2 RuleGroup
private aws.waf.rulegroup @defaults("name") {
  // ARN of the rulegroup
  arn string
  // ID of the rulegroup
  id string
  // Name of the rulegroup
  name string
  // Description of the rulegroup
  description string
  // List of waf rules
  rules() []aws.waf.rule
  // Scope either REGIONAL or CLOUDFRONT
  scope string
}

// Amazon WAF rule
private aws.waf.rule @defaults("name") {
  // arn of the acl/ruleGroup + the name of the rule
  id string
  // Name of the rule
  name string
  // Priority from lowest to highest number
  priority int
  // Part of the rule that tells WAF how to inspect a web request
  statement aws.waf.rule.statement
  // Part of the rule that tells WAF what to do with a web request when it matches the criteria defined in the rule
  action aws.waf.rule.action
  // ARN of either rule ACL or the RuleGroup that this rule belongs to
  belongsTo string
}

// Action that happens if a rule statement matches
private aws.waf.rule.action @defaults("action") {
  // Name of the rule this action belongs to
  ruleName string
  // One of Block, Allow, Count, Captcha
  action string
  // HTTP Response Code, only if the action is Block
  responseCode string
}

private aws.waf.rule.statement @defaults("kind") {
  // ID of the statement
  id string
  // Kind of statement, e.g., "sqliMatchStatement"
  kind string
  // Entire statement as JSON
  json dict
  // Statement that detects SQL injection attacks
  sqliMatchStatement aws.waf.rule.statement.sqlimatchstatement
  // Statement that detects XSS attacks
  xssMatchStatement aws.waf.rule.statement.xssmatchstatement
  // Statement that matches certain bytes
  byteMatchStatement aws.waf.rule.statement.bytematchstatement
  // Statement that matches a regex pattern
  regexMatchStatement aws.waf.rule.statement.regexmatchstatement
  // Statement that matches requests from certain countries
  geoMatchStatement aws.waf.rule.statement.geomatchstatement
  // Statement that matches requests from certain ips defined in an IPSet
  ipSetReferenceStatement aws.waf.rule.statement.ipsetreferencestatement
  // Statement that matches requests with certain labels
  labelMatchStatement aws.waf.rule.statement.labelmatchstatement
  // Statement managed by AWS
  managedRuleGroupStatement aws.waf.rule.statement.managedrulegroupstatement
  // Statement that matches if the conditions are not met
  notStatement aws.waf.rule.statement.notstatement
  // Statement that matches if one or many sub-statements match
  orStatement aws.waf.rule.statement.orstatement
  // Statement that matches if all sub-statements match
  andStatement aws.waf.rule.statement.andstatement
  // Statement that matches if a request comes in at a certain rate (rate limiting)
  rateBasedStatement aws.waf.rule.statement.ratebasedstatement
  // Statement that matches a regex pattern defined in a regex pattern set
  regexPatternSetReferenceStatement aws.waf.rule.statement.regexpatternsetreferencestatement
  // Statement that refers to the rules in a rule group
  ruleGroupReferenceStatement aws.waf.rule.statement.rulegroupreferencestatement
  // Statement that matches the size of the request
  sizeConstraintStatement aws.waf.rule.statement.sizeconstraintstatement
}

// Rule statement that checks for requests from certain countries
private aws.waf.rule.statement.geomatchstatement @defaults("countryCodes") {
  // Name of the rule this statement belongs to
  ruleName string
  // ID of the statement
  statementID string
  // Country codes
  countryCodes []string
}

// Rule statement that checks for requests from IP addresses defined in an IPSet
private aws.waf.rule.statement.ipsetreferencestatement {
  // Name of the rule this statement belongs to
  ruleName string
  // ID of the statement
  statementID string
  // ARN of the ipset
  arn string
  // ipSetForwardedIPConfig
  ipSetForwardedIPConfig aws.waf.rule.statement.ipsetreferencestatement.ipsetforwardedipconfig
}

private aws.waf.rule.statement.ipsetreferencestatement.ipsetforwardedipconfig {
  // Name of the rule this statement belongs to
  ruleName string
  // ID of the statement
  statementID string
  // Name of the header
  headerName string
  // Position
  position string
  // Fallback behavior
  fallbackBehavior string
}

private aws.waf.rule.statement.labelmatchstatement {
  // Name of the rule this statement belongs to
  ruleName string
  // ID of the statement
  statementID string
  // Key
  key string
  // Scope
  scope string
}

// Rule statement that is managed by AWS
private aws.waf.rule.statement.managedrulegroupstatement @defaults("name") {
  // Name of the rule this statement belongs to
  ruleName string
  // ID of the statement
  statementID string
  // Name
  name string
  // Vendor name
  vendorName string
}

// Rule statement that matches if all of the rule statements inside it match
private aws.waf.rule.statement.andstatement {
  // Name of the rule this statement belongs to
  ruleName string
  // ID of the statement
  statementID string
  // Sub-statements
  statements []aws.waf.rule.statement
}

// Rule statement that negates another rule statement
private aws.waf.rule.statement.notstatement {
  // Name of the rule this statement belongs to
  ruleName string
  // ID of the statement
  statementID string
  // Sub-statement (will be negated)
  statement aws.waf.rule.statement
}

// Rule statement that matches if one of the rule statements inside it matches
private aws.waf.rule.statement.orstatement {
  // Name of the rule this statement belongs to
  ruleName string
  // ID of the statement
  statementID string
  // Sub-statements
  statements []aws.waf.rule.statement
}

// Rule statement that matches at a certain rate of requests (rate limiting)
private aws.waf.rule.statement.ratebasedstatement {}

// Rule statement that checks for a regex pattern defined in a regex pattern set
private aws.waf.rule.statement.regexpatternsetreferencestatement {
  // Name of the rule this statement belongs to
  ruleName string
  // ID of the statement
  statementID string
  // ARN of the regex pattern set
  arn string
  // Field that is matched
  fieldToMatch aws.waf.rule.fieldtomatch
}

// Rule statement that refers to a group of rules
private aws.waf.rule.statement.rulegroupreferencestatement {
  // Name of the rule this statement belongs to
  ruleName string
  // ID of the statement
  statementID string
  // ARN of the rule group
  arn string
  // List of rules to exclude
  excludeRules []string
}

// Rule statement that checks the size of the specified field
private aws.waf.rule.statement.sizeconstraintstatement {
  // Name of the rule this statement belongs to
  ruleName string
  // ID of the statement
  statementID string
  // Size that triggers this statement
  size int
  // How to compare the size
  comparisonOperator string
  // Field to match
  fieldToMatch aws.waf.rule.fieldtomatch
}

// Rule statement that matches a specified regex pattern
private aws.waf.rule.statement.regexmatchstatement {
  // Name of the rule this statement belongs to
  ruleName string
  // ID of the statement
  statementID string
  // Field to match
  fieldToMatch aws.waf.rule.fieldtomatch
  // Regex pattern to match
  regexString string
}

// Rule statement that matches a specified sequence of bytes
private aws.waf.rule.statement.bytematchstatement @defaults("searchString") {
  // Name of the rule this statement belongs to
  ruleName string
  // ID of the statement
  statementID string
  // Field to match
  fieldToMatch aws.waf.rule.fieldtomatch
  // String to search for
  searchString string
}

// Field to match
private aws.waf.rule.fieldtomatch @defaults("target") {
  target string
  // Name of the rule this statement belongs to
  ruleName string
  // ID of the statement
  statementID string
  // Whether to match the HTTP method: GET or POST
  method bool
  // Whether to match the URI path
  uriPath bool
  // Whether to match the query string
  queryString bool
  // Whether to match all query arguments
  allQueryArguments bool
  // Whether to match the body (match if not null)
  body aws.waf.rule.fieldtomatch.body
  // Whether to match the cookie (match if not null)
  cookie aws.waf.rule.fieldtomatch.cookie
  // Whether to match the single header (match if not null)
  singleHeader aws.waf.rule.fieldtomatch.singleheader
  // Whether to match the header order (match if not null)
  headerOrder aws.waf.rule.fieldtomatch.headerorder
  // Whether to match the header (match if not null)
  headers aws.waf.rule.fieldtomatch.headers
  // Whether to match the JA3 fingerprint (match if not null)
  ja3Fingerprint aws.waf.rule.fieldtomatch.ja3fingerprint
  // Whether to match the JSON body (match if not null)
  jsonBody aws.waf.rule.fieldtomatch.jsonbody
  // Whether to match the single query argument of the field (match if not null)
  singleQueryArgument aws.waf.rule.fieldtomatch.singlequeryargument
}

// Body of the field to match
private aws.waf.rule.fieldtomatch.body {
  // Name of the rule this statement belongs to
  ruleName string
  // ID of the statement
  statementID string
  // What to do if the body is over size
  overSizeHandling string
}

// Cookie of the field to match
private aws.waf.rule.fieldtomatch.cookie {
  // Name of the rule this statement belongs to
  ruleName string
  // ID of the statement
  statementID string
  // What to do if the cookie is over size
  overSizeHandling string
}

// Order of headers of the field to match
private aws.waf.rule.fieldtomatch.headerorder {
  // Name of the rule this statement belongs to
  ruleName string
  // ID of the statement
  statementID string
  // What to do if the order of headers is over size
  overSizeHandling string
}

// Single header of the field to match
private aws.waf.rule.fieldtomatch.singleheader {
  // Name of the rule this statement belongs to
  ruleName string
  // ID of the statement
  statementID string
  // Name of the header
  name string
}

// Single query argument
private aws.waf.rule.fieldtomatch.singlequeryargument {
  // Name of the rule this statement belongs to
  ruleName string
  // ID of the statement
  statementID string
  // Name of the query argument
  name string
}

// JA3 fingerprint
private aws.waf.rule.fieldtomatch.ja3fingerprint {
  // Name of the rule this statement belongs to
  ruleName string
  // ID of the statement
  statementID string
  // FallbackBehavior
  fallbackBehavior string
}

// Request body as JSON
private aws.waf.rule.fieldtomatch.jsonbody {
  // Name of the rule this statement belongs to
  ruleName string
  // ID of the statement
  statementID string
  // What to do if the body is over size
  overSizeHandling string
  // Match scope
  matchScope string
  // What to do if the body is not valid JSON
  invalidFallbackBehavior string
  // Match pattern
  matchPattern aws.waf.rule.fieldtomatch.jsonbody.matchpattern
}

// Pattern to match
private aws.waf.rule.fieldtomatch.jsonbody.matchpattern {
  // Name of the rule this statement belongs to
  ruleName string
  // ID of the statement
  statementID string
  // Whether to match all
  all bool
  // Paths to include
  includePaths []string
}

// Headers
private aws.waf.rule.fieldtomatch.headers {
  // Name of the rule this statement belongs to
  ruleName string
  // ID of the statement
  statementID string
  // Match scope
  matchScope string
  // What to do if the headers are over size
  overSizeHandling string
  // Match pattern
  matchPattern aws.waf.rule.fieldtomatch.headers.matchpattern
}

// Pattern to match
private aws.waf.rule.fieldtomatch.headers.matchpattern {
  // Name of the rule this statement belongs to
  ruleName string
  // ID of the statement
  statementID string
  // Whether to match all
  all bool
  // Headers to include
  includeHeaders []string
  // Headers to exclude
  excludeHeaders []string
}


// Statement that matches XSS attacks
private aws.waf.rule.statement.xssmatchstatement {
  // Name of the rule this statement belongs to
  ruleName string
  // ID of the statement
  statementID string
  // Field to match
  fieldToMatch aws.waf.rule.fieldtomatch
}

// Statement that matches SQLI attacks
private aws.waf.rule.statement.sqlimatchstatement {
  // Name of the rule this statement belongs to
  ruleName string
  // ID of the statement
  statementID string
  // Field to match
  fieldToMatch aws.waf.rule.fieldtomatch
  // How aggressive the statement matches
  sensitivityLevel string
}


// Amazon WAF IP set (defining IP Ranges)
private aws.waf.ipset @defaults("name") {
  // ARN of the IP set
  arn string
  // ID of the IP set
  id string
  // Scope: REGIONAL or CLOUDFRONT
  scope string
  // Name of the IP set
  name string
  // Description of the IP set
  description string
  // Address type: ipv4 or ipv6
  addressType string
  // List of IP addresses
  addresses dict
}


// AWS Elastic File System (EFS) service
aws.efs @defaults("filesystems") {
  // A list of file systems managed by the service
  filesystems() []aws.efs.filesystem
}

// AWS Elastic File System (EFS) file system
private aws.efs.filesystem @defaults("name id region") {
  // Name of the file system
  name string
  // ID of the file system
  id string
  // ARN of the file system
  arn string
  // Whether the file system is encrypted
  encrypted bool
  // KMS key used for encryption of the file system
  kmsKey() aws.kms.key
  // Backup policy for the file system
  backupPolicy() dict
  // Region in which the file system exists
  region string
  // Availability zone where the file system exists if a specific AZ is defined
  availabilityZone string
  // Tags for the file system
  tags map[string]string
  // Creation timestamp
  createdAt time
}

// AWS Key Management Service (KMS)
aws.kms @defaults("keys") {
  // A list of all customer master keys (CMKs) in the caller's AWS account (across all regions)
  keys() []aws.kms.key
}

// AWS Key Management Service (KMS) key
private aws.kms.key @defaults("id region metadata.Description") {
  // Unique identifier for the key
  id string
  // ARN of the key
  arn string
  // Region the key lives in
  region string
  // Whether key rotation is enabled
  keyRotationEnabled() bool
  // Metadata for the key
  metadata() dict
}


// AWS service to create and manage permissions for users and groups
aws.iam {
  // List of IAM users in the account
  users() []aws.iam.user
  // List of IAM roles in the account
  roles() []aws.iam.role
  // List of IAM groups in the account
  groups() []aws.iam.group
  // List of IAM policies in the account
  policies() []aws.iam.policy
  // List of IAM policies attached to a user, role, or group
  attachedPolicies() []aws.iam.policy
  // IAM credential report
  credentialReport() []aws.iam.usercredentialreportentry
  // IAM account password policy for the account
  accountPasswordPolicy() dict
  // IAM account summary
  accountSummary() map[string]int
  // List of virtual mfs devices associated with the account
  virtualMfaDevices() []aws.iam.virtualmfadevice
  // List of server certificates stored in IAM
  serverCertificates() []dict
  instanceProfiles() []aws.iam.instanceProfile
}

// Entry in AWS IAM credential report
private aws.iam.usercredentialreportentry @defaults("arn") {
  init(properties map[string]string)
  // Properties on the IAM user credential report
  properties map[string]string

  // ARN for the credential report
  arn() string

  // Whether the access key is active
  accessKey1Active() bool
  // Time when key was last rotated
  accessKey1LastRotated() time
  // Time when key was last used
  accessKey1LastUsedDate() time
  // Region in which the key was last used
  accessKey1LastUsedRegion() string
  // Service that last used the key
  accessKey1LastUsedService() string

  // Whether the access key is active
  accessKey2Active() bool
  // Time when key was last rotated
  accessKey2LastRotated() time
  // Time when key was last used
  accessKey2LastUsedDate() time
  // Region in which the key was last used
  accessKey2LastUsedRegion() string
  // Service that last used the key
  accessKey2LastUsedService() string

  // Whether the cert is active
  cert1Active() bool
  // Time when the cert was last rotated
  cert1LastRotated() time

  // Whether the cert is active
  cert2Active() bool
  // Time when the cert was last rotated
  cert2LastRotated() time

  // Whether MFA is active in the account
  mfaActive() bool
  // Whether passwords are enabled
  passwordEnabled() bool
  // Time when the password was last changed
  passwordLastChanged() time
  // Time when the password was last used
  passwordLastUsed() time
  // Next time when the password should rotate
  passwordNextRotation() time

  // IAM user
  user() aws.iam.user
  // Time when user was created: deprecated, use createdAt
  userCreationTime() time
  // Time when user was created
  createdAt() time
}

// AWS IAM user
private aws.iam.user @defaults("arn name createdAt") {
  // ARN of the IAM user
  arn string
  // ID of the IAM user
  id string
  // Name of the user
  name string
  // Time when user was created: deprecated, use createdAt
  createDate time
  // Time when user was created
  createdAt time
  // Time when password was last used
  passwordLastUsed time
  // Tags for the IAM user
  tags map[string]string
  // List of inline policies attached to the user
  policies() []string
  // List of managed policies attached to the user
  attachedPolicies() []aws.iam.policy
  // List of group ARNs that the user belongs to
  groups() []string
  // List of access keys metadata associated with the user
  accessKeys() []dict
  // Login profile for the user
  loginProfile() aws.iam.loginProfile
}

// AWS IAM instance profile
private aws.iam.instanceProfile @defaults("arn instanceProfileId") {
  // ARN of the instance profile
  arn string
  // Time when the instance profile was created: deprecated, use createdAt
  createDate time
  // Time when the instance profile was created
  createdAt time
  // ID of the IAM instance profile
  instanceProfileId string
  // Name of the instance profile
  instanceProfileName string
  // Tags for the instance profile
  tags map[string]string
  // Role attached to the instanceProfile
  iamRoles() []aws.iam.role
}

// AWS IAM login profile for a user
private aws.iam.loginProfile @defaults("createdAt") {
  // Time when the login profile was created
  createdAt time
}

// AWS IAM policy
private aws.iam.policy @defaults("arn name") {
  // ARN of the policy
  arn string
  // ID of the policy: deprecated, use policyId
  id string
  // ID of the policy
  policyId() string
  // Name of the policy
  name() string
  // Description of the policy
  description() string
  // Whether the policy can be attached
  isAttachable() bool
  // Number of principal entities (users, groups, and roles) that the policy is attached to
  attachmentCount() int
  // Time when the policy was created: deprecated, use createdAt
  createDate() time
  // Time when the policy was created
  createdAt() time
  // Time when the policy was updated
  updateDate() time
  // Scope of the policy
  scope() string
  // List of versions for the policy
  versions() []aws.iam.policyversion
  // Default version of the policy
  defaultVersion() aws.iam.policyversion

  // List of users attached to the policy
  attachedUsers() []aws.iam.user
  // List of roles attached to the policy
  attachedRoles() []aws.iam.role
  // List of groups attached to the policy
  attachedGroups() []aws.iam.group
}

// AWS IAM policy version
private aws.iam.policyversion @defaults("arn isDefaultVersion createdAt") {
  // ARN of the policy version
  arn string
  // Version ID
  versionId string
  // Whether this version is the policy default version
  isDefaultVersion bool
  // JSON statements for this policy version
  document() dict
  // Time when this policy version was created: deprecated, use createdAt
  createDate time
  // Time when this policy version was created
  createdAt time
}

// AWS IAM role
private aws.iam.role @defaults("arn name") {
  // ARN of the role
  arn string
  // ID of the role
  id string
  // Name of the role
  name string
  // Description of the role
  description string
  // Tags associated with the role
  tags map[string]string
  // Time when the role was created: deprecated, use createdAt
  createDate time
  // Time when the role was created
  createdAt time
  // Policy document that grants an entity permission to assume the role
  assumeRolePolicyDocument dict
}

// AWS IAM group
private aws.iam.group @defaults("arn name") {
  // ARN of the group
  arn string
  // ID of the group
  id string
  // Name of the group
  name string
  // Time when the group was created: deprecated, use createdAt
  createDate time
  // Time when the group was created
  createdAt time
  // List of usernames that belong to the group
  usernames []string
}

// AWS IAM virtual MFA device
private aws.iam.virtualmfadevice @defaults("serialNumber") {
  // Serial number for the MFA device
  serialNumber string
  // Time when the MFA device was enabled
  enableDate time
  // User associated with the MFA device
  user() aws.iam.user
}

// AWS IAM Access Analyzer resource (for assessing the configuration of AWS IAM Access Analyzer)
aws.iam.accessAnalyzer @defaults("analyzers") {
  // List of `aws.iam.accessanalyzer.analyzer` objects for all AWS IAM Access Analyzers configured within the account
  analyzers() []aws.iam.accessanalyzer.analyzer
  // List of all active findings for all analyzers and regions
  findings() []aws.iam.accessanalyzer.finding
}

// AWS IAM Access Analyzer resource (provides an object representing an individual AWS IAM Access Analyzer configuration)
private aws.iam.accessanalyzer.analyzer @defaults("name type region status") {
  // ARN for the analyzer
  arn string
  // Name for the analyzer
  name string
  // Status of the analyzer: ACTIVE, CREATING, DISABLED, or FAILED
  status string
  // Type of analyzer: ACCOUNT or ORGANIZATION
  type string
  // Region where the analyzer exists
  region string
  // Tags for the analyzer
  tags map[string]string
  // Name of the last resource that was analyzed
  lastResourceAnalyzed string
  // Last scan timestamp
  lastResourceAnalyzedAt time
  // Creation timestamp
  createdAt time
}

// AWS IAM Access Analyzer finding
private aws.iam.accessanalyzer.finding @defaults("resourceType region type status") {
  // Finding id
  id string
  // Error message
  error string
  // Resource
  resourceArn string
  // Resource owner
  resourceOwnerAccount string
  // Resource type
  resourceType string
  // Finding type
  type string
  // Finding status
  status string
  // Time the finding was generated
  analyzedAt time
  // Creation timestamp
  createdAt time
  // Creation timestamp
  updatedAt time
  // Region where the finding exists
  region string
  // Analyzer ARN
  analyzerArn string
}

// AWS SageMaker
aws.sagemaker {
  // List of SageMaker endpoints
  endpoints() []aws.sagemaker.endpoint
  // List of SageMaker notebook instances
  notebookInstances() []aws.sagemaker.notebookinstance
}

// AWS SageMaker notebook instance
private aws.sagemaker.notebookinstance @defaults("arn name") {
  // ARN for the notebook instance
  arn string
  // Name of the notebook instance
  name string
  // Details about the notebook
  details() aws.sagemaker.notebookinstancedetails
  // Region where the notebook instance exists
  region string
  // Tags for the notebook instance
  tags map[string]string
}

// AWS SageMaker notebook instance details
private aws.sagemaker.notebookinstancedetails @defaults("arn") {
  // ARN for the notebook instance
  arn string
  // KMS key used to encrypt data
  kmsKey() aws.kms.key
  // Whether SageMaker provides internet access to the instance
  directInternetAccess string
}

// AWS SageMaker endpoint
private aws.sagemaker.endpoint @defaults("arn name") {
  // ARN for the endpoint
  arn string
  // Name of the endpoint
  name string
  // Configuration information for the endpoint
  config() dict
  // Region where the endpoint exists
  region string
  // Tags for the endpoint
  tags map[string]string
}

// AWS Simple Notification Service (SNS)
aws.sns {
  // List of SNS topics
  topics() []aws.sns.topic
}

// AWS Simple Notification Service (SNS) topic
private aws.sns.topic @defaults("arn") {
  // SNS topic ARN
  arn string
  // Region where the SNS topic exists
  region string
  // List of subscriptions associated with the topic ARN
  subscriptions() []aws.sns.subscription
  // Attributes for the SNS topic, including KMS ID if any
  attributes() dict
  // Tags for the topic
  tags() map[string]string
}

// AWS Simple Notification Service (SNS) subscription
private aws.sns.subscription @defaults("arn") {
  // ARN of the subscription
  arn string
  // Protocol value for the subscription
  protocol string
}

// AWS Elasticsearch service
aws.es {
  // List of Elasticsearch domains
  domains() []aws.es.domain
}

// Amazon Elasticsearch service domain
private aws.es.domain @defaults("arn name") {
  // ARN for the Elasticsearch domain
  arn string
  // Whether encryption at rest is enabled
  encryptionAtRestEnabled bool
  // Whether node-to-node encryption is enabled
  nodeToNodeEncryptionEnabled bool
  // Name of the Elasticsearch domain
  name string
  // Endpoint used to submit index and search requests
  endpoint string
  // Region where the domain exists
  region string
  // Tags for the domain
  tags map[string]string
  // Version of Elasticsearch running
  elasticsearchVersion string
  // Elasticsearch domain ID
  domainId string
  // Elasticsearch domain name
  domainName string
}

// AWS Certificate Manager resource (for assessing the configuration of AWS Certificate Manager)
aws.acm @defaults("certificates") {
  // List of `aws.acm.certificate` objects representing ACM certificates configured within the account
  certificates() []aws.acm.certificate
}

// AWS Certificate Manager Certificate resource (provides an object representing an individual ACM certificate)
private aws.acm.certificate @defaults("domainName issuer createdAt notAfter") {
  // ARN for the certificate
  arn string
  // Time before which the certificate is not valid
  notBefore time
  // Time after which the certificate is not valid
  notAfter time
  // Time when the cert was requested
  createdAt time
  // FQDN for the certificate
  domainName string
  // Status of the certificate: issued, expired, revoked, and so on
  status string
  // Name of the entity associated with the public key in the certificate
  subject string
  // Retrieves an Amazon-issued certificate and its certificate chain
  certificate() network.certificate
  // Tags associated with the certificate
  tags map[string]string
  // Algorithm used to generate the public-private key pair
  keyAlgorithm string
  // Serial number of the certificate
  serial string
  // Source of the certificate: AMAZON_ISSUED or IMPORTED
  source string
  // Name of the certificate authority that issued and signed the certificate
  issuer string
  // Time at which the certificate was issued (exists only when the certificate source is AMAZON_ISSUED)
  issuedAt time
  // Date and time when the certificate was imported (exists only when the certificate source is IMPORTED)
  importedAt time
}

// AWS Auto Scaling
aws.autoscaling @defaults("groups") {
  // List of autoscaling groups across the account
  groups() []aws.autoscaling.group
}

// AWS Auto Scaling group
private aws.autoscaling.group @defaults("name region minSize maxSize") {
  // ARN for the autoscaling group
  arn string
  // Name of the group
  name string
  // List of load balancer names associated with the group
  loadBalancerNames []string
  // Health check type used by the group: ELB or EC2
  healthCheckType string
  // Tags for the asg
  tags map[string]string
  // Region of the Auto Scaling group
  region string
  // Minimum number of instances to scale down to
  minSize int
  // Maximum number of instances to scale up to
  maxSize int
  // Time to wait after scaling up or down before starting the next scaling event
  defaultCooldown int
  // Launch configuration name
  launchConfigurationName string
  // Grace period in seconds before an instance with a failing health check is replaced
  healthCheckGracePeriod int
  // Time when the autoscaling group was created
  createdAt time
  // Maximum amount of time, in seconds, that an instance can be in service
  maxInstanceLifetime int
  // Desired size of the group
  desiredCapacity int
  // List of availability zones associated with the group
  availabilityZones []string
  // Whether Capacity Rebalancing is enabled
  capacityRebalance bool
  // Duration of the default instance warmup, in seconds
  defaultInstanceWarmup int
  // EC2 instances associated with the group
  instances() []aws.ec2.instance
}

// AWS Elastic Load Balancing
aws.elb {
  // List of classic load balancers
  classicLoadBalancers() []aws.elb.loadbalancer
  // List of application, gateway, and network load balancers (elbv2)
  loadBalancers() []aws.elb.loadbalancer
}

// AWS Elastic Load Balancer (ELB) Target Group
private aws.elb.targetgroup @defaults("name port ec2Targets lambdaTargets") {
  // Name for the load balancer target group
  name string
  // ARN for the load balancer target group
  arn string
  // Port for the load balancer target group
  port int
  // Protocol for the load balancer target group
  protocol string
  // Protocol version for the load balancer target group
  protocolVersion string
  // IP address type for the load balancer target group (IPv4, IPv6)
  ipAddressType string
  // Whether health check is enabled for the load balancer target group
  healthCheckEnabled bool
  // Health check interval for the load balancer target group
  healthCheckIntervalSeconds int
  // Health check path for the load balancer target group
  healthCheckPath string
  // Health check port for the load balancer target group
  healthCheckPort string
  // Health check protocol for the load balancer target group
  healthCheckProtocol string
  // Health check timeout seconds for the load balancer target group
  healthCheckTimeoutSeconds int
  // Target type for the for the load balancer target group (instance, IP, Lambda, ALB)
  targetType string
  // Unhealthy threshold count for the load balancer target group
  unhealthyThresholdCount int
  // VPC for the load balancer target group
  vpc() aws.vpc
  // EC2 targets for the load balancer target group
  ec2Targets() []aws.ec2.instance
  // Lambda targets for the load balancer target group
  lambdaTargets() []aws.lambda.function
}

// AWS Elastic Load Balancing load balancer
private aws.elb.loadbalancer @defaults("name region elbType scheme vpcId dnsName") {
  // ARN for the load balancer
  arn string
  // DNS name for the load balancer
  dnsName string
  // List of listener configurations for the load balancer
  listenerDescriptions() []dict
  // User-specified name for the load balancer
  name string
  // Scheme for the load balancer: internet-facing or internal
  scheme string
  // A list of attributes for the load balancer
  attributes() []dict
  // Deprecated: Use `vpc` instead
  vpcId string
  // Deprecated (use `createdAt` instead)
  createdTime time
  // Date and time the load balancer was created
  createdAt time
  // Availability zone where the load balancer runs
  availabilityZones []string
  // VPC security groups for the load balancer
  securityGroups []aws.ec2.securitygroup
  // ID of the Amazon Route 53 hosted zone associated with the load balancer
  hostedZoneId string
  // Region where the load balancer exists
  region string
  // Type of ELB. Possible values are `network`, `application`, `gateway`, or `classic`
  elbType string
  // VPC where the load balancer is located
  vpc aws.vpc
  // List of target groups for the load balancer
  targetGroups() []aws.elb.targetgroup
}

// AWS CodeBuild for building and testing code
aws.codebuild {
  // List of build projects
  projects() []aws.codebuild.project
}

// AWS CodeBuild project
private aws.codebuild.project @defaults("arn name") {
  // ARN for the project
  arn string
  // Description of the project
  description string
  // Name of the project
  name string
  // Build environment information about the project
  environment dict
  // Region where the project exists
  region string
  // Source used for the build project
  source dict
  // Tags for the project
  tags map[string]string
}

// Amazon GuardDuty for threat detection
aws.guardduty {
  // List of GuardDuty active findings
  findings() []aws.guardduty.finding
  // List of GuardDuty detectors
  detectors() []aws.guardduty.detector
}

// Amazon GuardDuty detector
private aws.guardduty.detector @defaults("id region") {
  // Unique ID for the detector
  id string
  // Region for the detector
  region string
  // Status of the detector: ENABLED or DISABLED
  status() string
  // Feature set for the detector
  features() []dict
  // Tags for the project
  tags() map[string]string
  // Publishing frequency for the detector
  findingPublishingFrequency() string
  // List of active findings by the detector
  findings() []aws.guardduty.finding
  // Deprecated (use `findings` instead)
  unarchivedFindings() []dict
}

// AWS Guard Duty finding
private aws.guardduty.finding @defaults("title region severity") {
  // Unique ID for the finding
  arn string
  // ID of the finding
  id string
  // Region where the finding was found
  region string
  // Title
  title string
  // Description
  description string
  // Severity of the finding
  severity float
  // Confidence level of the finding
  confidence float
  // Type of finding
  type string
  // Created at time
  createdAt time
  // Updated at time
  updatedAt time
}

// AWS Security Hub
aws.securityhub {
  // List of Security Hubs in the account
  hubs() []aws.securityhub.hub
}

// AWS Security Hub hub
private aws.securityhub.hub @defaults("arn") {
  // ARN for the Security Hub
  arn string
  // Date and time when the hub was enabled
  subscribedAt string
}

// AWS Secrets Manager
aws.secretsmanager {
  // List of secrets
  secrets() []aws.secretsmanager.secret
}

// AWS Secrets Manager secret
private aws.secretsmanager.secret @defaults("arn name") {
  // ARN for the secret
  arn string
  // Creation date of the secret
  createdAt time
  // Description of the secret
  description string
  // Last date the secret was changed
  lastChangedDate time
  // Last date the secret was automatically rotated
  lastRotatedDate time
  // Name of the secret
  name string
  // Date of the next secret rotation
  nextRotationDate time
  // Primary region of the secret
  primaryRegion string
  // Whether rotation is enabled for the secret
  rotationEnabled bool
  // Tags for the secret
  tags map[string]string
}


// Amazon Elastic Container Service (ECS)
aws.ecs {
  // List of AWS ECS Clusters
  clusters() []aws.ecs.cluster
  // List of AWS ECS Containers
  containers() []aws.ecs.container
  // List of AWS ECS Container Instances
  containerInstances() []aws.ecs.instance
}

// Amazon ECS cluster
private aws.ecs.cluster @defaults("name region status runningTasksCount pendingTasksCount") {
  // ARN of the ECS cluster
  arn string
  // Name of the ECS cluster
  name string
  // Tags of the ECS cluster
  tags map[string]string
  // Count of running tasks in the cluster
  runningTasksCount int
  // Count of pending tasks in the cluster
  pendingTasksCount int
  // Count of container instances registered to the cluster
  registeredContainerInstancesCount int
  // Configuration for the cluster
  configuration dict
  // Status of the cluster
  status string
  // List of AWS ECS task definitions
  tasks() []aws.ecs.task
  // List of AWS ECS container instances
  containerInstances() []aws.ecs.instance
  // Region where the cluster is located
  region string
}

// AWS ECS container instance
private aws.ecs.instance {
  // Whether the agent is connected to ECS
  agentConnected bool
  // ID for the container instance
  id string
  // ARN for the container instance
  arn string
  // Capacity provider associated with the container instance
  capacityProvider string
  // If container instance is EC2 instance, this is the EC2 instance resource
  ec2Instance() aws.ec2.instance
  // Region for the container instance
  region string
}

// Amazon ECS task
private aws.ecs.task {
  // ARN of the ECS task
  arn string
  // Cluster associated with the ECS task
  clusterName string
  // Connectivity status of the ECS task
  connectivity dict
  // Last reported status for the ECS task
  lastStatus string
  // Platform Family assigned to the ECS task
  platformFamily string
  // Platform Version assigned to the ECS task
  platformVersion string
  // User-defined tags associated with the ECS task
  tags map[string]string
  // List of AWS ECS containers
  containers() []aws.ecs.container
}

// Amazon ECS container
private aws.ecs.container {
  // Name of the ECS container + IP for unique identification
  name string
  // ARN of the ECS container
  arn string
  // Public IP address of the ECS container
  publicIp string
  // Image used for the ECS container
  image string
  // Cluster associated with the ECS container
  clusterName string
  // ARN for the task definition associated with the ECS container
  taskDefinitionArn string
  // logDriver setting for the ECS container
  logDriver string
  // Platform family associated with the ECS container
  platformFamily string
  // Platform version assigned to the ECS container
  platformVersion string
  // Status of the ECS container
  status string
  // Region where the ECS Container is located
  region string
  // Command used to start the container
  command []string
  // ARN for the task used to create the container
  taskArn string
  // Runtime ID for the container
  runtimeId string
  // Name of the ECS container
  containerName string
}

// Amazon EMR
aws.emr {
  // List of EMR clusters
  clusters() []aws.emr.cluster
}

// Amazon EMR cluster
private aws.emr.cluster @defaults("arn") {
  // ARN for the cluster
  arn string
  // Name of the cluster
  name string
  // An approximation of the cost of the cluster, represented in m1.small/hours
  normalizedInstanceHours int
  // ARN of outpost where cluster is launched
  outpostArn string
  // Details about the current status of the cluster
  status dict
  // List of master instances for the cluster
  masterInstances() []dict
  // EMR cluster ID
  id string
  // Tags for the cluster
  tags map[string]string
}

// Amazon CloudWatch
aws.cloudwatch {
  // List of CloudWatch log groups
  logGroups() []aws.cloudwatch.loggroup
  // List of CloudWatch alarms
  alarms() []aws.cloudwatch.metricsalarm
  // List of CloudWatch metrics
  metrics() []aws.cloudwatch.metric
}

// Amazon CloudWatch metrics alarm
private aws.cloudwatch.metricsalarm @defaults("arn") {
  // ARN for the metric alarm
  arn string
  // Metric name associated with the alarm
  metricName string
  // Metric namespace associated with the alarm
  metricNamespace string
  // Region where the alarm exists
  region string
  // List of alarm actions (SNS topic ARNs) associated with the alarm
  actions []aws.sns.topic
  // State of the alarm
  state string
  // Description of the reason for the state
  stateReason string
  // List of SNS topic ARNs to trigger for insufficient data actions
  insufficientDataActions []aws.sns.topic
  // List of SNS topic ARNs to trigger for OK actions
  okActions []aws.sns.topic
  // Name of the alarm
  name string
}

// Amazon CloudWatch metric
private aws.cloudwatch.metric @defaults("name region") {
  // Name of the metric
  name string
  // Namespace for the metric
  namespace string
  // Region where the metric exists
  region string
  // List of CloudWatch metric alarms for the metric
  alarms() []aws.cloudwatch.metricsalarm
  // Dimensions that apply to the metric
  dimensions() []aws.cloudwatch.metricdimension
  // Statistics for the metric
  statistics() aws.cloudwatch.metricstatistics
}

// Amazon CloudWatch metric dimension
aws.cloudwatch.metricdimension @defaults("name value") {
  // Name of the dimension
  name string
  // Value of the dimension
  value string
}

// Amazon CloudWatch metric statistics
aws.cloudwatch.metricstatistics @defaults("name region") {
  init(namespace string, region string, name string)
  // Namespace for the metric
  namespace string
  // Name for the metric
  name string
  // Region for the metrics
  region string
  // Label for the statistics
  label string
  // Datapoints for the statistic over the last 24 hours in hour intervals
  datapoints []aws.cloudwatch.metric.datapoint
}

// Amazon CloudWatch metric datapoint
private aws.cloudwatch.metric.datapoint @defaults("id") {
  // Unique identifier for the datapoint
  id string
  // Timestamp of the metric datapoint
  timestamp time
  // Maximum value for the statistic
  maximum float
  // Minimum value for the statistic
  minimum float
  // Average value for the statistic
  average float
  // Sum value for the statistic
  sum float
  // Unit of the statistic
  unit string
}

// Amazon CloudWatch log group
private aws.cloudwatch.loggroup @defaults("arn") {
  // ARN of the log group
  arn string
  // Name of the log group
  name string
  // List of metric filters associated with the log group
  metricsFilters() []aws.cloudwatch.loggroup.metricsfilter
  // KMS key used for log encryption
  kmsKey() aws.kms.key
  // Region where the log group is stored
  region string
  // Number of days to retain the log events in the specified log group
  retentionInDays int
}

// Amazon CloudWatch log group metrics filter
private aws.cloudwatch.loggroup.metricsfilter @defaults("id") {
  // Unique ID for the metric
  id string
  // Filter name associated with the metric
  filterName string
  // Filter pattern associated with the metric
  filterPattern string
  // List of CloudWatch metrics
  metrics []aws.cloudwatch.metric
}

// Amazon CloudFront
aws.cloudfront @defaults("distributions functions") {
  // List of CloudFront distributions
  distributions() []aws.cloudfront.distribution
  // List of CloudFront functions
  functions() []aws.cloudfront.function
}

// Amazon CloudFront distribution
private aws.cloudfront.distribution @defaults("domainName status") {
  // ARN of the CloudFront distribution
  arn string
  // Status of the distribution
  status string
  // Domain Name of the distribution
  domainName string
  // Details on the origins of this distribution
  origins []aws.cloudfront.distribution.origin
  // Default cache behavior for the distribution
  defaultCacheBehavior dict
  // All cache behaviors for the distribution
  cacheBehaviors []dict
  // HTTP version of the distribution
  httpVersion string
  // Whether the distribution is IPV6 enabled
  isIPV6Enabled bool
  // Whether the distribution is enabled
  enabled bool
  // Price class of the distribution
  priceClass string
  // CNAMEs aliases if any for this distribution
  cnames []string
}

// Amazon CloudFront distribution origin
private aws.cloudfront.distribution.origin @defaults("id originPath") {
  // Domain name for the origin
  domainName string
  // Unique id for the origin
  id string
  // Number of times CloudFront attempts to connect
  connectionAttempts int
  // Number of seconds CloudFront waits when attempting a connection
  connectionTimeout int
  // Path that CloudFront appends to original domain
  originPath string
  // Account ID where the origin exists
  account string
}

// Amazon CloudFront function
private aws.cloudfront.function @defaults("name status") {
  // Name of the CloudFront function
  name string
  // Status of the CloudFront function
  status string
  // ARN of the CloudFront function
  arn string
  // Date and time when the function was last updated
  lastModifiedTime time
  // Deprecated (use `createdAt` instead)
  createdTime time
  // Date and time the function was created
  createdAt time
  // Stage that the function is in
  stage string
  // Comment to describe the function
  comment string
  // Runtime environment for the function
  runtime string
}

// AWS CloudTrail
aws.cloudtrail @defaults("trails") {
  // List of CloudTrail trails associated with the account
  trails() []aws.cloudtrail.trail
}

// AWS CloudTrail trail
private aws.cloudtrail.trail @defaults("name region") {
  // ARN of the trail
  arn string
  // Name of the trail
  name string
  // KMS key used to encrypt the logs
  kmsKey() aws.kms.key
  // Whether the trail exists in multiple regions (false if single region)
  isMultiRegionTrail bool
  // Whether the trail is an organization trail (logs events for management and member accounts of the organization)
  isOrganizationTrail bool
  // Whether log file validation is enabled
  logFileValidationEnabled bool
  // Whether API calls from global services are included
  includeGlobalServiceEvents bool
  // S3 bucket where trail files are delivered
  s3bucket() aws.s3.bucket
  // ARN of the SNS topic that the trail uses to send notifications
  snsTopicARN string
  // JSON list of information about the trail
  status() dict
  // Log group where trail files are delivered
  logGroup() aws.cloudwatch.loggroup
  // Role for logs endpoint to assume when writing to log group
  cloudWatchLogsRoleArn string
  // Group for logs endpoint to assume when writing to log group
  cloudWatchLogsLogGroupArn string
  // Settings for the trail's configured event selectors
  eventSelectors() []dict
  // Region in which the trail was created (home region)
  region string
}

// Amazon S3 bucket control
aws.s3control @defaults("accountPublicAccessBlock") {
  // Account level public access configuration for S3
  accountPublicAccessBlock() dict
}

// Amazon S3 cloud object storage
aws.s3 @defaults("buckets") {
  // List of S3 buckets across the account
  buckets() []aws.s3.bucket
}

// Amazon S3 bucket
private aws.s3.bucket @defaults("name location public") {
  // ARN of the bucket
  arn string
  // Name of the bucket
  name string
  // Policy associated with the bucket
  policy() aws.s3.bucket.policy
  // Tags for the bucket
  tags() map[string]string
  // List of access control grants associated with the bucket
  acl() []aws.s3.bucket.grant
  // Owner for the bucket
  owner() map[string]string
  // Whether the bucket is public
  public() bool
  // List of CORS information for the bucket
  cors() []aws.s3.bucket.corsrule
  // Location of the bucket
  location() string
  // Versioning state and MFA delete status of bucket
  versioning() map[string]string
  // Logging status and user permissions for bucket logging status
  logging() map[string]string
  // Website configuration for the bucket
  staticWebsiteHosting() map[string]string
  // Whether the bucket is locked by default
  defaultLock() string
  // Bucket cross-region replication configuration
  replication() dict
  // Bucket encryption configuration
  encryption() dict
  // Public access block configuration for the bucket
  publicAccessBlock() dict
  // Whether the bucket still exists (stale reference)
  exists bool
  // Deprecated (use `createdAt` instead)
  createdTime time
  // Date and time the bucket was created
  createdAt time
}

// Amazon S3 bucket grant
private aws.s3.bucket.grant @defaults("name permission") {
  // ID of the bucket grant
  id string
  // Name for the bucket grant
  name string
  // Permission associated with the grant
  permission string
  // Grantee associated with the grant
  grantee map[string]string
}

// Amazon S3 bucket CORS rule
private aws.s3.bucket.corsrule @defaults("name") {
  // Name of the rule
  name string
  // List of allowed headers
  allowedHeaders []string
  // List of allowed methods GET, POST, PUT, and so on
  allowedMethods []string
  // List of origins from which the bucket can be accessed
  allowedOrigins []string
  // List of exposed response headers
  exposeHeaders []string
  // Time in seconds that the browser caches preflight response
  maxAgeSeconds int
}

// Amazon S3 bucket policy
private aws.s3.bucket.policy @defaults("bucketName version") {
  // Unique ID for the policy
  id string
  // Deprecated. Use `bucketName` instead
  name string
  // Bucket name that this policy belongs
  bucketName string
  // Document for the policy
  document string
  // Version of the policy
  version() string
  // List of statements for the policy
  statements() []dict
}

// AWS Application Auto Scaling
aws.applicationAutoscaling @defaults("namespace") {
  init(namespace string)
  // Service namespace to query for application auto scaling: comprehend, rds, sagemaker, appstream, elasticmapreduce, dynamodb, lambda, ecs, cassandra, ec2, neptune, kafka, custom-resource, or elasticache
  namespace string
  // List of scalable targets belonging to the service namespace
  scalableTargets() []aws.applicationAutoscaling.target
}

// AWS Application Auto Scaling target
private aws.applicationAutoscaling.target @defaults("arn") {
  // Namespace for the target
  namespace string
  // ARN of the auto scaling target
  arn string
  // Scalable dimension for the target
  scalableDimension string
  // Minimum capacity set for the auto scaling target
  minCapacity int
  // Maximum capacity set for the auto scaling target
  maxCapacity int
  // suspendedState for the auto scaling target
  suspendedState dict
  // Creation date for the auto scaling target
  createdAt time
}

// AWS Backup
aws.backup @defaults("vaults") {
  // List of vaults for the service
  vaults() []aws.backup.vault
}

// AWS Backup vault
private aws.backup.vault @defaults("name region") {
  // ARN of the vault
  arn string
  // Name of the vault
  name string
  // List of recovery points stored in the backup vault
  recoveryPoints() []aws.backup.vaultRecoveryPoint
  // Region of the vault
  region string
  // Date the backup vault was created
  createdAt time
  // Whether the backup is locked
  locked bool
  // ARN of the encryption key
  encryptionKeyArn string
}

// AWS Backup vault recovery point
private aws.backup.vaultRecoveryPoint @defaults("resourceType completionDate status") {
  // ARN of the recovery point
  arn string
  // Resource type for the recovery point: EFS, DynamoDB, and so on
  resourceType string
  // Information about who created the recovery point
  createdBy dict
  // ARN of the IAM role used to create the recovery point
  iamRoleArn string
  // Status of the recovery point
  status string
  // Date the recovery point was created
  creationDate time
  // Date the recovery point completed
  completionDate time
  // ARN of the key used to encrypt the recovery point
  encryptionKeyArn string
  // Whether the recovery point is encrypted
  isEncrypted bool
}

// Amazon DynamoDB
aws.dynamodb {
  // List of backups for DynamoDB
  backups() []dict
  // List of global tables for DynamoDB
  globalTables() []aws.dynamodb.globaltable
  // List of tables for DynamoDB
  tables() []aws.dynamodb.table
  // List of DynamoDB settings across all regions
  limits() []aws.dynamodb.limit
  // List of exports for DynamoDB
  exports() []aws.dynamodb.export
}

// Amazon DynamoDB Export
private aws.dynamodb.export {
  // Table associated with the export
  table() aws.dynamodb.table
  // S3 bucket associated with the export
  s3Bucket() aws.s3.bucket
  // S3 prefix for the export
  s3Prefix() string
  // Count of items in the export
  itemCount() int
  // Type of the export (FULL_EXPORT or INCREMENTAL_EXPORT)
  type string
  // Status of the export (IN_PROGRESS, COMPLETED, or FAILED)
  status string
  // Format of the export (DYNAMODB_JSON or ION)
  format() string
  // Start time of the export
  startTime() time
  // End time of the export
  endTime() time
  // S3 SSE Algorithm for the export
  s3SseAlgorithm() string
  // SSE KMS Key Id
  kmsKey() aws.kms.key
  // ARN for the export
  arn string
}

// Amazon DynamoDB limits
private aws.dynamodb.limit @defaults("arn") {
  // ARN representing the account + region where the limit applies
  arn string
  // Region where the limits apply
  region string
  // Account max read limit
  accountMaxRead int
  // Account max write limit
  accountMaxWrite int
  // Table max read limit
  tableMaxRead int
  // Table max write limit
  tableMaxWrite int
}

// Amazon DynamoDB global table
private aws.dynamodb.globaltable @defaults("name") {
  // ARN for the global table
  arn string
  // Table name
  name string
  // List of replica settings for the table
  replicaSettings() []dict
}

// Amazon DynamoDB table
private aws.dynamodb.table @defaults("name region") {
  // ARN for the table
  arn string
  // Table ID
  id string
  // Table name
  name string
  // Region where the table exists
  region string
  // Backups for the table
  backups() []dict
  // Description of server-side encryption for the table
  sseDescription dict
  // Provisioned throughput settings for the table
  provisionedThroughput dict
  // Continuous backups and point-in-time recovery settings for the table
  continuousBackups() dict
  // Tags for the table
  tags() map[string]string
  // Deprecated (use `createdAt` instead)
  createdTime time
  // Date and time the table was created
  createdAt time
  // Whether deletion protection is enabled
  deletionProtectionEnabled bool
  // Global table version
  globalTableVersion string
  // Number of items in the table
  items int
  // Total size of the specified table, in bytes. DynamoDB updates this value approximately every six hours.
  sizeBytes int
  // Latest stream for this table
  latestStreamArn string
  // Current state of the table: CREATING, UPDATING, DELETING, ACTIVE, INACCESSIBLE_ENCRYPTION_CREDENTIALS, ARCHIVING, or ARCHIVED
  status string
}

// Amazon Simple Queue Service (SQS)
aws.sqs {
  // List of Amazon SQS queues
  queues() []aws.sqs.queue
}

// Amazon Simple Queue Service (SQS) Queue
private aws.sqs.queue {
  // ARN for the queue
  arn() string
  // Time when the queue was created
  createdAt() time
  // Dead letter SQS queue, if any
  deadLetterQueue() aws.sqs.queue
  // Delay seconds set on the queue
  deliveryDelaySeconds() int
  // KMS Key for SSE, if any
  kmsKey() aws.kms.key
  // Time when the queue was last modified
  lastModified() time
  // Maximum amount of messages that can be received by the queue
  maxReceiveCount() int
  // Maximum message size for the queue
  maximumMessageSize() int
  // Time in seconds the queue retains messages
  messageRetentionPeriodSeconds() int
  // Time in seconds the queue waits for messages
  receiveMessageWaitTimeSeconds() int
  // Region for the queue
  region string
  // Whether SSE is enabled for the queue
  sqsManagedSseEnabled() bool
  // Type of queue: Fifo or Standard
  queueType() string
  // URL for the queue
  url string
  // Visibility timeout for the queue
  visibilityTimeoutSeconds() int
}

// Amazon Relational Database Service (RDS)
aws.rds {
  // Deprecated: Use `instances` instead
  dbInstances() []aws.rds.dbinstance
  // List of database instances
  instances() []aws.rds.dbinstance
  // Deprecated: Use `clusters` instead
  dbClusters() []aws.rds.dbcluster
  // List of RDS database clusters
  clusters() []aws.rds.dbcluster
  // List of all pending maintenance actions for the database instance
  allPendingMaintenanceActions() []aws.rds.pendingMaintenanceAction
  // List of all parameter groups
  parameterGroups() []aws.rds.parameterGroup
  // List of all cluster parameter groups
  clusterParameterGroups() []aws.rds.clusterParameterGroup
}

// Amazon RDS Backup Setting
private aws.rds.backupsetting {
  // Target fot the backup setting
  target string
  // Retention period for the backup setting
  retentionPeriod int
  // Whether dedicated log volume is enabled for the backup setting
  dedicatedLogVolume bool
  // Whether backups have encryption turned on
  encrypted bool
  // KMS key associated with the backup setting
  kmsKey() aws.kms.key
  // Region for the backup setting
  region string
  // Status of the backup setting
  status string
  // Timezone for the backup setting
  timezone string
  // Time of earliest available restore
  earliestRestoreAvailable time
  // Time of latest available restore
  latestRestoreAvailable time
}

// Amazon RDS database cluster
aws.rds.dbcluster @defaults("id region engine engineVersion") {
  // ARN for the database cluster
  arn string
  // Region where the database cluster exists
  region string
  // Identifier for the database cluster
  id string
  // List of database instances that belong to the cluster
  members []aws.rds.dbinstance
  // List of snapshots for the cluster
  snapshots() []aws.rds.snapshot
  // Tags for the database cluster
  tags map[string]string
  // Whether the cluster is encrypted
  storageEncrypted bool
  // Amount of storage, in GiB, provisioned on the cluster
  storageAllocated int
  // Storage IOPS provisioned on the cluster
  storageIops int
  // Type of storage provisioned on the cluster
  storageType string
  // Current state of the cluster
  status string
  // Deprecated (use `createdAt` instead)
  createdTime time
  // Date and time the RDS cluster was created
  createdAt time
  // Number of days automated snapshots are retained
  backupRetentionPeriod int
  // Whether minor version patches are applied automatically
  autoMinorVersionUpgrade bool
  // Name of the compute and memory capacity class of the cluster database instances
  clusterDbInstanceClass string
  // Name of the database engine for this database cluster
  engine string
  // Version of the database engine for this DB cluster
  engineVersion string
  // Whether the cluster is publicly accessible
  publiclyAccessible bool
  // Whether the cluster is a Multi-AZ deployment
  multiAZ bool
  // Whether deletion protection is enabled
  deletionProtection bool
  // List of VPC security group elements that the database cluster belongs to
  securityGroups() []aws.ec2.securitygroup
  // List of Availability Zones (AZs) where instances in the database cluster can be created
  availabilityZones []string
  // Port on which the database engine is listening
  port int
  // Connection endpoint for the primary instance of the database cluster
  endpoint string
  // Cluster hosted zone ID
  hostedZoneId string
  // Master username for the database
  masterUsername string
  // Latest time to which a database can be restored with point-in-time restore
  latestRestorableTime time
  // Backup setting for the database cluster
  backupSettings() []aws.rds.backupsetting
  // Life cycle type for the database engine. By default, this value is set to `open-source-rds-extended-support`, which enrolls your DB engine into Amazon RDS Extended Support. At the end of standard support, you can avoid charges for Extended Support by setting the value to `open-source-rds-extended-support-disabled`. In this case, creating the DB engine will fail if the DB major version is past its end of standard support date.
  engineLifecycleSupport string
  // Expiration date for the instance certificate
  certificateExpiresAt time
  // ID of the Certificate Authority
  certificateAuthority string
  // Whether IAM database authentication is enabled
  iamDatabaseAuthentication bool
  // Mode of the database activity stream
  activityStreamMode string
  // Status of the database activity stream
  activityStreamStatus string
  // Interval, in seconds, between points when Enhanced Monitoring metrics are collected
  monitoringInterval int
  // Network type of the DB instance
  networkType string
  // Preferred maintenance window for the database cluster
  preferredMaintenanceWindow string
  // Preferred backup window for the database cluster
  preferredBackupWindow string
  // Whether the HTTP API endpoint is enabled
  httpEndpointEnabled bool
  // Container for engine configuration values
  parameterGroupName string
  // Global cluster identifier if the cluster is a global cluster member
  globalClusterIdentifier string
  // CloudWatch Database Insights mode. Possible values are `standard` and `advanced`
  databaseInsightsMode string
}

// Amazon RDS snapshot
private aws.rds.snapshot @defaults("id region type encrypted createdAt") {
  // ARN of the snapshot
  arn string
  // ID of the snapshot
  id string
  // Attribute values that describe permissions to restore the snapshot
  attributes() []dict
  // Type of snapshot: manual or automated
  type string
  // Whether the snapshot is encrypted
  encrypted bool
  // Region where the snapshot exists
  region string
  // Whether the snapshot is for a cluster
  isClusterSnapshot bool
  // Tags for the snapshot
  tags map[string]string
  // Snapshot DB engine
  engine string
  // Snapshot DB engine version
  engineVersion string
  // Snapshot status
  status string
  // Port that the database instance or cluster listens on
  port int
  // Allocated storage size in gibibytes (GiB)
  allocatedStorage int
  // Creation date of the snapshot
  createdAt time
}

// Amazon RDS database instance
aws.rds.dbinstance @defaults("id region engine engineVersion") {
  // ARN for the database instance
  arn string
  // Identifier for the database instance
  id string
  // Name of the database instance
  name string
  // Number of days automated snapshots are retained
  backupRetentionPeriod int
  // List of snapshots for the database instance
  snapshots() []aws.rds.snapshot
  // Whether the instance is encrypted
  storageEncrypted bool
  // Amount of storage, in GiB, provisioned on the instance
  storageAllocated int
  // Storage IOPS provisioned on the instance
  storageIops int
  // Type of storage provisioned on the instance
  storageType string
  // Region where the instance exists
  region string
  // Availability zone where the instance exists
  availabilityZone string
  // Whether the instance is publicly accessible. Note: This will only return a value for non-Aurora Multi-AZ DB clusters
  publiclyAccessible bool
  // List of log types the instance is configured to export to CloudWatch logs
  enabledCloudwatchLogsExports []string
  // Whether deletion protection is enabled
  deletionProtection bool
  // Whether the instance is a Multi-AZ deployment
  multiAZ bool
  // Interval, in seconds, between points when Enhanced Monitoring metrics are collected
  monitoringInterval int
  // ARN of the CloudWatch log stream that receives the enhanced monitoring metrics data
  enhancedMonitoringResourceArn string
  // Tags for the database instance
  tags map[string]string
  // Name of the compute and memory capacity class of the database instance
  dbInstanceClass string
  // User-supplied unique key that identifies a database instance
  dbInstanceIdentifier string
  // Name of the database engine for this database instance
  engine string
  // Version of the database engine for this database instance
  engineVersion string
  // List of VPC security group elements that the database instance belongs to
  securityGroups() []aws.ec2.securitygroup
  // Current state of this database
  status string
  // Whether minor version patches are applied automatically
  autoMinorVersionUpgrade bool
  // Deprecated (use `createdAt` instead)
  createdTime time
  // Date and time the RDS instance was created
  createdAt time
  // Port that the database instance listens on. If the database instance is part of a DB cluster, this can be a different port than the DB cluster port.
  port int
  // Connection endpoint for the database instance
  endpoint string
  // Master username for the database instance
  masterUsername string
  // Latest time to which a database can be restored with point-in-time restore
  latestRestorableTime time
  // Backup setting for the database instance
  backupSettings() []aws.rds.backupsetting
  // Subnet for the RDS instance
  subnets() []aws.vpc.subnet
  // Life cycle type for the database engine. By default, this value is set to `open-source-rds-extended-support`, which enrolls your DB engine into Amazon RDS Extended Support. At the end of standard support, you can avoid charges for Extended Support by setting the value to `open-source-rds-extended-support-disabled`. In this case, creating the DB engine will fail if the DB major version is past its end of standard support date.
  engineLifecycleSupport string
  // Expiration date for the instance certificate
  certificateExpiresAt time
  // ID of the Certificate Authority
  certificateAuthority string
  // Whether IAM database authentication is enabled
  iamDatabaseAuthentication bool
  // Assigned IAM instance profile
  customIamInstanceProfile string
  // Mode of the database activity stream
  activityStreamMode string
  // Status of the database activity stream
  activityStreamStatus string
  // List of pending maintenance actions for the database instance
  pendingMaintenanceActions() []aws.rds.pendingMaintenanceAction
  // Network type of the DB instance
  networkType string
  // Preferred maintenance window for the database cluster
  preferredMaintenanceWindow string
  // Preferred backup window for the database cluster
  preferredBackupWindow string
}

// Amazon RDS pending maintenance action
private aws.rds.pendingMaintenanceAction {
  // ARN for resource
  resourceArn string
  // Action to take
  action string
  // Description of the action
  description string
  // Auto applied after date
  autoAppliedAfterDate time
  // Current apply date
  currentApplyDate time
  // Forced apply date
  forcedApplyDate time
  // Opt-in status
  optInStatus string
}

// Amazon RDS cluster parameter groups
aws.rds.clusterParameterGroup @defaults("name family region arn") {
  // ARN for resource
  arn string
  // Family of the parameter group
  family string
  // Name of the parameter group
  name string
  // Description of the parameter group
  description string
  // Region of the parameters
  region string
  parameters() []aws.rds.parameterGroup.parameter
}

// Amazon RDS parameter groups
aws.rds.parameterGroup @defaults("name family region arn") {
  // ARN for resource
  arn string
  // Family of the parameter group
  family string
  // Name of the parameter group
  name string
  // Description of the parameter group
  description string
  // Region of the parameters
  region string
  // The parameters of the group
  parameters() []aws.rds.parameterGroup.parameter
}

aws.rds.parameterGroup.parameter @defaults("name value") {
  // Specifies the valid range of values for the parameter
  allowedValues string
  // When to apply parameter updates
  applyMethod string
  // Specifies the engine specific parameters type
  applyType string
  // Specifies the valid data type for the parameter
  dataType string
  // Provides a description of the parameter
  description string
  // being changed
  isModifiable bool
  // The earliest engine version to which the parameter can apply
  minimumEngineVersion string
  // The name of the parameter
  name string
  // The value of the parameter
  value string
  // The source of the parameter value
  source string
  // The valid DB engine modes
  supportedEngineModes []string
}


// Amazon ElastiCache
aws.elasticache @defaults("cacheClusters") {
  // Deprecated: Use `cacheClusters` instead.
  clusters() []dict
  // List of cache clusters
  cacheClusters() []aws.elasticache.cluster
  // List of serverless caches
  serverlessCaches() []aws.elasticache.serverlessCache
}

// Amazon ElastiCache cluster
private aws.elasticache.cluster @defaults("cacheClusterId region nodeType engine") {
  // ARN for the cluster
  arn string
  // Whether the cluster has at rest encryption enabled
  atRestEncryptionEnabled bool
  // Whether Redis authentication tokens (or passwords) enable Redis to require a password before allowing clients to run commands
  authTokenEnabled bool
  // Date and time authentication token was last modified
  authTokenLastModifiedDate time
  // Whether the cluster is configured to auto-upgrade to the next minor version (Redis 6.0 or later)
  autoMinorVersionUpgrade bool
  // Date and time when the cluster was created
  cacheClusterCreateTime time
  // User-supplied unique key that identifies the cluster
  cacheClusterId string
  // Current state of the cluster: available, creating, deleted, deleting, incompatible-network, modifying, rebooting cluster nodes, restore-failed, or snapshotting
  cacheClusterStatus string
  // Name of the compute and memory capacity node type for the cluster
  cacheNodeType string
  // A list of cache nodes that are members of the cluster
  cacheNodes []string
  // A list of cache security group elements, composed of name and status sub-elements
  cacheSecurityGroups []string
  // Name of the cache subnet group associated with the cluster
  cacheSubnetGroupName string
  // URL of the web page where you can download the latest ElastiCache client library
  clientDownloadLandingPage string
  // Node type for the nodes in the cluster
  nodeType string
  // Name of the cache engine used for this cluster: Memcached or Redis
  engine string
  // Version of the cache engine that is used in this cluster
  engineVersion string
  // Network type associated with the cluster: ipv4 or ipv6
  ipDiscovery string
  // Log delivery configurations being modified
  logDeliveryConfigurations []dict
  // Supported network connection type for the cluster: ipv4, ipv6, or dual_stack
  networkType string
  // Describes a notification topic and its status
  notificationConfiguration string
  // Number of cache nodes in the cluster
  numCacheNodes int
  // Name of the availability zone in which the cluster is located or "Multiple" if the cache nodes are located in different availability zones
  preferredAvailabilityZone string
  // Region where the cluster exists
  region string
  // A list of VPC security groups associated with the cluster
  securityGroups() []aws.ec2.securitygroup
  // Number of days ElastiCache retains automatic cluster snapshots before deleting them
  snapshotRetentionLimit int
  // Whether in-transit encryption is enabled
  transitEncryptionEnabled bool
  // Whether migrating clients to use in-transit encryption (with no downtime) is allowed
  transitEncryptionMode string
}

// Amazon ElastiCache serverless cache
private aws.elasticache.serverlessCache @defaults("name description status engine engineVersion") {
  // ARN for the cache
  arn string
  // Unique identifier of the serverless cache
  name string
  // Description of the serverless cache
  description string
  // Cache engine used for this cluster: Memcached or Redis
  engine string
  // Version of the cache engine that is used in this cluster
  engineVersion string
  // Version number of the engine with which the serverless cache is compatible
  majorEngineVersion string
  // ID of the Amazon Web Services Key Management Service (KMS) key
  kmsKeyId string
  // A list of VPC security groups associated with the cluster
  securityGroups() []aws.ec2.securitygroup
  // Number of days ElastiCache retains automatic cluster snapshots before deleting them
  snapshotRetentionLimit int
  // Time each day that a cache snapshot is created
  dailySnapshotTime string
  // Status of the serverless cache
  status string
  // Region where the cache exists
  region string
  // Time when the serverless cache was created
  createdAt time
}

// Amazon Redshift
aws.redshift @defaults("clusters") {
  // List of clusters
  clusters() []aws.redshift.cluster
}

// Amazon Redshift cluster
private aws.redshift.cluster @defaults("dbName clusterVersion clusterStatus region") {
  // Whether major upgrades are applied automatically
  allowVersionUpgrade bool
  // ARN for the cluster
  arn string
  // Number of days automatic cluster snapshots are retained
  automatedSnapshotRetentionPeriod int
  // Availability zone where the cluster exists
  availabilityZone string
  // List of cluster parameter group names
  clusterParameterGroupNames []string
  // Specific revision number of the database in the cluster
  clusterRevisionNumber string
  // Current state of this cluster: available, creating, deleting, rebooting, renaming, or resizing
  clusterStatus string
  // Name of the subnet group associated with the cluster
  clusterSubnetGroupName string
  // Version of the Redshift engine running on the cluster
  clusterVersion string
  // Cluster creation timestamp
  createdAt time
  // Name of the initial database that was created when the cluster was created
  dbName string
  // Whether the cluster is encrypted at rest
  encrypted bool
  // Whether enhanced VPC routing is enabled for the cluster traffic
  enhancedVpcRouting bool
  // Logging configuration for the cluster
  logging() dict
  // Master user name for the cluster.
  masterUsername string
  // Name of the initial database created when cluster was created
  name string
  // Next scheduled maintenance window
  nextMaintenanceWindowStartTime time
  // Node type for the nodes in the cluster
  nodeType string
  // Number of nodes in the cluster
  numberOfNodes int
  // Detailed list of parameters for each parameter group name
  parameters() []dict
  // Weekly time range for system maintenance (in UTC)
  preferredMaintenanceWindow string
  // Whether the cluster is publicly accessible
  publiclyAccessible bool
  // Region where the cluster exists
  region string
  // Tags for the cluster
  tags map[string]string
  // ID of the VPC where the cluster is running
  vpcId string
}

// AWS Elastic Container Registry (ECR)
aws.ecr {
  // List of private repositories
  privateRepositories() []aws.ecr.repository
  // List of public repositories associated with the AWS account
  publicRepositories() []aws.ecr.repository
  // List of images
  images() []aws.ecr.image
}

// AWS Elastic Container Registry repository
private aws.ecr.repository @defaults("uri region") {
  // ARN of the repository
  arn string
  // Name of the repository
  name string
  // URI of the repository, used for push/pull operations
  uri string
  // AWS Account ID associated with public registry for this repository
  registryId string
  // Whether the repository is public
  public bool
  // List of images in the repository
  images() []aws.ecr.image
  // Region where the image is stored
  region string
  // Whether the repository option to scan on image push is enabled
  imageScanOnPush bool
}

// AWS Elastic Container Registry image
private aws.ecr.image @defaults("uri region") {
  // SHA256 of the image manifest
  digest string
  // Type of image manifest
  mediaType string
  // List of tags associated with image
  tags []string
  // AWS account ID associated with public registry for this image
  registryId string
  // Name of the repository for the image
  repoName string
  // Region where the ECR image is located
  region string
  // ARN for the image
  arn string
  // URI for the image repository
  uri string
  // Time the image was pushed
  pushedAt time
  // Size of the image in bytes
  sizeInBytes int
  // Time of the most recent image pull (Amazon only refreshes this data once every 24 hours.)
  lastRecordedPullTime time
}

// AWS Database Migration Service (DMS)
aws.dms @defaults("replicationInstances") {
  // List of DMS replication instances
  replicationInstances() []dict
}

// Amazon API Gateway
aws.apigateway @defaults("restApis") {
  // List of `aws.apigateway.restapi` objects representing all rest APIs across all enabled regions in the account
  restApis() []aws.apigateway.restapi
}

// Amazon API Gateway REST API
private aws.apigateway.restapi @defaults("name id") {
  // ARN for the REST API
  arn string
  // Unique ID for the REST API
  id string
  // Name for the REST API
  name string
  // Time when the REST API was created
  createdDate time
  // Description for the REST API
  description string
  // Stages for the REST API
  stages() []aws.apigateway.stage
  // Region where the REST API exists
  region string
  // Tags for the REST API
  tags map[string]string
}

// Amazon API Gateway REST API stages
private aws.apigateway.stage @defaults("arn") {
  // ARN for the REST API stage
  arn string
  // Name for the stage
  name string
  // Whether tracing is enabled for the stage
  tracingEnabled bool
  // Description for the stage
  description string
  // ID of the deployment the stage is attached to
  deploymentId string
  // Method settings for the stage
  methodSettings dict
}

// AWS Lambda
aws.lambda {
  // List of Lambda functions across all regions in the account
  functions() []aws.lambda.function
}

// AWS Lambda function
private aws.lambda.function @defaults("arn") {
  // ARN of the function
  arn string
  // Name of the function
  name string
  // Runtime environment for the function
  runtime string
  // Concurrency limit for the function
  concurrency() int
  // Target ARN of the dead-letter queue config
  dlqTargetArn string
  // Policy for the function
  policy() dict
  // VPC configuration for the Lambda function
  vpcConfig dict
  // Region where the function exists
  region string
  // Tags for the function
  tags map[string]string
}

// Amazon Systems Manager
aws.ssm @defaults("instances") {
  // List of SSM instances
  instances() []aws.ssm.instance
  // List of SSM parameters
  parameters() []aws.ssm.parameter
}


// Amazon SSM parameter
private aws.ssm.parameter {
  // Allowed pattern for the parameter
  allowedPattern string
  // ARN for the parameter
  arn string
  // Region for the parameter
  region string
  // Data type for the parameter
  dataType string
  // Description for the parameter
  description string
  // KMS key associated with the parameter (if any)
  kmsKey() aws.kms.key
  // Date when the parameter was last modified
  lastModifiedDate time
  // Name of the parameter
  name string
  // Tier of the parameter (Standard, Advanced, or Intelligent-Tiering)
  tier string
  // Type of the parameter (String, StringList, or SecureString)
  type string
  // Version of the parameter
  version int
}

// Amazon SSM instance
private aws.ssm.instance @defaults("instanceId region platformName platformVersion ipAddress") {
  // Instance ID for the SSM Instance
  instanceId string
  // Ping status (such as online) for the SSM Instance
  pingStatus string
  // IP Address for the SSM instance
  ipAddress string
  // Platform name for the SSM Instance, as described by AWS
  platformName string
  // Type of platform for the SSM Instance, as described by AWS: Windows, Linux, and so on
  platformType string
  // Platform version for the SSM Instance, as described by AWS
  platformVersion string
  // Region where the SSM instance is located
  region string
  // ARN for the SSM instance
  arn string
  // Tags for the SSM instance
  tags() map[string]string
}

// Amazon EC2
aws.ec2 {
  // List of security groups available to the account
  securityGroups() []aws.ec2.securitygroup
  // List of instances across the AWS account (all regions)
  instances() []aws.ec2.instance
  // map[region]boolean used to denote if EBS encryption is on by default per region
  ebsEncryptionByDefault() map[string]bool
  // List of volumes across the AWS account
  volumes() []aws.ec2.volume
  // List of snapshots across the account
  snapshots() []aws.ec2.snapshot
  // List of internet gateways
  internetGateways() []aws.ec2.internetgateway
  // List of VPN connections
  vpnConnections() []aws.ec2.vpnconnection
  // List of network ACLs
  networkAcls() []aws.ec2.networkacl
  // List of keypairs for the account
  keypairs() []aws.ec2.keypair
  // List of Elastic IPs (EIPs)
  eips() []aws.ec2.eip
}

// Amazon Elastic IP (EIP)
private aws.ec2.eip @defaults("publicIp attached privateIpAddress") {
  // Public IP address of the EIP
  publicIp string
  // Whether the Elastic IP is associated with an instance (false if no allocationId is present)
  attached bool
  // EC2 instance associated with the EIP
  instance() aws.ec2.instance
  // ID of the network interface
  networkInterfaceId string
  // ID of the network interface owner
  networkInterfaceOwnerId string
  // Private IP address for the EIP
  privateIpAddress string
  // IPv4 pool of the EIP
  publicIpv4Pool string
  // Tags for the EIP
  tags map[string]string
  // Region where the EIP is located
  region string
}

// Amazon VPC NAT Gateway
private aws.vpc.natgateway {
  // Time when the gateway was created
  createdAt time
  // ID of the NAT gateway
  natGatewayId string
  // State of the NAT gateway (pending, failed, available, deleting, or deleted)
  state string
  // Tags for the NAT gateway
  tags map[string]string
  // VPC associated with the NAT gateway
  vpc() aws.vpc
  // List of addresses associated with the NAT gateway
  addresses []aws.vpc.natgateway.address
  // Subnet for the NAT gateway
  subnet() aws.vpc.subnet
}

// Amazon VPC NAT gateway address
private aws.vpc.natgateway.address {
  // Allocation ID for the address
  allocationId string
  // Network interface ID for the address
  networkInterfaceId string
  // Private IP associated with the address
  privateIp string
  // EIP associated with the address
  publicIp() aws.ec2.eip // AllocationId can get us back to the actual EIP
  // Whether this is the primary address for the NAT gateway
  isPrimary bool
}

// Amazon VPC Service Endpoint
private aws.vpc.serviceEndpoint {
  // Whether acceptance is required
  acceptanceRequired() bool
  // List of availability zones for the service endpoint
  availabilityZones() []string
  // List of base endpoint DNS names for the service endpoint
  dnsNames []string
  // Service ID
  id string
  // Whether the service endpoint manages VPC endpoints
  managesVpcEndpoints() bool
  // Service name
  name string
  // Service owner
  owner string
  // Service payer responsibility
  payerResponsibility() string
  // Service private DNS name verification state
  privateDnsNameVerificationState() string
  // List of service private DNS names
  privateDnsNames() []string
  // Tags for the service endpoint
  tags map[string]string
  // Service type
  type string
  // Whether the service supports VPC endpoint policy
  vpcEndpointPolicySupported() bool
}

// Amazon VPC Peering Connection
private aws.vpc.peeringConnection {
  // VPC for the peering connection acceptor
  acceptorVpc() aws.vpc.peeringConnection.peeringVpc
  // Expiration time for the peering connection
  expirationTime time
  // ID of the peering connection
  id string
  // VPC for the peering connection requestor
  requestorVpc() aws.vpc.peeringConnection.peeringVpc
  // Status of the peering connection
  status string
  // Tags for the peering connection
  tags map[string]string
}

// Amazon VPC Peering Connection Peering VPC
private aws.vpc.peeringConnection.peeringVpc {
  // Whether DNS resolution from the remote VPC is allowed
  allowDnsResolutionFromRemoteVpc bool
  // Whether egress is allowed from a local classic link to the remote VPC
  allowEgressFromLocalClassicLinkToRemoteVpc bool
  // Whether egress is allowed from a local VPC to a classic link
  allowEgressFromLocalVpcToRemoteClassicLink bool
  // List of IPv4 CIDR blocks for peering connection
  ipv4CiderBlocks []string
  // List of IPv6 CIDR blocks for peering connection
  ipv6CiderBlocks []string
  // Owner ID of the peering connection
  ownerID string
  // Region of the peering connection
  region string
  // VPC associated with the peering connection (populated if it's in the same account)
  vpc() aws.vpc
  // ID of the VPC associated with the peering connection
  vpcId string
}

// Amazon EC2 network ACL
private aws.ec2.networkacl @defaults("id region") {
  // ARN for the network ACL
  arn string
  // ID for the network ACL
  id string
  // Region for the network ACL
  region string
  // Entries for the network ACL
  entries() []aws.ec2.networkacl.entry
  // Whether the ACL is the default network ACL for the VPC
  isDefault bool
  // Tags for the network ACL
  tags map[string]string
  // Associations for the network ACL
  associations []aws.ec2.networkacl.association
}

private aws.ec2.networkacl.association {
  // ID for the association
  associationId string
  // Network ACL ID for the association
  networkAclId string
  // Subnet ID for the association
  subnetId string
}

// Amazon EC2 network ACL entry
private aws.ec2.networkacl.entry @defaults("id egress ruleAction cidrBlock portRange") {
  // Whether this is an entry for egress rules
  egress bool
  // Allow or deny
  ruleAction string
  // Rule number
  ruleNumber int
  // Port range for the ACL entry
  portRange() aws.ec2.networkacl.entry.portrange
  // CIDR block for the ACL entry
  cidrBlock string
  // IPv6 CIDR block for the ACL entry
  ipv6CidrBlock string
  // ID for the ACL entry rule
  id string
}

// Amazon EC2 network ACL entry port range
private aws.ec2.networkacl.entry.portrange @defaults("from to") {
  // Starting port for port range
  from int
  // Ending port for port range
  to int
  // ID for the entry port range
  id string
}

// Amazon EC2 VPN connection
private aws.ec2.vpnconnection @defaults("arn") {
  // ARN for the VPN connection
  arn string
  // List of telemetry data for the VPN
  vgwTelemetry []aws.ec2.vgwtelemetry
}

// Amazon EC2 VPN tunnel telemetry
private aws.ec2.vgwtelemetry {
  // Outside IP address
  outsideIpAddress string
  // VPN tunnel status
  status string
  // VPN tunnel status message
  statusMessage string
}

// Amazon EC2 internet gateway
private aws.ec2.internetgateway @defaults("arn") {
  // ARN for the gateway
  arn string
  // ID for the gateway
  id string
  // VPC attachments
  attachments []dict
}

// Amazon EC2 (EBS) snapshot
private aws.ec2.snapshot @defaults("id region volumeSize state") {
  // ARN for the snapshot
  arn string
  // ID for the snapshot
  id string
  // Region where the snapshot exists
  region string
  // Users/groups that have the permissions to create volumes from the snapshot
  createVolumePermission() []dict
  // ID of the volume used to create the snapshot
  volumeId string
  // Time when the snapshot began
  startTime time
  // Time when the snapshot completed
  completionTime time
  // Tags for the snapshot
  tags map[string]string
  // State of the snapshot: pending, completed, error, recoverable, or recovering
  state string
  // Size of the volume, in GiB
  volumeSize int
  // Description of the snapshot
  description string
  // Whether the snapshot is encrypted
  encrypted bool
  // The storage tier in which the snapshot is stored
  storageTier string
}

// Amazon EC2 (EBS) volume
private aws.ec2.volume @defaults("id region volumeType size encrypted state") {
  // ARN for the EC2 volume
  arn string
  // ID of the EC2 volume
  id string
  // Information about the volume attachments
  attachments []dict
  // Whether the volume is encrypted
  encrypted bool
  // State of the volume: creating, available, in-use, and so on
  state string
  // A map of tags associated with the EBS volume
  tags map[string]string
  // Availability Zone in which the volume was created
  availabilityZone string
  // EBS volume type: gp2, gp3, io1, io2, st1, sc1, or standard
  volumeType string
  // Time the volume was created
  createTime time
  // Region where the EC2 volume is stored
  region string
  // Whether Amazon EBS Multi-Attach is enabled
  multiAttachEnabled bool
  // Throughput that the volume supports, in MiB/s.
  throughput int
  // Size of the volume, in GiBs.
  size int
  // Number of I/O operations per second (IOPS). For gp3, io1, and io2 volumes, this represents the number of IOPS that are provisioned for the volume. For gp2 volumes, this represents the baseline performance of the volume and the rate at which the volume accumulates I/O credits for bursting.
  iops int
}

// Amazon Inspector
aws.inspector {
  // List of coverage results for the AWS account
  coverages() []aws.inspector.coverage
}

// Amazon Inspector environment coverage
private aws.inspector.coverage {
  // Account ID for the coverage finding
  accountId string
  // Resource ID for the coverage finding
  resourceId string
  // Resource type, e.g. AWS_EC2_INSTANCE
  resourceType string
  // Time when the coverage finding was last scanned
  lastScannedAt time
  // Reason for the coverage finding status
  statusReason string
  // Code for the coverage finding status
  statusCode string
  // Type of coverage finding
  scanType string
  // Region where it was found
  region string
  // Details about the EC2 instance associated with the finding
  ec2Instance() aws.inspector.coverage.instance
  // Details about the ECR image associated with the finding
  ecrImage() aws.inspector.coverage.image
  // Details about the ECR repo associated with the finding
  ecrRepo() aws.inspector.coverage.repository
  // Details about the Lambda function associated with the finding
  lambda() aws.lambda.function
}

// Amazon Inspector instance coverage group
private aws.inspector.coverage.instance {
  // Platform for the EC2 instance
  platform string
  // Tags associated with the EC2 instance
  tags map[string]string
  // Image associated with the EC2 instance
  image aws.ec2.image
  // Region where the EC2 instance is found
  region string
}

// Amazon Inspector container image coverage group
private aws.inspector.coverage.image {
  // Time when the image was scanned
  imagePulledAt time
  // Tags associated with the image
  tags map[string]string
  // Region where the image is found
  region string
}

// Amazon Inspector container registry coverage group
private aws.inspector.coverage.repository {
  // Name of the ECR repository
  name string
  // Scan frequency of the ECR repo
  scanFrequency string
  // Region where the ECR repo is found
  region string
}

// Amazon EC2 instance
private aws.ec2.instance @defaults("instanceId region state instanceType architecture platformDetails") {
  // ARN for the instance
  arn string
  // Instance ID for the instance
  instanceId string
  // Whether detailed monitoring is enabled
  detailedMonitoring string
  // Region where the instance exists
  region string
  // Public IP for instance
  publicIp string
  // Amazon Systems Manager information for the instance
  ssm() dict
  // VPC associated with the instance
  vpc() aws.vpc
  // A value of "optional" denotes IMDSv1 server compatibility; "required" denotes IMDSv2
  httpTokens string
  // Status of the IMDS endpoint enabled on the instance
  httpEndpoint string
  // Patch state information about the instance
  patchState() dict
  // State of the instance: pending, running, stopping, stopped, rebooting, or terminated
  state string
  // List of devices attached to the instance (such as EBS volume)
  deviceMappings []aws.ec2.instance.device
  // List of security groups (IDs) associated with the instance
  securityGroups() []aws.ec2.securitygroup
  // Platform details
  platformDetails string
  // Public DNS name for the instance
  publicDnsName string
  // Status of the specified instance
  instanceStatus() dict
  // Reason for the most recent state transition
  stateReason dict
  // Reason for the most recent state transition
  stateTransitionReason string
  // Whether the instance has EBS optimization turned on
  ebsOptimized bool
  // Whether enhanced networking with ENA is enabled
  enaSupported bool
  // Instance type, such as t2.micro
  instanceType string
  // Tags on the instance
  tags map[string]string
  // Instance profile of the instance
  iamInstanceProfile() aws.iam.instanceProfile
  // Image that was used for the instance
  image() aws.ec2.image
  // Launch time of the instance
  launchTime time
  // Private IP address for the instance
  privateIp string
  // Private DNS name for the instance
  privateDnsName string
  // Key pair associated with the instance
  keypair() aws.ec2.keypair
  // Time when the last state transition occurred
  stateTransitionTime time
  // ARN of the VPC associated with the instance
  vpcArn string
  // Hypervisor type of the instance: ovm or xen
  hypervisor string
  // Whether this is a Spot Instance or a Scheduled Instance: spot, scheduled, or capacity-block
  instanceLifecycle string
  // Root device type used by the AMI: ebs or instance-store
  rootDeviceType string
  // Device name of the root device volume, such as /dev/sda1
  rootDeviceName string
  // Architecture of the instance
  architecture string
  // TPM version supported. NitroTPM is enabled if this value is `2.0`
  tpmSupport string
  // List of network interfaces for the instance
  networkInterfaces() []aws.ec2.networkinterface
}

// AWS EC2 network interface
private aws.ec2.networkinterface @defaults("id privateIpAddress macAddress") {
  // ID of the network interface
  id string
  // Description of the network interface
  description string
  // Subnet of the network interface
  subnet() aws.vpc.subnet
  // VPC of the network interface
  vpc() aws.vpc
  // Status of the network interface. If the network interface is not attached to an instance, the status is available; if a network interface is attached to an instance the status is in-use
  status string
  // Whether the network interface performs source/destination checking (A value of true means checking is enabled, and false means checking is disabled. The value must be false for the network interface to perform network address translation (NAT) in your VPC.)
  sourceDestCheck bool
  // Whether the network interface is being managed by an AWS service (for example, AWS Management Console, Auto Scaling, and so on)
  requesterManaged bool
  // Tags set on the interface
  tags map[string]string
  // Availability zone of the network interface
  availabilityZone string
  // Security groups associated with the network interface
  securityGroups() []aws.ec2.securitygroup
  // Whether this is an IPv6 only network interface
  ipv6Native bool
  // MAC address of the network interface
  macAddress string
  // Private DNS name of the network interface (IPv4)
  privateDnsName string
  // Private IPv4 address of the network interface
  privateIpAddress string
}

// Amazon EC2 key pair
private aws.ec2.keypair @defaults("name type region") {
  // ARN of the key pair
  arn string
  // Fingerprint for the key pair
  fingerprint string
  // Name of the key pair
  name string
  // Type of key, such as RSA
  type string
  // Tags for the key pair
  tags map[string]string
  // Region where the key pair exists
  region string
  // Date the keypair was created
  createdAt time
}

// Amazon EC2 image (AMI)
private aws.ec2.image @defaults("ownerAlias id name") {
  // ARN for the AMI
  arn string
  // ID of the image
  id string
  // Name for the image
  name string
  // Architecture associated with the image
  architecture string
  // AWS account ID of the image owner
  ownerId string
  // Alias for the image owner
  ownerAlias string
  // Date the image was created
  createdAt time
  // Date the image was deprecated
  deprecatedAt time
  // Whether enhanced networking with ENA is enabled
  enaSupport bool
  // The supported TPM version. If the image is configured for NitroTPM support, the value is v2.0
  tpmSupport string
}

// Amazon EC2 instance block device
private aws.ec2.instance.device @defaults("deviceName volumeId status") {
  // Whether the volume should be deleted on instance termination
  deleteOnTermination bool
  // Status of the device
  status string
  // Volume ID for the device
  volumeId string
  // Name for the device
  deviceName string
}

// Amazon EC2 security group
private aws.ec2.securitygroup @defaults("id name region vpc.id") {
  // Security group ARN
  arn string
  // Security group ID
  id string
  // Name of the security group
  name string
  // Description of the security group
  description string
  // A map of tags associated with the security group
  tags map[string]string
  // VPC associated with the security group
  vpc() aws.vpc
  // IP permissions (ingress) for the security group
  ipPermissions() []aws.ec2.securitygroup.ippermission
  // IP permissions (egress) for the security group
  ipPermissionsEgress() []aws.ec2.securitygroup.ippermission
  // Region associated with the security group
  region string
  // Whether the security group is attached to Amazon Elastic Compute Cloud
  isAttachedToNetworkInterface() bool
}

// Amazon EC2 security group IP permission
private aws.ec2.securitygroup.ippermission @defaults("id toPort fromPort ipProtocol ipRanges ipv6Ranges") {
  // AWS ID of the security group
  id string
  // Start of port range for TCP/UDP protocols
  fromPort int
  // End of port range for TCP/UDP protocols
  toPort int
  // IP protocol name
  ipProtocol string
  // IPv4 address ranges
  ipRanges []string
  // IPv6 address ranges
  ipv6Ranges []string
  // List of prefix IDs
  prefixListIds []dict
  // List of user ID group pairs
  userIdGroupPairs []dict
}

// AWS Config
aws.config {
  // List of configuration recorders for each region in the account
  recorders() []aws.config.recorder
  // List of AWS Config rules
  rules() []aws.config.rule
  // List of delivery channels for each region in the account
  deliveryChannels() []aws.config.deliverychannel
}

// AWS Config rule
private aws.config.rule @defaults("name id region state") {
  // ARN for the config rule
  arn string
  // State of the rule
  state string
  // Rule identifier that causes the function to evaluate resources
  source dict
  // ID of the Config rule
  id string
  // Name that you assigned to the Config rule
  name string
  // Description that provided for the Config rule
  description string
  // Region for the Config rule
  region string
}

// AWS Config recorder
private aws.config.recorder @defaults("name region") {
  // Name of the recorder
  name string
  // ARN of the IAM role used to describe the AWS resources associated with the account
  roleArn string
  // Whether the recorder records config changes for every supported type of regional resource
  allSupported bool
  // Whether the recorder records all supported types of global resources
  includeGlobalResourceTypes bool
  // Whether the recorder is currently recording
  recording bool
  // Last (previous) status of the recorder
  lastStatus string
  // Region for the recorder
  region string
  // Whether the recorder records specific resource types
  resourceTypes []string
}

// AWS Config delivery channel
private aws.config.deliverychannel @defaults("name region") {
  // Name of the delivery channel
  name string
  // S3 bucket name where configuration snapshots are delivered
  s3BucketName string
  // Prefix for the S3 bucket where configuration snapshots are delivered
  s3KeyPrefix string
  // ARN of the SNS topic that AWS Config delivers notifications to
  snsTopicARN string
  // Region for the delivery channel
  region string
}

// Amazon Elastic Kubernetes Service (EKS)
aws.eks {
  // EKS clusters
  clusters() []aws.eks.cluster
}

// Amazon EKS managed node group
private aws.eks.nodegroup @defaults("name scalingConfig.DesiredSize diskSize status") {
  // Name for the EKS node group
  name string
  // ARN for the EKS node group
  arn() string
  // Region for the EKS node group
  region string
  // Time when the EKS node group was created
  createdAt() time
  // Time when the EKS node group was last modified
  modifiedAt() time
  // Status for the EKS node group
  status() string
  // Capacity type for the EKS node group (ON_DEMAND, SPOT)
  capacityType() string
  // Scaling configuration for the EKS node group
  scalingConfig() dict
  // Instance types for the EKS node group
  instanceTypes() []string
  // AMI type for the EKS node group
  amiType() string
  // IAM role for the EKS node group
  nodeRole() aws.iam.role
  // Disk size for the EKS node group
  diskSize() int
  // Kubernetes labels applied to the EKS node group
  labels() map[string]string
  // Tags for the EKS node group
  tags()  map[string]string
  // List of autoscaling groups for the node group
  autoscalingGroups() []aws.autoscaling.group
}

// Amazon EKS add-on
private aws.eks.addon @defaults("name addonVersion status") {
  // Add-on name
  name string
  // Amazon Resource Name (ARN) of the add-on
  arn() string
  // Add-on status
  status() string
  // Add-on version
  addonVersion() string
  // Unix epoch timestamp at object creation
  createdAt() time
  // Unix epoch timestamp for the last modification to the object
  modifiedAt() time
  // Tags for the EKS node group
  tags()  map[string]string
  // Add-on publisher
  publisher() string
  // Add-on owner
  owner() string
  // Configuration values that you provided
  configurationValues() string
}

// Amazon EKS cluster
aws.eks.cluster @defaults("arn version status") {
  // Name of the cluster
  name string
  // ARN of the cluster
  arn string
  // Region for the cluster
  region string
  // A map of tags associated with the cluster
  tags map[string]string
  // Endpoint of Kubernetes API server
  endpoint string
  // Kubernetes server version
  version string
  // Amazon EKS cluster version
  platformVersion string
  // Cluster status
  status string
  // Encryption configuration for the cluster
  encryptionConfig []dict
  // Cluster logging configuration
  logging dict
  // Kubernetes network configuration
  networkConfig dict
  // VPC configuration
  resourcesVpcConfig dict
  // Cluster creation timestamp
  createdAt time
  // List of EKS node groups
  nodeGroups() []aws.eks.nodegroup
  // List of EKS add-ons
  addons() []aws.eks.addon
  // IAM role that provides permissions for the Kubernetes control plane to make calls to Amazon Web Services API operations on your behalf
  iamRole aws.iam.role
  // Kubernetes support policy of the cluster. (`STANDARD` support automatically upgrades at the end of standard support. `EXTENDED` automatically enters extended support at the end of standard support)
  supportType string
  // Cluster authentication mode
  authenticationMode string
}

// Amazon Neptune
aws.neptune @defaults("clusters") {
  // List of database clusters
  clusters() []aws.neptune.cluster
  // List of database instances
  instances() []aws.neptune.instance
}

// Amazon Neptune cluster
private aws.neptune.cluster @defaults("arn name status") {
  // ARN for the cluster
  arn string
  // Name of the cluster
  name string
  // User-supplied DB cluster identifier
  clusterIdentifier string
  // User-supplied global database cluster identifier
  globalClusterIdentifier string
  // Name of the database engine
  engine string
  // Database engine version
  engineVersion string
  // Amazon KMS key identifier for the encrypted DB cluster
  kmsKeyId string
  // Region where the cluster exists
  region string
  // Time when the cluster was created
  automaticRestartTime time
  // List of EC2 Availability Zones
  availabilityZones []string
  // Number of days automatic DB snapshots are retained
  backupRetentionPeriod int
  // Time when the cluster was created
  createdAt time
  // Whether the DB cluster can be cloned across accounts
  crossAccountClone bool
  // DB cluster parameter group for the DB cluster
  clusterParameterGroup string
  // Subnet group associated with the DB cluster
  subnetGroup string
  // Amazon Region-unique, immutable identifier for the DB cluster
  clusterResourceId string
  // Whether the DB cluster has deletion protection enabled
  deletionProtection bool
  // Earliest time to which a database can be restored
  earliestRestorableTime time
  // List of log types that this cluster is configured to export to CloudWatch logs
  enabledCloudwatchLogsExports []string
  // Connection endpoint for the primary instance
  endpoint string
  // Whether mapping of Amazon Identity and Access Management (IAM) accounts to database accounts is enabled
  iamDatabaseAuthenticationEnabled bool
  // Latest time to which a database can be restored
  latestRestorableTime time
  // Username
  masterUsername string
  // Whether the cluster has instances in multiple availability zones
  multiAZ bool
  // Port on which the database engine is listening
  port int
  // Daily time range during which automated backups are created
  preferredBackupWindow string
  // Weekly time range during which system maintenance can occur
  preferredMaintenanceWindow string
  // Status of the cluster
  status string
  // Whether the DB cluster is encrypted
  storageEncrypted bool
  // Storage type
  storageType string
}

// Amazon Neptune instance
private aws.neptune.instance @defaults("arn name status"){
  // ARN for the instance
  arn string
  // Name of the instance
  name string
  // User-supplied DB cluster identifier
  clusterIdentifier string
  // Whether minor version patches are applied automatically
  autoMinorVersionUpgrade bool
  // Name of the availability zone
  availabilityZone string
  // Number of days automatic DB snapshots are retained
  backupRetentionPeriod int
  // Name of the compute and memory capacity class
  instanceClass string
  // Status of the instance
  status string
  // Port on which the database engine is listening
  port int
  // Whether the instance has deletion protection enabled
  deletionProtection bool
  // List of log types that this DB instance is configured to export to CloudWatch logs
  enabledCloudwatchLogsExports []string
  // Connection endpoint
  endpoint dict
  // Name of the database engine
  engine string
  // Database engine version
  engineVersion string
  // Amazon CloudWatch Log ARN log stream to which the database writes the audit log
  enhancedMonitoringResourceArn string
  // Whether mapping of Amazon Identity and Access Management (IAM) accounts to database accounts is enabled
  iamDatabaseAuthenticationEnabled bool
  // Time when the cluster was created
  createdAt time
  // Amazon KMS key identifier for the encrypted DB instance
  kmsKeyId string
  // Latest time to which a database can be restored
  latestRestorableTime time
  // Username
  masterUsername string
  // Interval, in seconds, between points when Enhanced Monitoring metrics are collected
  monitoringInterval int
  // ARN for the IAM role that permits Neptune to send Enhanced Monitoring metrics to Amazon CloudWatch Logs
  monitoringRoleArn string
  // Whether the cluster has instances in multiple availability zones
  multiAZ bool
  // Daily time range during which automated backups are created
  preferredBackupWindow string
  // Weekly time range during which system maintenance can occur
  preferredMaintenanceWindow string
  // Order in which a Read Replica is promoted
  promotionTier int
  // Region where the cluster exists
  region string
  // Whether the DB cluster is encrypted
  storageEncrypted bool
  // Storage type
  storageType string
  // Key store with which the instance is associated for TDE encryption
  tdeCredentialArn string
}

// Amazon Timestream for LiveAnalytics
aws.timestream.liveanalytics @defaults("databases") {
  // List of databases
  databases() []aws.timestream.liveanalytics.database
  // List of database tables
  tables() []aws.timestream.liveanalytics.table
}

// Amazon Timestream for LiveAnalytics database
private aws.timestream.liveanalytics.database @defaults("name region") {
  // ARN for the database
  arn string
  // Name of the database
  name string
  // KMS key used to encrypt the data stored in the database
  kmsKeyId string
  // Region where the database exists
  region string
  // Time when the database was created
  createdAt time
  // Time when the database was last updated
  updatedAt time
  // Total number of tables in database
  tableCount int
}

// Amazon Timestream for LiveAnalytics table
private aws.timestream.liveanalytics.table @defaults("name region") {
  // ARN for the table
  arn string
  // Name of the table
  name string
  // Name of the database
  databaseName string
  // Region where the table exists
  region string
  // Time when the table was created
  createdAt time
  // Time when the table was last updated
  updatedAt time
  // Magnetic store properties for the table
  magneticStoreWriteProperties dict
  // Retention duration properties for the table
  retentionProperties dict
}

// AWS CodeDeploy
aws.codedeploy {
  // List of CodeDeploy applications across all enabled regions
  applications() []aws.codedeploy.application
}

// AWS CodeDeploy Application
private aws.codedeploy.application @defaults("applicationName computePlatform createdAt region") {
  // ARN of the application
  arn string
  // ID of the application
  applicationId string
  // Name of the application
  applicationName string
  // Compute platform of the application (Server, Lambda, or ECS)
  computePlatform string
  // Time the application was created
  createdAt time
  // Whether the application is linked to a GitHub account
  linkedToGitHub bool
  // Tags for the application
  tags() map[string]string
  // List of deployment groups for this application
  deploymentGroups() []aws.codedeploy.deploymentGroup
  // List of deployments for this application
  deployments() []aws.codedeploy.deployment
  // Region of the application
  region string
}

// AWS CodeDeploy Deployment Group
private aws.codedeploy.deploymentGroup @defaults("deploymentGroupName computePlatform deploymentGroupId region") {
  // Application name this deployment group belongs to
  applicationName string
  // ARN of the deployment group
  arn string
  // ID of the deployment group
  deploymentGroupId string
  // Name of the deployment group
  deploymentGroupName string
  // Compute platform of the deployment group (Server, Lambda, or ECS)
  computePlatform string
  // Service role ARN for the deployment group
  serviceRoleArn string
  // Target revision for the deployment group (includes type, location, etc.)
  targetRevision() dict
  // Tags for the deployment group
  tags() map[string]string
  // Region of the deployment group
  region string
  // List of deployments for this deployment group
  deployments() []aws.codedeploy.deployment
  // Auto scaling groups associated with the deployment group
  autoScalingGroups() []aws.autoscaling.group
  // EC2 tag filters for the deployment group
  ec2TagFilters() []dict
  // On-premises instance tag filters for the deployment group
  onPremisesInstanceTagFilters() []dict
  // Last successful deployment information
  lastSuccessfulDeployment() aws.codedeploy.deployment
  // Last attempted deployment information
  lastAttemptedDeployment() aws.codedeploy.deployment
  // Deployment style (BLUE_GREEN or IN_PLACE)
  deploymentStyle() dict
  // Blue/green deployment configuration
  blueGreenDeploymentConfiguration() dict
  // Load balancer info
  loadBalancerInfo() dict
}

// AWS CodeDeploy Deployment
private aws.codedeploy.deployment @defaults("deploymentId status applicationName deploymentGroupName createdAt region") {
  // Application name for the deployment
  applicationName string
  // Deployment ID
  deploymentId string
  // ARN of the deployment (Note: Deployments themselves don't have ARNs, this will be a synthetic ID or the deploymentId itself)
  arn string
  // Status of the deployment (Created, Queued, InProgress, Succeeded, Failed, Stopped, Ready)
  status string
  // Deployment group name
  deploymentGroupName string
  // Deployment configuration name
  deploymentConfigName string
  // Time the deployment was created
  createdAt time
  // Time the deployment was completed
  compleatedAt time
  // Description of the deployment
  description string
  // Creator of the deployment (user, autoscaling, codeDeployRollback, etc.)
  creator string
  // Whether to ignore application stop failures
  ignoreApplicationStopFailures bool
  // Information about the instances targeted by the deployment (complex structure, represented as dict)
  targetInstances() dict
  // Revision information for the deployment (S3 location, GitHub location, etc.)
  revision() dict
  // Region of the deployment
  region string
  // Error information, if any
  errorInformation() dict
  // Deployment overview (counts for Pending, InProgress, Succeeded, Failed, Skipped, Ready)
  deploymentOverview() dict
  // Whether this deployment is a rollback
  isRollback() bool
  // Rollback information if this deployment is a rollback or was rolled back
  rollbackInfo() dict
}
