# Copyright (c) Mondoo, Inc.
# SPDX-License-Identifier: BUSL-1.1

resources:
  aws:
    docs:
      desc: |
        Use the `aws` resource to assess the configuration of AWS accounts. It features the `aws.regions` field, which returns a list of enabled AWS regions in the account, and the `aws.vpcs` field, which provides a list of VPCs configured within the account.
    fields:
      regions: {}
      vpcs: {}
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
    refs:
    - title: 'AWS Documentation: Managing AWS Regions'
      url: https://docs.aws.amazon.com/general/latest/gr/rande-manage.html
    - title: 'AWS Documentation: Security in Amazon Virtual Private Cloud'
      url: https://docs.aws.amazon.com/vpc/latest/userguide/security.html
    snippets:
    - query: aws.regions
      title: List all enabled regions within the AWS account
    - query: aws.vpcs
      title: List of `aws.vpc` resources for all VPCs across all enabled regions
    - query: "aws.vpcs {\n  arn \n  id \n  state \n  isDefault \n  region \n  flowLogs\n
        \ routeTables\n}\n"
      title: List of `aws.vpc` resources for all VPCs across all enabled regions and
        the values for specified fields
    - query: |
        aws.vpcs.all( flowLogs.any(status == "ACTIVE") )
      title: Ensure VPC flow logging is enabled in all VPCs
  aws.account:
    docs:
      desc: |
        The `aws.account` resource provides configuration for AWS accounts, including the account number and configured aliases.
    fields:
      aliases: {}
      id: {}
      organization:
        min_mondoo_version: 6.11.0
      tags:
        min_mondoo_version: 9.0.0
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
    snippets:
    - query: "aws.account { \n  id \n  aliases \n}\n"
      title: Return the account ID (number) and any configured account aliases
  aws.acm:
    docs:
      desc: Use the `aws.acm` resource to assess the configuration of the AWS Certificates
        Manager service in the account. This resource returns a list of ACM certificates
        found in the account.
    fields:
      certificates: {}
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
    refs:
    - title: What Is AWS Certificate Manager?
      url: https://docs.aws.amazon.com/acm/latest/userguide/acm-overview.html
    - title: Security in AWS Certificate Manager
      url: https://docs.aws.amazon.com/acm/latest/userguide/security.html
    snippets:
    - query: aws.acm.certificates
      title: Return a list of `aws.acm.certificate` resources within the AWS account
    - query: "aws.acm.certificates {\n  arn\n  notBefore\n  notAfter \n  createdAt\n
        \ domainName\n  status\n  subject\n  certificate() \n}\n"
      title: Return a list of `aws.acm.certificate` resources within the AWS account
        along with values for specified fields
    - query: |
        aws.acm.certificates.
          where( status != /PENDING_VALIDATION/ ).
          all (notAfter - notBefore <= 90 * time.day)
      title: Check whether ACM certificates in your account are marked for expiration
        within 90 days
  aws.acm.certificate:
    docs:
      desc: |
        The `aws.acm.certificate` resource provides fields for assessing the configuration of AWS ACM certificates. For usage, read the `aws.acm` resource documentation.
    fields:
      arn: {}
      certificate: {}
      createdAt: {}
      domainName: {}
      importedAt:
        min_mondoo_version: 9.0.0
      issuedAt:
        min_mondoo_version: 9.0.0
      issuer:
        min_mondoo_version: 9.0.0
      keyAlgorithm:
        min_mondoo_version: 9.0.0
      notAfter: {}
      notBefore: {}
      serial:
        min_mondoo_version: 9.0.0
      source:
        min_mondoo_version: 9.0.0
      status: {}
      subject: {}
      tags:
        min_mondoo_version: 5.16.0
    is_private: true
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
  aws.apigateway:
    docs:
      desc: Use the `aws.apigateway` resource to assess the configuration of the AWS
        API Gateway service.
    fields:
      restApis: {}
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
    refs:
    - title: What is Amazon API Gateway?
      url: https://docs.aws.amazon.com/apigateway/latest/developerguide/welcome.html
    - title: Security in Amazon API Gateway
      url: https://docs.aws.amazon.com/apigateway/latest/developerguide/security.html
    snippets:
    - query: aws.apigateway.restApis
      title: Return a list of `aws.apigateway.restapi` resources for all REST APIs
        configured with the account across all enabled regions
    - query: |
        aws.apigateway.restApis {
          createdDate
          description
          stages
          region
          arn
          id
          name
        }
      title: Return a list of `aws.apigateway.restapi` resources for all REST APIs
        configured with the account across all enabled regions and the value for specified
        fields
    - query: "aws.apigateway.restApis.all(stages.all(\n  methodSettings['CachingEnabled']
        == true && \n    methodSettings['CacheDataEncrypted'] == true\n))\n"
      title: Check that all methods in Amazon API Gateway have caching enabled and
        encrypted
    - query: |
        aws.apigateway.restApis.all(stages.all(
        methodSettings['LoggingLevel'] == "ERROR" || methodSettings['LoggingLevel'] == "INFO"
        ))
      title: Check that all methods in Amazon API Gateway have logging enabled
  aws.apigateway.restapi:
    docs:
      desc: |
        The `aws.apigateway.restapi` resource provides fields representing an individual REST API configured within the AWS account. For usage, read the `aws.apigateway` resource documentation.
    fields:
      arn: {}
      createdDate: {}
      description: {}
      id: {}
      name: {}
      region: {}
      stages: {}
      tags:
        min_mondoo_version: 5.16.0
    is_private: true
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
  aws.apigateway.stage:
    docs:
      desc: |
        The `aws.apigateway.stage` resource provides fields representing an individual stage configured on a REST API. For usage, read the `aws.apigateway` resource documentation.
    fields:
      arn: {}
      deploymentId: {}
      description: {}
      methodSettings: {}
      name: {}
      tracingEnabled: {}
    is_private: true
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
    snippets:
    - query: "aws.apigateway.restApis { \n  arn \n  stages \n}\n"
      title: Return a list of AWS API Gateway REST APIs configured across all enabled
        regions in the AWS account and the values for the arn and stages
  aws.applicationAutoscaling:
    docs:
      desc: |
        The aws.applicationAutoscaling resource inspects targets in the AWS Application Auto Scaling service. The AWS Auto Scaling service allows users to load balance multiple AWS services including:
          - AppStream 2.0 fleets
          - Aurora replicas
          - Amazon Comprehend document classification and entity recognizer endpoints
          - DynamoDB tables and global secondary indexes
          - Amazon Elastic Container Service (ECS) services
          - ElastiCache for Redis clusters (replication groups)
          - Amazon EMR clusters
          - Amazon Keyspaces (for Apache Cassandra) tables
          - Lambda function provisioned concurrency
          - Amazon Managed Streaming for Apache Kafka (MSK) broker storage
          - Amazon Neptune clusters
          - SageMaker endpoint variants
          - SageMaker inference components
          - SageMaker Serverless provisioned concurrency
          - Spot Fleet requests
    fields:
      namespace: {}
      scalableTargets: {}
    min_mondoo_version: 6.11.1
    platform:
      name:
      - aws
  aws.applicationAutoscaling.target:
    fields:
      arn: {}
      createdAt:
        min_mondoo_version: 9.0.0
      maxCapacity: {}
      minCapacity: {}
      namespace: {}
      scalableDimension: {}
      suspendedState: {}
    is_private: true
    min_mondoo_version: 6.11.1
    platform:
      name:
      - aws
    refs:
    - title: What is Application Auto Scaling?
      url: https://docs.aws.amazon.com/autoscaling/application/userguide/what-is-application-auto-scaling.html
  aws.autoscaling:
    docs:
      desc: |
        Use the `aws.autoscaling` resource to assess the configuration of AWS auto scaling groups within an AWS account. This resource provides the `.groups` field, which returns a list of all auto scaling groups configured across all enabled regions across the account.
    fields:
      groups: {}
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
    refs:
    - title: Security in Amazon EC2 Auto Scaling
      url: https://docs.aws.amazon.com/autoscaling/ec2/userguide/security.html
    - title: Compliance validation for Amazon EC2 Auto Scaling
      url: https://docs.aws.amazon.com/autoscaling/ec2/userguide/ec2-auto-scaling-compliance.html
    snippets:
    - query: "aws.autoscaling.groups { \n  arn \n  healthCheckType \n  loadBalancerNames
        \n  name \n}\n"
      title: Return a list of all auto-scaling groups configured across all enabled
        regions across the account and the values for specified fields
    - query: "aws.autoscaling.groups.where(loadBalancerNames.length > 0) { \n  healthCheckType
        == \"ELB\" \n}\n"
      title: Check that all autoscaling groups associated with a load balancer use
        health checks
  aws.autoscaling.group:
    docs:
      desc: |
        The `aws.autoscaling.group` resource provides fields representing an individual AWS auto scaling group within the account. For usage, read the `aws.autoscaling` resource documentation.
    fields:
      arn: {}
      availabilityZones:
        min_mondoo_version: 9.0.0
      capacityRebalance:
        min_mondoo_version: 9.0.0
      createdAt:
        min_mondoo_version: 9.0.0
      defaultCooldown:
        min_mondoo_version: 9.0.0
      defaultInstanceWarmup:
        min_mondoo_version: 9.0.0
      desiredCapacity:
        min_mondoo_version: 9.0.0
      healthCheckGracePeriod:
        min_mondoo_version: 9.0.0
      healthCheckType: {}
      instances:
        min_mondoo_version: 9.0.0
      launchConfigurationName:
        min_mondoo_version: 9.0.0
      loadBalancerNames: {}
      maxInstanceLifetime:
        min_mondoo_version: 9.0.0
      maxSize:
        min_mondoo_version: 9.0.0
      minSize:
        min_mondoo_version: 9.0.0
      name: {}
      region:
        min_mondoo_version: 9.0.0
      tags:
        min_mondoo_version: 5.16.0
    is_private: true
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
  aws.backup:
    docs:
      desc: |
        Use the `aws.backup` resource to assess the configuration of AWS Backup vaults in an AWS account. This resource provides the `.vaults` field, which returns a list of all AWS Backup vaults configured across all enabled regions across the account.
    fields:
      vaults: {}
    min_mondoo_version: latest
    platform:
      name:
      - aws
    refs:
    - title: What is AWS Backup?
      url: https://docs.aws.amazon.com/aws-backup/latest/devguide/whatisbackup.html
    - title: Compliance validation for AWS Backup
      url: https://docs.aws.amazon.com/aws-backup/latest/devguide/backup-compliance.html
    snippets:
    - query: "aws.backup.vaults { \n  arn \n  region \n  recoveryPoints \n  name \n}\n"
      title: Return a list of all AWS Backup vaults configured across all enabled
        regions across the account and all their recovery points
  aws.backup.vault:
    docs:
      desc: |
        The `aws.backup.vault` resource provides fields representing an individual AWS Backup vaults within the account. For usage, read the `aws.backup` resource documentation.
    fields:
      arn: {}
      createdAt: {}
      encryptionKeyArn: {}
      locked: {}
      name: {}
      recoveryPoints: {}
      region: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - aws
  aws.backup.vaultRecoveryPoint:
    fields:
      arn: {}
      completionDate: {}
      createdBy: {}
      creationDate: {}
      encryptionKeyArn: {}
      iamRoleArn: {}
      isEncrypted: {}
      resourceType: {}
      status: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - aws
  aws.cloudfront:
    fields:
      distributions: {}
      functions: {}
    min_mondoo_version: latest
    platform:
      name:
      - aws
  aws.cloudfront.distribution:
    fields:
      arn: {}
      cacheBehaviors: {}
      cnames: {}
      defaultCacheBehavior: {}
      domainName: {}
      enabled: {}
      httpVersion: {}
      isIPV6Enabled: {}
      origins: {}
      priceClass: {}
      status: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - aws
  aws.cloudfront.distribution.origin:
    fields:
      account: {}
      connectionAttempts: {}
      connectionTimeout: {}
      domainName: {}
      id: {}
      originPath: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - aws
  aws.cloudfront.function:
    fields:
      arn: {}
      comment: {}
      createdAt:
        min_mondoo_version: 9.0.0
      createdTime: {}
      lastModifiedTime: {}
      name: {}
      runtime: {}
      stage: {}
      status: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - aws
  aws.cloudtrail:
    docs:
      desc: |
        Use the `aws.cloudtrail` resource to assess the configuration of AWS CloudTrails deployed within an AWS account. The resource provides the `.trails` field, which creates a list of `aws.cloudtrail.trail` resources representing all CloudTrails configured across every enabled region.
    fields:
      trails: {}
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
    refs:
    - title: Security best practices in AWS CloudTrail
      url: https://docs.aws.amazon.com/awscloudtrail/latest/userguide/best-practices-security.html
    snippets:
    - query: |
        aws.cloudtrail.trails {
          arn
          name
          kmsKey
          isMultiRegionTrail
          isOrganizationTrail
          logFileValidationEnabled
          includeGlobalServiceEvents
          s3bucket
          snsTopicARN
          status
          logGroup
          cloudWatchLogsRoleArn
          cloudWatchLogsLogGroupArn
          eventSelectors
          region
        }
      title: Return a list of `aws.cloudtrail.trail` resources representing all AWS
        Cloud Trails configured across all enabled regions and the values for specified
        fields
    - query: "aws.cloudtrail.trails.any( \n  isMultiRegionTrail && status['IsLogging']
        \n)\n"
  aws.cloudtrail.trail:
    docs:
      desc: |
        The `aws.cloudtrail.trail` object represents an individual AWS CloudTrail configured within an account. For usage, read the `aws.cloudtrail` resource documentation.
    fields:
      arn: {}
      cloudWatchLogsLogGroupArn: {}
      cloudWatchLogsRoleArn: {}
      eventSelectors: {}
      includeGlobalServiceEvents: {}
      isMultiRegionTrail: {}
      isOrganizationTrail: {}
      kmsKey: {}
      logFileValidationEnabled: {}
      logGroup: {}
      name: {}
      region: {}
      s3bucket: {}
      snsTopicARN: {}
      status: {}
    is_private: true
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
  aws.cloudwatch:
    docs:
      desc: |
        Use the `aws.cloudwatch` resource to assess the configuration of the AWS CloudWatch service.
    fields:
      alarms: {}
      logGroups: {}
      metrics: {}
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
    snippets:
    - query: aws.cloudwatch.logGroups
      title: Return a list of `aws.cloudwatch.loggroup` resources representing individual
        CloudWatch log groups configured across all enabled regions
    - query: aws.cloudwatch.metrics
      title: Return a list of `aws.cloudwatch.metric` resources representing individual
        CloudWatch metrics configured across all enabled regions
    - query: |
        aws.cloudwatch.alarms.all(
          actions.length > 0 == true &&
          insufficientDataActions.length > 0 == props.cloudwatchAlarmInsufficientDataActionRequired &&
          okActions.length > 0 == props.cloudwatchAlarmOkActionRequired
        )
      title: Check that all cloudwatch alarms have at least one action enabled
  aws.cloudwatch.loggroup:
    docs:
      desc: |
        The `aws.cloudwatch.loggroup` object represents an individual AWS CloudWatch log group configured within an account. For usage, read the `aws.cloudwatch` resource documentation.
    fields:
      arn: {}
      kmsKey: {}
      metricsFilters: {}
      name: {}
      region: {}
      retentionInDays:
        min_mondoo_version: 9.0.0
    is_private: true
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
  aws.cloudwatch.loggroup.metricsfilter:
    docs:
      desc: |
        The `aws.cloudwatch.metricsfilter` object represents an individual AWS CloudWatch metrics filter configured within an account. For usage, read the `aws.cloudwatch` resource documentation.
    fields:
      filterName: {}
      filterPattern: {}
      id: {}
      metrics: {}
    is_private: true
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
  aws.cloudwatch.metric:
    docs:
      desc: |
        The `aws.cloudwatch.metric` object represents an individual AWS CloudWatch metric configured within an account. For usage, read the `aws.cloudwatch` resource documentation.
    fields:
      alarms: {}
      dimensions:
        min_mondoo_version: 5.17.1
      name: {}
      namespace: {}
      region: {}
      statistics:
        min_mondoo_version: 5.17.1
    is_private: true
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
  aws.cloudwatch.metric.datapoint:
    fields:
      average: {}
      id: {}
      maximum: {}
      minimum: {}
      sum: {}
      timestamp: {}
      unit: {}
    is_private: true
    min_mondoo_version: 5.17.1
    platform:
      name:
      - aws
  aws.cloudwatch.metricdimension:
    fields:
      name: {}
      value: {}
    min_mondoo_version: 5.17.1
    platform:
      name:
      - aws
  aws.cloudwatch.metricsalarm:
    docs:
      desc: |
        The `aws.cloudwatch.metricsalarm` object represents an individual AWS CloudWatch metric alarm configured within an account. For usage, read the `aws.cloudwatch` resource documentation.
    fields:
      actions: {}
      arn: {}
      insufficientDataActions: {}
      metricName: {}
      metricNamespace: {}
      name: {}
      okActions: {}
      region: {}
      state: {}
      stateReason: {}
    is_private: true
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
  aws.cloudwatch.metricstatistics:
    fields:
      datapoints: {}
      label: {}
      name: {}
      namespace: {}
      region: {}
    min_mondoo_version: 5.17.1
    platform:
      name:
      - aws
  aws.codebuild:
    docs:
      desc: "Use the `aws.codebuild` resource to assess the configuration of the AWS
        CodeBuild service and the projects within. \n"
    fields:
      projects: {}
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
    refs:
    - title: Security in AWS CodeBuild
      url: https://docs.aws.amazon.com/codebuild/latest/userguide/security.html
    snippets:
    - query: |
        aws.codebuild.projects {
          arn
          description
          name
          environment
          region
          source
        }
      title: Return a list of `aws.codebuild.project` resources representing all AWS
        CodeBuild projects configured across all enabled regions within the account
        and the values for specified fields
    - query: "aws.codebuild.projects { \n  environment ['EnvironmentVariables'].where(_['Name']
        == \"AWS_ACCESS_KEY_ID\") { _['Type'] != \"PLAINTEXT\"}\n  environment ['EnvironmentVariables'].where(_['Name']
        == \"AWS_SECRET_ACCESS_KEY\") { _['Type'] != \"PLAINTEXT\"} \n}\n"
      title: Check that all projects containing env variables AWS_ACCESS_KEY_ID and
        AWS_SECRET_ACCESS_KEY are not in plaintext
    - query: "aws.codebuild.projects.where( source['Type'] == \"BITBUCKET\" || source['Type']
        == \"GITHUB\" ) { \n  source['Auth']['Type'] == \"OAUTH\"\n}\n"
      title: Check that all projects using GitHub or Bitbucket as the source use oauth
  aws.codebuild.project:
    docs:
      desc: |
        The `aws.codebuild.project` resource provides fields representing an individual AWS CodeBuild project configured within the account. For usage, read the `aws.codebuild` documentation.
    fields:
      arn: {}
      description: {}
      environment: {}
      name: {}
      region: {}
      source: {}
      tags:
        min_mondoo_version: 5.16.0
    is_private: true
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
  aws.codedeploy:
    fields:
      applications: {}
    min_mondoo_version: 9.0.0
    platform:
      name:
      - aws
  aws.codedeploy.application:
    fields:
      applicationId: {}
      applicationName: {}
      arn: {}
      computePlatform: {}
      createdAt: {}
      deploymentGroups: {}
      deployments: {}
      linkedToGitHub: {}
      region: {}
      tags: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - aws
  aws.codedeploy.deployment:
    fields:
      applicationName: {}
      arn: {}
      compleatedAt: {}
      createdAt: {}
      creator: {}
      deploymentConfigName: {}
      deploymentGroupName: {}
      deploymentId: {}
      deploymentOverview: {}
      description: {}
      errorInformation: {}
      ignoreApplicationStopFailures: {}
      isRollback: {}
      region: {}
      revision: {}
      rollbackInfo: {}
      status: {}
      targetInstances: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - aws
  aws.codedeploy.deploymentGroup:
    fields:
      applicationName: {}
      arn: {}
      autoScalingGroups: {}
      blueGreenDeploymentConfiguration: {}
      computePlatform: {}
      deploymentGroupId: {}
      deploymentGroupName: {}
      deploymentStyle: {}
      deployments: {}
      ec2TagFilters: {}
      lastAttemptedDeployment: {}
      lastSuccessfulDeployment: {}
      loadBalancerInfo: {}
      onPremisesInstanceTagFilters: {}
      region: {}
      serviceRoleArn: {}
      tags: {}
      targetRevision: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - aws
  aws.codedeploy.deploymentgroup:
    fields:
      applicationName: {}
      arn: {}
      autoScalingGroups: {}
      blueGreenDeploymentConfiguration: {}
      computePlatform: {}
      deploymentGroupId: {}
      deploymentGroupName: {}
      deploymentStyle: {}
      deployments: {}
      ec2TagFilters: {}
      lastAttemptedDeployment: {}
      lastSuccessfulDeployment: {}
      loadBalancerInfo: {}
      onPremisesInstanceTagFilters: {}
      region: {}
      serviceRoleArn: {}
      tags: {}
      targetRevision: {}
    min_mondoo_version: 9.0.0
    platform:
      name:
      - aws
  aws.config:
    docs:
      desc: |
        Use the `aws.config` resource to assess the configuration of the AWS Config service. The resource provides the `.recorders` field, which returns a list of `aws.config.recorder` resources representing all AWS Config recorders configured across all enabled regions, as well as the `.rules` field, which returns a list of `aws.config.rule` resources representing all AWS Config rules configured across all enabled regions in the account.
    fields:
      deliveryChannels:
        min_mondoo_version: 9.0.0
      recorders: {}
      rules: {}
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
    refs:
    - title: Security in AWS Config
      url: https://docs.aws.amazon.com/config/latest/developerguide/security.html
    - title: AWS Config best practices
      url: https://aws.amazon.com/blogs/mt/aws-config-best-practices/
    snippets:
    - query: "aws.config.rules {\n  arn \n  state \n  source\n}\n"
      title: Return a list of `aws.config.rule` resources representing AWS Config
        rules configured across all enabled regions and the values for specified fields
    - query: "aws.config.recorders {\n  name\n  roleArn\n  allSupported\n  includeGlobalResourceTypes
        \n  resourceTypes\n  recording\n  lastStatus\n  region\n}\n"
      title: Return a list of AWS Config recorders configured across all enabled regions
        and the values for specified fields
    - query: |
        aws.config.recorders.any(allSupported == true && includeGlobalResourceTypes == true)
        aws.config.recorders.where(allSupported == true && includeGlobalResourceTypes == true).all(
          recording == true && lastStatus == "SUCCESS"
        )
      title: Ensure AWS Config is enabled in all regions
  aws.config.deliverychannel:
    docs:
      desc: |
        The `aws.config.deliverychannel` resource provides fields representing an individual AWS Config delivery channel configured within an account. For usage, read the `aws.config` resource documentation.
    fields:
      name: {}
      region: {}
      s3BucketName: {}
      s3KeyPrefix: {}
      snsTopicARN: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - aws
  aws.config.recorder:
    docs:
      desc: "The `aws.config.recorder` resource provides fields representing an individual
        AWS Config recorder configured within an account. For usage, read the `aws.config`
        resource documentation. \n"
    fields:
      allSupported: {}
      includeGlobalResourceTypes: {}
      lastStatus: {}
      name: {}
      recording: {}
      region: {}
      resourceTypes: {}
      roleArn: {}
    is_private: true
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
  aws.config.rule:
    docs:
      desc: |
        The `aws.config.rule` resource provides fields representing an individual AWS Config rule configured within an account. For usage, read the `aws.config` resource documentation.
    fields:
      arn: {}
      description:
        min_mondoo_version: 9.0.0
      id:
        min_mondoo_version: 9.0.0
      name:
        min_mondoo_version: 9.0.0
      region:
        min_mondoo_version: 9.0.0
      source: {}
      state: {}
    is_private: true
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
  aws.dms:
    docs:
      desc: |
        Use the `aws.dms` resource to assess the configuration of the AWS Database Migration service.
    fields:
      replicationInstances: {}
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
    refs:
    - title: Security in AWS Database Migration Service
      url: https://docs.aws.amazon.com/dms/latest/userguide/CHAP_Security.html
    snippets:
    - query: aws.dms.replicationInstances
      title: Return a list of AWS Database Migration service replication instances
    - query: |
        aws.dms.replicationInstances.none(
          _['PubliclyAccessible'] == true
        )
      title: Check whether AWS Database Migration Service replication instances are
        public
  aws.dynamodb:
    docs:
      desc: |
        Use the `aws.dynamodb` resource to assess the configuration of the AWS DynamoDB.
    fields:
      backups: {}
      exports:
        min_mondoo_version: 9.0.0
      globalTables: {}
      limits: {}
      tables: {}
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
    refs:
    - title: Security and Compliance in Amazon DynamoDB
      url: https://docs.aws.amazon.com/amazondynamodb/latest/developerguide/security.html
    snippets:
    - query: "aws.dynamodb.tables {\n  arn \n  name \n  region \n  backups \n  sseDescription
        \n  provisionedThroughput \n  continuousBackups \n}\n"
      title: Return a list of `aws.dynamodb.table` resources and the values for specified
        fields
    - query: "aws.dynamodb.limits {\n  arn \n  region \n  accountMaxRead \n  accountMaxWrite
        \n  tableMaxRead\n  tableMaxWrite \n}\n"
      title: Return a list of `aws.dynamodb.limit` resources and the values for specified
        fields
    - query: "aws.dynamodb.globaltables {\n  arn \n  name \n  replicaSettings \n}\n"
      title: Return a list of `aws.dynamodb.globaltable` resources and the value for
        specified fields
    - query: "aws.dynamodb.tables.all( \n  sseDescription['SSEType'] == 'KMS' && sseDescription['Status']
        == 'ENABLED'\n)\n"
      title: Check that all DynamoDB tables are encrypted with AWS Key Management
        Service (KMS)
  aws.dynamodb.export:
    fields:
      arn: {}
      endTime: {}
      format: {}
      itemCount: {}
      kmsKey: {}
      s3Bucket: {}
      s3Prefix: {}
      s3SseAlgorithm: {}
      startTime: {}
      status: {}
      table: {}
      type: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - aws
  aws.dynamodb.globaltable:
    docs:
      desc: |
        The `aws.dynamodb.globaltable` resource provides fields representing a DynamoDB global table. For usage, see `aws.dynamodb` resource documentation.
    fields:
      arn: {}
      name: {}
      replicaSettings: {}
      tags:
        min_mondoo_version: 5.16.0
    is_private: true
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
  aws.dynamodb.limit:
    docs:
      desc: |
        The `aws.dynamodb.limit` resource provides fields representing a DynamoDB limit configuration. For usage, see `aws.dynamodb` resource documentation.
    fields:
      accountMaxRead: {}
      accountMaxWrite: {}
      arn: {}
      region: {}
      tableMaxRead: {}
      tableMaxWrite: {}
    is_private: true
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
  aws.dynamodb.table:
    docs:
      desc: |
        The `aws.dynamodb.table` resource provides fields representing a DynamoDB table. For usage, see `aws.dynamodb` resource documentation.
    fields:
      arn: {}
      backups: {}
      continuousBackups: {}
      createdAt:
        min_mondoo_version: 9.0.0
      createdTime:
        min_mondoo_version: 9.0.0
      deletionProtectionEnabled:
        min_mondoo_version: 9.0.0
      globalTableVersion:
        min_mondoo_version: 9.0.0
      id:
        min_mondoo_version: 9.0.0
      items:
        min_mondoo_version: 9.0.0
      latestStreamArn:
        min_mondoo_version: 9.0.0
      name: {}
      provisionedThroughput: {}
      region: {}
      sizeBytes:
        min_mondoo_version: 9.0.0
      sseDescription: {}
      status:
        min_mondoo_version: 9.0.0
      tags:
        min_mondoo_version: 5.16.0
    is_private: true
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
  aws.ec2:
    docs:
      desc: |
        Use the `aws.ec2` resource to assess the configuration of AWS EC2 instances.
    fields:
      ebsEncryptionByDefault: {}
      eips:
        min_mondoo_version: 9.0.0
      instances: {}
      internetGateways: {}
      keypairs: {}
      networkAcls: {}
      securityGroups: {}
      snapshots: {}
      volumes: {}
      vpnConnections: {}
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
    refs:
    - title: Security in Amazon EC2
      url: https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/ec2-security.html
    - title: Security in Amazon Virtual Private Cloud
      url: https://docs.aws.amazon.com/vpc/latest/userguide/security.html
    snippets:
    - query: "aws.ec2.instances {\n  arn\n  instanceId \n  detailedMonitoring \n  region
        \n  publicIp \n  ssm \n  vpc \n  httpTokens \n  patchState \n  state \n  deviceMappings
        \n  securityGroups\n  publicDnsName \n  instanceStatus\n  stateReason \n  stateTransitionReason
        \n  ebsOptimized \n  instanceType \n  tags \n  image\n  launchTime \n}\n"
      title: Return a list of all EC2 instances across all enabled regions in the
        account and the values for specified fields
    - query: |
        aws.ec2.securityGroups {
          arn
          region
          vpc
          id
        }
      title: Return a list of security groups across every enabled region and the
        value for specified fields
    - query: "aws.ec2.volumes {\n  arn\n  region \n}\n"
      title: Return a list of all EBS volumes along with the associated ARN and the
        region the volume exists in
    - query: |
        aws.ec2.securityGroups.where(name == "default") {
          ipPermissions.all(ipRanges.length == 0 && ipv6Ranges.length == 0 && fromPort == 0 && toPort == 0)
          ipPermissionsEgress.all(ipRanges.length == 0 && ipv6Ranges.length == 0 && fromPort == 0 && toPort == 0)
        }
      title: Ensure the default security group of every VPC restricts all traffic
  aws.ec2.eip:
    fields:
      attached: {}
      instance: {}
      networkInterfaceId: {}
      networkInterfaceOwnerId: {}
      privateIpAddress: {}
      publicIp: {}
      publicIpv4Pool: {}
      region: {}
      tags: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - aws
  aws.ec2.image:
    docs:
      desc: |
        The `aws.ec2.image` resource provides fields for assessing AMIs within an account. For usage, see `aws.ec2` resource documentation.
    fields:
      architecture: {}
      arn: {}
      createdAt:
        min_mondoo_version: 9.0.0
      deprecatedAt:
        min_mondoo_version: 9.0.0
      enaSupport:
        min_mondoo_version: 9.0.0
      id: {}
      name: {}
      ownerAlias:
        min_mondoo_version: 5.22.0
      ownerId: {}
      tpmSupport:
        min_mondoo_version: 9.0.0
    is_private: true
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
  aws.ec2.instance:
    docs:
      desc: |
        The `aws.ec2.instance` resource provides fields for assessing the configuration of EC2 instances within an account. For usage, see `aws.ec2` resource documentation.
    fields:
      architecture:
        min_mondoo_version: 9.0.0
      arn: {}
      detailedMonitoring: {}
      deviceMappings: {}
      ebsOptimized: {}
      enaSupported:
        min_mondoo_version: 9.0.0
      httpEndpoint:
        min_mondoo_version: 9.0.0
      httpTokens: {}
      hypervisor:
        min_mondoo_version: 9.0.0
      iamInstanceProfile: {}
      image: {}
      instanceId: {}
      instanceLifecycle:
        min_mondoo_version: 9.0.0
      instanceStatus: {}
      instanceType: {}
      keypair: {}
      launchTime: {}
      networkInterfaces:
        min_mondoo_version: 9.0.0
      patchState: {}
      platformDetails:
        min_mondoo_version: 8.11.0
      privateDnsName:
        min_mondoo_version: 5.37.0
      privateIp:
        min_mondoo_version: 5.37.0
      publicDnsName: {}
      publicIp: {}
      region: {}
      rootDeviceName:
        min_mondoo_version: 9.0.0
      rootDeviceType:
        min_mondoo_version: 9.0.0
      securityGroups: {}
      ssm: {}
      state: {}
      stateReason: {}
      stateTransitionReason: {}
      stateTransitionTime:
        min_mondoo_version: 9.0.0
      tags: {}
      tpmSupport:
        min_mondoo_version: 9.0.0
      vpc: {}
      vpcArn:
        min_mondoo_version: 9.0.0
    is_private: true
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
  aws.ec2.instance.device:
    docs:
      desc: |
        The `aws.ec2.instance.device` resource provides fields for assessing the configuration of devices attached to EC2 instances within an account. For usage, see `aws.ec2` resource documentation.
    fields:
      deleteOnTermination: {}
      deviceName: {}
      status: {}
      volumeId: {}
    is_private: true
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
  aws.ec2.internetgateway:
    docs:
      desc: |
        The `aws.ec2.internetgateway` resource provides fields for assessing the configuration of internet gateways within an AWS account. For usage, see `aws.ec2` resource documentation.
    fields:
      arn: {}
      attachments: {}
      id: {}
    is_private: true
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
  aws.ec2.keypair:
    fields:
      arn: {}
      createdAt: {}
      fingerprint: {}
      name: {}
      region: {}
      tags: {}
      type: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - aws
  aws.ec2.networkacl:
    docs:
      desc: |
        The `aws.ec2.networkacl` resource provides fields for assessing the configuration of VPC network ACLs within an AWS account. For usage, see `aws.ec2` resource documentation.
    fields:
      arn: {}
      associations:
        min_mondoo_version: 9.0.0
      entries: {}
      id: {}
      isDefault:
        min_mondoo_version: 9.0.0
      region: {}
      tags:
        min_mondoo_version: 9.0.0
    is_private: true
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
  aws.ec2.networkacl.association:
    fields:
      associationId: {}
      networkAclId: {}
      subnetId: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - aws
  aws.ec2.networkacl.entry:
    docs:
      desc: |
        The `aws.ec2.networkacl.entry` resource provides fields for assessing the configuration of network ACL entries within an AWS account. For usage, see `aws.ec2` resource documentation.
    fields:
      cidrBlock:
        min_mondoo_version: 9.0.0
      egress: {}
      id: {}
      ipv6CidrBlock:
        min_mondoo_version: latest
      portRange: {}
      ruleAction: {}
      ruleNumber:
        min_mondoo_version: 9.0.0
    is_private: true
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
  aws.ec2.networkacl.entry.portrange:
    docs:
      desc: |
        The `aws.ec2.networkacl.portrange` resource provides fields for assessing the port range configuration of network ACL entries within an AWS account. For usage, see `aws.ec2` resource documentation.
    fields:
      from: {}
      id: {}
      to: {}
    is_private: true
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
  aws.ec2.networkinterface:
    fields:
      availabilityZone: {}
      description: {}
      id: {}
      ipv6Native: {}
      macAddress: {}
      privateDnsName: {}
      privateIpAddress: {}
      requesterManaged: {}
      securityGroups: {}
      sourceDestCheck: {}
      status: {}
      subnet: {}
      tags: {}
      vpc: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - aws
  aws.ec2.securitygroup:
    docs:
      desc: |
        The `aws.ec2.securitygroup` resource provides fields for assessing the configuration of security groups within an AWS account. For usage, see `aws.ec2` resource documentation.
    fields:
      arn: {}
      description: {}
      id: {}
      ipPermissions: {}
      ipPermissionsEgress: {}
      isAttachedToNetworkInterface: {}
      name: {}
      region: {}
      tags: {}
      vpc: {}
    is_private: true
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
  aws.ec2.securitygroup.ippermission:
    docs:
      desc: |
        The `aws.ec2.securitygroup.ippermission` resource provides fields for assessing the configuration of ip permissions for security groups within an AWS account. For usage, see `aws.ec2` resource documentation.
    fields:
      fromPort: {}
      id: {}
      ipProtocol: {}
      ipRanges: {}
      ipv6Ranges: {}
      prefixListIds:
        min_mondoo_version: 9.0.0
      toPort: {}
      userIdGroupPairs:
        min_mondoo_version: 9.0.0
    is_private: true
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
  aws.ec2.snapshot:
    docs:
      desc: |
        The `aws.ec2.snapshot` resource provides fields for assessing the configuration of EBS snapshots within an AWS account. For usage, see `aws.ec2` resource documentation.
    fields:
      arn: {}
      completionTime:
        min_mondoo_version: 9.0.0
      createVolumePermission: {}
      description:
        min_mondoo_version: 9.0.0
      encrypted:
        min_mondoo_version: 9.0.0
      id: {}
      region: {}
      startTime: {}
      state: {}
      storageTier:
        min_mondoo_version: 9.0.0
      tags: {}
      volumeId: {}
      volumeSize:
        min_mondoo_version: 9.0.0
    is_private: true
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
  aws.ec2.vgwtelemetry:
    docs:
      desc: |
        The `aws.ec2.vgwtelemetry` resource provides fields for assessing the configuration of telemetry for VPN tunnels within an AWS account. For usage, see `aws.ec2` resource documentation.
    fields:
      outsideIpAddress: {}
      status: {}
      statusMessage: {}
    is_private: true
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
  aws.ec2.volume:
    docs:
      desc: |
        The `aws.ec2.volume` resource provides fields for assessing the configuration of EBS volumes within an AWS account. For usage, see `aws.ec2` resource documentation.
    fields:
      arn: {}
      attachments: {}
      availabilityZone: {}
      createTime:
        min_mondoo_version: 5.25.0
      encrypted: {}
      id: {}
      iops:
        min_mondoo_version: 9.0.0
      multiAttachEnabled:
        min_mondoo_version: 9.0.0
      region: {}
      size:
        min_mondoo_version: 9.0.0
      state: {}
      tags: {}
      throughput:
        min_mondoo_version: 9.0.0
      volumeType: {}
    is_private: true
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
  aws.ec2.vpnconnection:
    docs:
      desc: |
        The `aws.ec2.vpnconnection` resource provides fields for assessing the configuration of VPN connections within an AWS account. For usage, see `aws.ec2` resource documentation.
    fields:
      arn: {}
      vgwTelemetry: {}
    is_private: true
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
  aws.ecr:
    docs:
      desc: |
        The `aws.ecr` resource to assess the configuration of an Amazon Elastic Container Registry.
    fields:
      images: {}
      privateRepositories: {}
      publicRepositories: {}
    min_mondoo_version: 7.15.0
    platform:
      name:
      - aws
    refs:
    - title: Security in Amazon Elastic Container Registry
      url: https://docs.aws.amazon.com/AmazonECR/latest/userguide/security.html
    snippets:
    - query: |
        aws.ecr {*}
      title: Return a `aws.ecr` resource representing the Elastic Container Registry.
  aws.ecr.image:
    docs:
      desc: |
        Use the `aws.ecr.image` resource to assess a container image stored in an Amazon Elastic Container Registry.
    fields:
      arn: {}
      digest: {}
      lastRecordedPullTime:
        min_mondoo_version: 9.0.0
      mediaType: {}
      pushedAt:
        min_mondoo_version: 9.0.0
      region: {}
      registryId: {}
      repoName: {}
      sizeInBytes:
        min_mondoo_version: 9.0.0
      tags: {}
      uri: {}
    is_private: true
    min_mondoo_version: 7.15.0
    platform:
      name:
      - aws
    snippets:
    - query: "aws.ecr {\n  publicRepositories { \n    images {\n      digest\n      repoName\n
        \     tags\n    }\n  }\n}\n"
      title: Return a list of `aws.ecr.image` resources representing the images stored
        in public repositories.
  aws.ecr.repository:
    docs:
      desc: |
        Use the `aws.ecr.repository` resource to assess the Amazon Elastic Container Registry repositories.
    fields:
      arn: {}
      imageScanOnPush:
        min_mondoo_version: 8.19.0
      images: {}
      name: {}
      public: {}
      region: {}
      registryId: {}
      uri: {}
    is_private: true
    min_mondoo_version: 7.15.0
    platform:
      name:
      - aws
    snippets:
    - query: "aws.ecr {\n  publicRepositories { \n    name\n    uri\n  }\n}\n"
      title: Return a list of `aws.ecr.repository` resources representing the public
        repositories.
  aws.ecs:
    fields:
      clusters: {}
      containerInstances: {}
      containers: {}
    min_mondoo_version: latest
    platform:
      name:
      - aws
  aws.ecs.cluster:
    fields:
      arn: {}
      configuration: {}
      containerInstances: {}
      name: {}
      pendingTasksCount: {}
      region: {}
      registeredContainerInstancesCount: {}
      runningTasksCount: {}
      status: {}
      tags: {}
      tasks: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - aws
  aws.ecs.container:
    fields:
      arn: {}
      clusterName: {}
      command: {}
      containerName: {}
      image: {}
      logDriver: {}
      name: {}
      platformFamily: {}
      platformVersion: {}
      publicIp: {}
      region: {}
      runtimeId: {}
      status: {}
      taskArn: {}
      taskDefinitionArn: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - aws
  aws.ecs.instance:
    fields:
      agentConnected: {}
      arn: {}
      capacityProvider: {}
      ec2Instance: {}
      id: {}
      region: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - aws
  aws.ecs.task:
    fields:
      arn: {}
      clusterName: {}
      connectivity: {}
      containers: {}
      lastStatus: {}
      platformFamily: {}
      platformVersion: {}
      tags: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - aws
  aws.efs:
    docs:
      desc: |
        Use the `aws.efs` resource to assess the configuration of Amazon Elastic File System deployments.
    fields:
      filesystems: {}
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
    refs:
    - title: Security in Amazon EFS
      url: https://docs.aws.amazon.com/efs/latest/ug/security-considerations.html
    snippets:
    - query: |
        aws.efs.filesystems {
          name
          id
          arn
          encrypted
          kmsKey
          backupPolicy
          region
        }
      title: Return a list of `aws.efs.filesystem` resources representing any EFS
        deployments across all enabled regions in the account
    - query: "aws.efs.filesystems.all(\n  encrypted == true && kmsKey.arn == /^arn:aws:kms:.*/
        \n)\n"
      title: Check whether all EFS systems are configured to encrypt file data using
        KMS
  aws.efs.filesystem:
    docs:
      desc: "The `aws.efs.filesystem` resource provides fields for assessing the configuration
        of individual EFS deployments. For usage, read the `aws.efs` resource documentation.
        \n"
    fields:
      arn: {}
      availabilityZone:
        min_mondoo_version: 9.0.0
      backupPolicy: {}
      createdAt:
        min_mondoo_version: 9.0.0
      encrypted: {}
      id: {}
      kmsKey: {}
      name: {}
      region: {}
      tags: {}
    is_private: true
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
  aws.eks:
    docs:
      desc: |
        Use the `aws.eks` resource to assess the configuration of Amazon Elastic Kubernetes Service (EKS).
    fields:
      clusters: {}
    min_mondoo_version: 5.31.0
    platform:
      name:
      - aws
    refs:
    - title: Amazon Elastic Kubernetes Service
      url: https://aws.amazon.com/eks/
  aws.eks.addon:
    fields:
      addonVersion: {}
      arn: {}
      configurationValues: {}
      createdAt: {}
      modifiedAt: {}
      name: {}
      owner: {}
      publisher: {}
      status: {}
      tags: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - aws
  aws.eks.cluster:
    fields:
      addons:
        min_mondoo_version: 9.0.0
      arn: {}
      authenticationMode:
        min_mondoo_version: 9.0.0
      createdAt: {}
      encryptionConfig: {}
      endpoint: {}
      iamRole:
        min_mondoo_version: 9.0.0
      logging: {}
      name: {}
      networkConfig: {}
      nodeGroups:
        min_mondoo_version: 9.0.0
      platformVersion: {}
      region: {}
      resourcesVpcConfig: {}
      status: {}
      supportType:
        min_mondoo_version: 9.0.0
      tags: {}
      version: {}
    min_mondoo_version: 5.31.0
    platform:
      name:
      - aws
  aws.eks.nodegroup:
    fields:
      amiType: {}
      arn: {}
      autoscalingGroups: {}
      capacityType: {}
      createdAt: {}
      diskSize: {}
      instanceTypes: {}
      labels: {}
      modifiedAt: {}
      name: {}
      nodeRole: {}
      region: {}
      scalingConfig: {}
      status: {}
      tags: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - aws
  aws.elasticache:
    docs:
      desc: |
        Use the `aws.elasticache` resource to assess the configuration of Amazon ElastiCache.
    fields:
      cacheClusters: {}
      clusters: {}
      serverlessCaches:
        min_mondoo_version: 9.0.0
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
    refs:
    - title: Security in Amazon ElastiCache
      url: https://docs.aws.amazon.com/AmazonElastiCache/latest/red-ug/redis-security.html
    snippets:
    - query: |
        aws.elasticache.clusters.all(
          _['SnapshotRetentionLimit'] > _['SnapshotRetentionPeriod']
        )
      title: Check if the ElastiCache Redis clusters have automatic backup turned
        on
  aws.elasticache.cluster:
    fields:
      arn: {}
      atRestEncryptionEnabled: {}
      authTokenEnabled: {}
      authTokenLastModifiedDate: {}
      autoMinorVersionUpgrade:
        min_mondoo_version: 8.22.0
      cacheClusterCreateTime: {}
      cacheClusterId: {}
      cacheClusterStatus: {}
      cacheNodeType: {}
      cacheNodes: {}
      cacheSecurityGroups: {}
      cacheSubnetGroupName: {}
      clientDownloadLandingPage: {}
      engine: {}
      engineVersion: {}
      ipDiscovery: {}
      logDeliveryConfigurations: {}
      networkType: {}
      nodeType: {}
      notificationConfiguration: {}
      numCacheNodes: {}
      preferredAvailabilityZone: {}
      region: {}
      securityGroups: {}
      snapshotRetentionLimit: {}
      transitEncryptionEnabled: {}
      transitEncryptionMode: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - aws
  aws.elasticache.serverlessCache:
    fields:
      arn: {}
      createdAt: {}
      dailySnapshotTime: {}
      description: {}
      engine: {}
      engineVersion: {}
      kmsKeyId: {}
      majorEngineVersion: {}
      name: {}
      region: {}
      securityGroups: {}
      snapshotRetentionLimit: {}
      status: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - aws
  aws.elb:
    docs:
      desc: |
        Use the `aws.elb` resource to assess the configuration of Amazon Elastic Load Balancers within an AWS account.
    fields:
      classicLoadBalancers: {}
      loadBalancers: {}
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
    refs:
    - title: Security in Elastic Load Balancing
      url: https://docs.aws.amazon.com/elasticloadbalancing/latest/userguide/security.html
    snippets:
    - query: |
        aws.elb.loadbalancers {
          arn
          dnsName
          listenerDescriptions
          name
          scheme
          attributes
        }
      title: Return a list of all application, gateway, and network Elastic Load Balancers
        deployed across every enabled region and the values for specified fields
    - query: |
        aws.elb.classicLoadBalancers {
          arn
          dnsName
          listenerDescriptions
          name
          scheme
          attributes
        }
      title: Return a list of all classic Elastic Load Balancers deployed across every
        enabled region and the values for specified fields
    - query: "aws.elb.loadBalancers.all( listenerDescriptions.any ( \n  _['Protocol']
        == \"HTTPS\" || _['Protocol'] == \"SSL\" ) \n)\n"
      title: Check whether HTTP to HTTPS redirection is configured on all application
        load balancer http listeners
    - query: "aws.elb.classicLoadBalancers.all( listenerDescriptions.any ( \n  _['Listener']['Protocol']
        == \"HTTPS\" || _['Listener']['Protocol'] == \"SSL\" ) \n)\n"
      title: Check that all Classic Load Balancers use SSL certificates provided by
        AWS Cert Mgr
  aws.elb.loadbalancer:
    docs:
      desc: |
        The `aws.elb.loadbalancer` resource provides fields for assessing the configuration of individual classic, application, gateway, and network Amazon Elastic Load Balancers. For usage, read the `aws.elb` documentation.
    fields:
      arn: {}
      attributes: {}
      availabilityZones:
        min_mondoo_version: 9.0.0
      createdAt:
        min_mondoo_version: 9.0.0
      createdTime:
        min_mondoo_version: 9.0.0
      dnsName: {}
      elbType:
        min_mondoo_version: 9.0.0
      hostedZoneId:
        min_mondoo_version: 9.0.0
      listenerDescriptions: {}
      name: {}
      region:
        min_mondoo_version: 9.0.0
      scheme: {}
      securityGroups:
        min_mondoo_version: 9.0.0
      targetGroups:
        min_mondoo_version: 9.0.0
      vpc:
        min_mondoo_version: 9.0.0
      vpcId:
        min_mondoo_version: 9.0.0
    is_private: true
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
  aws.elb.targetgroup:
    fields:
      arn: {}
      attributes: {}
      ec2Targets: {}
      healthCheckEnabled: {}
      healthCheckIntervalSeconds: {}
      healthCheckPath: {}
      healthCheckPort: {}
      healthCheckProtocol: {}
      healthCheckTimeoutSeconds: {}
      ipAddressType: {}
      ipTargets: {}
      lambdaTargets: {}
      name: {}
      port: {}
      protocol: {}
      protocolVersion: {}
      targetType: {}
      unhealthyThresholdCount: {}
      vpc: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - aws
  aws.emr:
    docs:
      desc: |
        Use the `aws.emr` resource to assess the configuration of Amazon EMR clusters. This resource provides a list of `aws.emr.cluster` resources representing EMR clusters deployed across all enabled regions.
    fields:
      clusters: {}
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
    refs:
    - title: Security in Amazon EMR
      url: https://docs.aws.amazon.com/emr/latest/ManagementGuide/emr-security.html
    snippets:
    - query: |
        aws.emr.clusters {
          arn
          name
          normalizedInstanceHours
          outpostArn
          status
          masterInstances
          id
        }
      title: Return a list of EMR clusters deployed across all enabled regions and
        the value for specified fields
    - query: |
        aws.emr.clusters.all(
          masterInstances { _['PublicIpAddress'] == null }
        )
      title: Check whether EMR cluster master nodes have public ips
  aws.emr.cluster:
    docs:
      desc: |
        The `aws.emr.cluster` resource provides fields for assessing the configuration of individual Amazon EMR clusters. For usage, read the `aws.emr` resource documentation.
    fields:
      arn: {}
      id: {}
      masterInstances: {}
      name: {}
      normalizedInstanceHours: {}
      outpostArn: {}
      status: {}
      tags: {}
    is_private: true
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
  aws.es:
    docs:
      desc: |
        Use the `aws.es` resource to assess the configuration of Amazon Elasticsearch domains. This resource provides a list of `aws.es.domain` resources representing Elasticsearch domains deployed across all enabled regions.
    fields:
      domains: {}
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
    refs:
    - title: Amazon Security Blog on Elasticsearch
      url: https://aws.amazon.com/blogs/security/tag/amazon-elasticsearch-service/
  aws.es.domain:
    docs:
      desc: |
        The `aws.es.domain` resource provides fields for assessing the configuration of individual Amazon Elasticsearch domains. For usage, read the `aws.es` resource documentation
    fields:
      arn: {}
      domainId:
        min_mondoo_version: 9.0.0
      domainName:
        min_mondoo_version: 9.0.0
      elasticsearchVersion:
        min_mondoo_version: 9.0.0
      encryptionAtRestEnabled: {}
      endpoint: {}
      name: {}
      nodeToNodeEncryptionEnabled: {}
      region: {}
      tags: {}
    is_private: true
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
  aws.guardduty:
    docs:
      desc: |
        Use the `aws.guardduty` resource to assess the configuration of the AWS GuardDuty service. The resource provides a list of `aws.guardduty.detector` resources representing GuardDuty Detectors deployed across all enabled regions.
    fields:
      detectors: {}
      findings:
        min_mondoo_version: 9.0.0
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
    refs:
    - title: Amazon GuardDuty User Guide
      url: https://docs.aws.amazon.com/guardduty/latest/ug/what-is-guardduty.html
    - title: Security in Amazon GuardDuty
      url: https://docs.aws.amazon.com/guardduty/latest/ug/security.html
    snippets:
    - query: |
        aws.guardduty.detectors {
          id
          region
          status
          findingPublishingFrequency
          unarchivedFindings
        }
      title: Return a list of Amazon GuardDuty Detectors along with the values for
        specified fields
    - query: |
        aws.guardduty.detectors.all( status == "ENABLED" )
      title: Check that guardduty is enabled in all regions
  aws.guardduty.detector:
    docs:
      desc: |
        The `aws.guardduty.detector` resource provides fields for assessing the configuration of individual Amazon GuardDuty Detectors. For usage, read the `aws.guardduty` resource documentation
    fields:
      features:
        min_mondoo_version: 9.0.0
      findingPublishingFrequency: {}
      findings:
        min_mondoo_version: 9.0.0
      id: {}
      region: {}
      status: {}
      tags:
        min_mondoo_version: 9.0.0
      unarchivedFindings: {}
    is_private: true
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
  aws.guardduty.finding:
    fields:
      arn: {}
      confidence: {}
      createdAt: {}
      description: {}
      id: {}
      region: {}
      severity: {}
      title: {}
      type: {}
      updatedAt: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - aws
  aws.iam:
    docs:
      desc: |
        Use the `aws.iam` resource to assess the configuration of the AWS IAM service. The resource provides a list of `aws.iam.user` resources representing GuardDuty Detectors deployed across all enabled regions.
    fields:
      accountPasswordPolicy: {}
      accountSummary: {}
      attachedPolicies: {}
      credentialReport: {}
      groups: {}
      instanceProfiles:
        min_mondoo_version: 9.0.0
      policies: {}
      roles: {}
      serverCertificates: {}
      users: {}
      virtualMfaDevices: {}
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
    refs:
    - title: AWS Identity and Access Management User Guide
      url: https://docs.aws.amazon.com/IAM/latest/UserGuide/introduction.html
    - title: Security in IAM and AWS STS
      url: https://docs.aws.amazon.com/IAM/latest/UserGuide/security.html
    snippets:
    - query: |
        aws.iam.users {
          users
          roles
          groups
          policies
          attachedPolicies
          accountSummary
          virtualMfaDevices
          serverCertificates
        }
      title: Return a list of `aws.iam.user` resources representing IAM users in the
        account and specified fields
    - query: "aws.iam.credentialReport.where(mfaActive == false) { \n  user { \n    arn
        \n    name \n    groups \n  }\n}\n"
      title: Return a list of users that do not have MFA configured along with the
        ARN, name, and associated IAM Groups
    - query: "aws.iam.credentialReport.\n  where(\n    passwordEnabled &&\n    accessKey1Active
        &&\n    userCreationTime < time.today\n  ).\n  all(\n    accessKey1LastUsedDate
        != null\n  ) \n"
      title: Do not setup access keys during initial user setup for all IAM users
        that have a console password
  aws.iam.accessAnalyzer:
    fields:
      analyzers: {}
      findings: {}
    min_mondoo_version: 9.0.0
    platform:
      name:
      - aws
    refs:
    - title: Using AWS IAM Access Analyzer
      url: https://docs.aws.amazon.com/IAM/latest/UserGuide/what-is-access-analyzer.html
    snippets:
    - query: aws.iam.accessAnalyzer.analyzers
      title: Return a list of AWS IAM Access Analyzers configured across the AWS account
    - query: "aws.iam.accessAnalyzer.analyzers { \n  arn \n  name \n  status \n  type
        \n}\n"
      title: Return a list of `aws.iam.accessAnalyzer.analyzer` resources and the
        value for specified fields
    - query: |
        aws.iam.accessAnalyzer.analyzers.all(
          status == "ACTIVE"
        )
      title: Ensure that IAM Access analyzer is enabled for all regions
  aws.iam.accessanalyzer.analyzer:
    fields:
      arn: {}
      createdAt: {}
      lastResourceAnalyzed: {}
      lastResourceAnalyzedAt: {}
      name: {}
      region: {}
      status: {}
      tags: {}
      type: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - aws
  aws.iam.accessanalyzer.finding:
    fields:
      analyzedAt: {}
      analyzerArn: {}
      createdAt: {}
      error: {}
      id: {}
      region: {}
      resourceArn: {}
      resourceOwnerAccount: {}
      resourceType: {}
      status: {}
      type: {}
      updatedAt: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - aws
  aws.iam.group:
    docs:
      desc: |
        The `aws.iam.group` resource provides fields for assessing the configuration of IAM Groups. For usage, read the `aws.iam` resource documentation.
    fields:
      arn: {}
      createDate: {}
      createdAt:
        min_mondoo_version: 9.0.0
      id: {}
      name: {}
      usernames: {}
    is_private: true
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
  aws.iam.instanceProfile:
    fields:
      arn: {}
      createDate: {}
      createdAt: {}
      iamRoles: {}
      instanceProfileId: {}
      instanceProfileName: {}
      tags: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - aws
  aws.iam.loginProfile:
    fields:
      createdAt: {}
    is_private: true
    min_mondoo_version: "10"
    platform:
      name:
      - aws
  aws.iam.policy:
    docs:
      desc: |
        The `aws.iam.policy` resource provides fields for assessing the configuration of individual IAM Policies. For usage, read the `aws.iam` resource documentation.
    fields:
      arn: {}
      attachedGroups: {}
      attachedRoles: {}
      attachedUsers: {}
      attachmentCount: {}
      createDate: {}
      createdAt:
        min_mondoo_version: 9.0.0
      defaultVersion: {}
      description: {}
      id: {}
      isAttachable: {}
      name: {}
      policyId:
        min_mondoo_version: 9.0.0
      scope: {}
      updateDate: {}
      versions: {}
    is_private: true
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
  aws.iam.policyversion:
    docs:
      desc: |
        The `aws.iam.policyversion` resource provides fields for assessing the metadata for IAM Policy versions. For usage, read the `aws.iam` resource documentation.
    fields:
      arn: {}
      createDate: {}
      createdAt:
        min_mondoo_version: 9.0.0
      document: {}
      isDefaultVersion: {}
      versionId: {}
    is_private: true
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
  aws.iam.role:
    docs:
      desc: |
        The `aws.iam.role` resource provides fields for assessing the configuration of individual IAM Roles. For usage, read the `aws.iam` resource documentation.
    fields:
      arn: {}
      assumeRolePolicyDocument: {}
      createDate: {}
      createdAt:
        min_mondoo_version: 9.0.0
      description: {}
      id: {}
      name: {}
      tags: {}
    is_private: true
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
  aws.iam.user:
    docs:
      desc: |
        The `aws.iam.user` resource provides fields for assessing the configuration of individual IAM users. For usage, read the `aws.iam` resource documentation.
    fields:
      accessKeys: {}
      arn: {}
      attachedPolicies: {}
      createDate: {}
      createdAt:
        min_mondoo_version: 9.0.0
      groups: {}
      id: {}
      loginProfile:
        min_mondoo_version: "10"
      name: {}
      passwordLastUsed: {}
      policies: {}
      tags: {}
    is_private: true
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
  aws.iam.usercredentialreportentry:
    docs:
      desc: |
        The `aws.iam.credentialreportentry` resource provides fields for assessing the metadata for individual IAM Credential Report entries. For usage, read the `aws.iam` resource documentation.
    fields:
      accessKey1Active: {}
      accessKey1LastRotated: {}
      accessKey1LastUsedDate: {}
      accessKey1LastUsedRegion: {}
      accessKey1LastUsedService: {}
      accessKey2Active: {}
      accessKey2LastRotated: {}
      accessKey2LastUsedDate: {}
      accessKey2LastUsedRegion: {}
      accessKey2LastUsedService: {}
      arn: {}
      cert1Active: {}
      cert1LastRotated: {}
      cert2Active: {}
      cert2LastRotated: {}
      createdAt:
        min_mondoo_version: 9.0.0
      mfaActive: {}
      passwordEnabled: {}
      passwordLastChanged: {}
      passwordLastUsed: {}
      passwordNextRotation: {}
      properties: {}
      user: {}
      userCreationTime: {}
    is_private: true
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
  aws.iam.virtualmfadevice:
    docs:
      desc: |
        The `aws.iam.virtualmfadevice` resource provides fields for assessing the metadata for individual virtual MFA devices associated with IAM users. For usage, read the `aws.iam` resource documentation.
    fields:
      enableDate: {}
      serialNumber: {}
      user: {}
    is_private: true
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
  aws.inspector:
    docs:
      desc: |
        The `aws.inspector` resource provides fields for assessing the configuration of Amazon Inspector.
    fields:
      coverages: {}
    min_mondoo_version: 9.0.0
    platform:
      name:
      - aws
    refs:
    - title: Amazon Inspector
      url: https://docs.aws.amazon.com/inspector/
    - title: Amazon Inspector Getting Started Tutorial
      url: https://docs.aws.amazon.com/inspector/latest/user/getting_started_tutorial.html
  aws.inspector.coverage:
    fields:
      accountId: {}
      ec2Instance: {}
      ecrImage: {}
      ecrRepo: {}
      lambda: {}
      lastScannedAt: {}
      region: {}
      resourceId: {}
      resourceType: {}
      scanType: {}
      statusCode: {}
      statusReason: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - aws
  aws.inspector.coverage.image:
    fields:
      imagePulledAt: {}
      region: {}
      tags: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - aws
  aws.inspector.coverage.instance:
    fields:
      image: {}
      platform: {}
      region: {}
      tags: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - aws
  aws.inspector.coverage.repository:
    fields:
      name: {}
      region: {}
      scanFrequency: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - aws
  aws.kms:
    docs:
      desc: "Use the `aws.kms` resource to assess the configuration of AWS KMS keys.
        \n"
    fields:
      keys: {}
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
    snippets:
    - query: |
        aws.kms.keys.where( metadata['KeyState'] == "Enabled" ).all( keyRotationEnabled == true )
      title: Ensure rotation for customer created CMKs is enabled
  aws.kms.key:
    docs:
      desc: |
        The `aws.kms.key` resource provides fields for assessing the configuration of individual KMS keys. For usage, read the `aws.kms` resource documentation.
    fields:
      arn: {}
      id: {}
      keyRotationEnabled: {}
      metadata: {}
      region: {}
    is_private: true
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
  aws.lambda:
    docs:
      desc: "Use the `aws.lambda` resource to assess the configuration of AWS Lambda.
        \n"
    fields:
      functions: {}
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
  aws.lambda.function:
    docs:
      desc: |
        The `aws.lambda.function` resource provides fields for assessing the configuration of individual AWS Lambda functions. For usage, read the `aws.lambda` resource documentation.
    fields:
      arn: {}
      concurrency: {}
      dlqTargetArn: {}
      name: {}
      policy: {}
      region: {}
      runtime:
        min_mondoo_version: 8.28.0
      tags: {}
      vpcConfig: {}
    is_private: true
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
  aws.neptune:
    fields:
      clusters: {}
      instances: {}
    min_mondoo_version: 9.0.0
    platform:
      name:
      - aws
  aws.neptune.cluster:
    fields:
      arn: {}
      automaticRestartTime: {}
      availabilityZones: {}
      backupRetentionPeriod: {}
      clusterIdentifier: {}
      clusterParameterGroup: {}
      clusterResourceId: {}
      createdAt: {}
      crossAccountClone: {}
      deletionProtection: {}
      earliestRestorableTime: {}
      enabledCloudwatchLogsExports: {}
      endpoint: {}
      engine: {}
      engineVersion: {}
      globalClusterIdentifier: {}
      iamDatabaseAuthenticationEnabled: {}
      kmsKeyId: {}
      latestRestorableTime: {}
      masterUsername: {}
      multiAZ: {}
      name: {}
      port: {}
      preferredBackupWindow: {}
      preferredMaintenanceWindow: {}
      region: {}
      status: {}
      storageEncrypted: {}
      storageType: {}
      subnetGroup: {}
      tags: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - aws
  aws.neptune.instance:
    fields:
      arn: {}
      autoMinorVersionUpgrade: {}
      availabilityZone: {}
      backupRetentionPeriod: {}
      clusterIdentifier: {}
      createdAt: {}
      deletionProtection: {}
      enabledCloudwatchLogsExports: {}
      endpoint: {}
      engine: {}
      engineVersion: {}
      enhancedMonitoringResourceArn: {}
      iamDatabaseAuthenticationEnabled: {}
      instanceClass: {}
      kmsKeyId: {}
      latestRestorableTime: {}
      masterUsername: {}
      monitoringInterval: {}
      monitoringRoleArn: {}
      multiAZ: {}
      name: {}
      port: {}
      preferredBackupWindow: {}
      preferredMaintenanceWindow: {}
      promotionTier: {}
      region: {}
      status: {}
      storageEncrypted: {}
      storageType: {}
      tdeCredentialArn: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - aws
  aws.organization:
    fields:
      accounts:
        min_mondoo_version: 9.0.0
      arn: {}
      featureSet: {}
      masterAccountEmail: {}
      masterAccountId: {}
    min_mondoo_version: 6.11.0
    platform:
      name:
      - aws
  aws.rds:
    docs:
      desc: |
        Use the `aws.rds` resource to assess the configuration of AWS RDS deployments. The resource returns lists of `aws.rds.dbcluster`, `aws.rds.dbinstance`, and `aws.rds.snapshot` resources, each with fields for assessing the configuration of those assets.
    fields:
      allPendingMaintenanceActions:
        min_mondoo_version: 9.0.0
      clusterParameterGroups:
        min_mondoo_version: 9.0.0
      clusters:
        min_mondoo_version: 9.0.0
      dbClusters: {}
      dbInstances: {}
      instances:
        min_mondoo_version: 9.0.0
      parameterGroups:
        min_mondoo_version: 9.0.0
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
    refs:
    - title: Security in Amazon RDS
      url: https://docs.aws.amazon.com/AmazonRDS/latest/UserGuide/UsingWithRDS.html
    snippets:
    - query: aws.rds.dbInstances.all(snapshots.length > 0)
      title: Check whether RDS DB instances have backups enabled
    - query: aws.rds.dbInstances.all(multiAZ == true)
      title: Check whether high availability is enabled for all rds instances
    - query: aws.rds.dbClusters { snapshots.where( encrypted == false) } { arn region
        id }
      title: Return a list of RDS Clusters across all regions where snapshots are
        not encrypted and return the `arn` `region` and `id` for the cluster
  aws.rds.backupsetting:
    fields:
      dedicatedLogVolume: {}
      earliestRestoreAvailable: {}
      encrypted: {}
      kmsKey: {}
      latestRestoreAvailable: {}
      region: {}
      retentionPeriod: {}
      status: {}
      target: {}
      timezone: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - aws
  aws.rds.clusterParameterGroup:
    fields:
      arn: {}
      description: {}
      family: {}
      name: {}
      parameters: {}
      region: {}
    min_mondoo_version: 9.0.0
    platform:
      name:
      - aws
  aws.rds.dbcluster:
    docs:
      desc: |
        The `aws.rds.dbcluster` resource provides fields for assessing the configuration of AWS RDS Clusters.
    fields:
      activityStreamMode:
        min_mondoo_version: latest
      activityStreamStatus:
        min_mondoo_version: latest
      arn: {}
      autoMinorVersionUpgrade:
        min_mondoo_version: 9.0.0
      availabilityZones:
        min_mondoo_version: 9.0.0
      backupRetentionPeriod:
        min_mondoo_version: 9.0.0
      backupSettings:
        min_mondoo_version: 9.0.0
      certificateAuthority:
        min_mondoo_version: latest
      certificateExpiresAt:
        min_mondoo_version: latest
      clusterDbInstanceClass:
        min_mondoo_version: 9.0.0
      createdAt:
        min_mondoo_version: 9.0.0
      createdTime:
        min_mondoo_version: 9.0.0
      databaseInsightsMode:
        min_mondoo_version: 9.0.0
      deletionProtection:
        min_mondoo_version: 9.0.0
      endpoint:
        min_mondoo_version: 9.0.0
      engine:
        min_mondoo_version: 9.0.0
      engineLifecycleSupport:
        min_mondoo_version: 9.0.0
      engineVersion:
        min_mondoo_version: 9.0.0
      globalClusterIdentifier:
        min_mondoo_version: 9.0.0
      hostedZoneId:
        min_mondoo_version: 9.0.0
      httpEndpointEnabled:
        min_mondoo_version: 9.0.0
      iamDatabaseAuthentication:
        min_mondoo_version: latest
      id: {}
      latestRestorableTime:
        min_mondoo_version: 9.0.0
      masterUsername:
        min_mondoo_version: 9.0.0
      members: {}
      monitoringInterval:
        min_mondoo_version: 9.0.0
      multiAZ:
        min_mondoo_version: 9.0.0
      networkType:
        min_mondoo_version: 9.0.0
      parameterGroupName:
        min_mondoo_version: 9.0.0
      port:
        min_mondoo_version: 9.0.0
      preferredBackupWindow:
        min_mondoo_version: 9.0.0
      preferredMaintenanceWindow:
        min_mondoo_version: 9.0.0
      publiclyAccessible:
        min_mondoo_version: 9.0.0
      region: {}
      securityGroups:
        min_mondoo_version: 9.0.0
      snapshots: {}
      status:
        min_mondoo_version: 9.0.0
      storageAllocated:
        min_mondoo_version: 9.0.0
      storageEncrypted:
        min_mondoo_version: 9.0.0
      storageIops:
        min_mondoo_version: 9.0.0
      storageType:
        min_mondoo_version: 9.0.0
      tags: {}
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
  aws.rds.dbinstance:
    docs:
      desc: |
        The `aws.rds.dbinstance` resource provides fields for assessing the configuration of RDS instances. For usage, read the `aws.rds` resource documentation.
    fields:
      activityStreamMode:
        min_mondoo_version: latest
      activityStreamStatus:
        min_mondoo_version: latest
      arn: {}
      autoMinorVersionUpgrade:
        min_mondoo_version: 8.22.0
      availabilityZone:
        min_mondoo_version: 9.0.0
      backupRetentionPeriod: {}
      backupSettings:
        min_mondoo_version: 9.0.0
      certificateAuthority:
        min_mondoo_version: latest
      certificateExpiresAt:
        min_mondoo_version: latest
      createdAt:
        min_mondoo_version: 9.0.0
      createdTime:
        min_mondoo_version: 9.0.0
      customIamInstanceProfile:
        min_mondoo_version: latest
      dbInstanceClass:
        min_mondoo_version: 5.19.1
      dbInstanceIdentifier:
        min_mondoo_version: 5.19.1
      deletionProtection: {}
      enabledCloudwatchLogsExports: {}
      endpoint:
        min_mondoo_version: 9.0.0
      engine:
        min_mondoo_version: 5.19.1
      engineLifecycleSupport:
        min_mondoo_version: 9.0.0
      engineVersion:
        min_mondoo_version: 9.0.0
      enhancedMonitoringResourceArn: {}
      iamDatabaseAuthentication:
        min_mondoo_version: latest
      id: {}
      latestRestorableTime:
        min_mondoo_version: 9.0.0
      masterUsername:
        min_mondoo_version: 9.0.0
      monitoringInterval:
        min_mondoo_version: 9.0.0
      multiAZ: {}
      name: {}
      networkType:
        min_mondoo_version: 9.0.0
      pendingMaintenanceActions:
        min_mondoo_version: 9.0.0
      port:
        min_mondoo_version: 9.0.0
      preferredBackupWindow:
        min_mondoo_version: 9.0.0
      preferredMaintenanceWindow:
        min_mondoo_version: 9.0.0
      publiclyAccessible: {}
      region: {}
      securityGroups:
        min_mondoo_version: 5.19.1
      snapshots: {}
      status:
        min_mondoo_version: 5.19.1
      storageAllocated:
        min_mondoo_version: 9.0.0
      storageEncrypted: {}
      storageIops:
        min_mondoo_version: 9.0.0
      storageType:
        min_mondoo_version: 9.0.0
      subnets:
        min_mondoo_version: 9.0.0
      tags: {}
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
  aws.rds.parameterGroup:
    fields:
      arn: {}
      description: {}
      family: {}
      id: {}
      name: {}
      parameters: {}
      region:
        min_mondoo_version: 9.0.0
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
  aws.rds.parameterGroup.parameter:
    fields:
      allowedValues: {}
      applyMethod: {}
      applyType: {}
      dataType: {}
      description: {}
      isModifiable: {}
      minimumEngineVersion: {}
      name: {}
      source: {}
      supportedEngineModes: {}
      value: {}
    min_mondoo_version: 9.0.0
    platform:
      name:
      - aws
  aws.rds.pendingMaintenanceAction:
    fields:
      action: {}
      autoAppliedAfterDate: {}
      currentApplyDate: {}
      description: {}
      forcedApplyDate: {}
      optInStatus: {}
      resourceArn: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - aws
  aws.rds.snapshot:
    docs:
      desc: |
        The `aws.rds.snapshot` resource provides fields for assessing the configuration of RDS snapshots. For usage, read the `aws.rds` resource documentation.
    fields:
      allocatedStorage:
        min_mondoo_version: 9.0.0
      arn: {}
      attributes: {}
      createdAt:
        min_mondoo_version: 9.0.0
      encrypted: {}
      engine:
        min_mondoo_version: 9.0.0
      engineVersion:
        min_mondoo_version: 9.0.0
      id: {}
      isClusterSnapshot: {}
      port:
        min_mondoo_version: 9.0.0
      region: {}
      status:
        min_mondoo_version: 9.0.0
      tags: {}
      type: {}
    is_private: true
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
  aws.redshift:
    docs:
      desc: |
        Use the `aws.redshift` resource to assess the configuration of Amazon Redshift deployments. The resource returns lists of `aws.rds.dbcluster`, `aws.rds.dbinstance`, and `aws.rds.snapshot` resources, each with fields for assessing the configuration of those assets.
    fields:
      clusters: {}
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
    refs:
    - title: Amazon Redshift security overview
      url: https://docs.aws.amazon.com/redshift/latest/dg/c_security-overview.html
    snippets:
    - query: |
        aws.redshift.clusters {
          arn
          encrypted
          nodeType
          allowVersionUpgrade
          preferredMaintenanceWindow
          automatedSnapshotRetentionPeriod
          publiclyAccessible
          parameters
          logging
          name
          region
          clusterParameterGroupNames
        }
      title: Return a list of Amazon Redshift clusters deployed across all enabled
        regions and the values for specified fields
    - query: |
        aws.redshift.clusters.where( encrypted == false ) {
          arn
          region
          name
        }
      title: Return a list of Amazon Redshift clusters that are not encrypted and
        the values for the `arn` `region` and `name` fields
  aws.redshift.cluster:
    fields:
      allowVersionUpgrade: {}
      arn: {}
      automatedSnapshotRetentionPeriod: {}
      availabilityZone:
        min_mondoo_version: 9.0.0
      clusterParameterGroupNames: {}
      clusterRevisionNumber:
        min_mondoo_version: 9.0.0
      clusterStatus:
        min_mondoo_version: 9.0.0
      clusterSubnetGroupName:
        min_mondoo_version: 9.0.0
      clusterVersion:
        min_mondoo_version: 9.0.0
      createdAt:
        min_mondoo_version: 9.0.0
      dbName:
        min_mondoo_version: 9.0.0
      encrypted: {}
      enhancedVpcRouting:
        min_mondoo_version: 9.0.0
      logging: {}
      masterUsername:
        min_mondoo_version: 9.0.0
      name: {}
      nextMaintenanceWindowStartTime:
        min_mondoo_version: 9.0.0
      nodeType: {}
      numberOfNodes:
        min_mondoo_version: 9.0.0
      parameters: {}
      preferredMaintenanceWindow: {}
      publiclyAccessible: {}
      region: {}
      tags: {}
      vpcId:
        min_mondoo_version: 9.0.0
    is_private: true
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
  aws.s3:
    docs:
      desc: |
        Amazon Simple Storage Service (Amazon S3) is an object storage service
    fields:
      buckets: {}
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
    refs:
    - title: Amazon S3 Product Page
      url: https://aws.amazon.com/s3/
    - title: 'AWS Documentation: What is Amazon S3?'
      url: https://docs.aws.amazon.com/AmazonS3/latest/userguide/Welcome.html
    snippets:
    - query: |
        aws.s3.buckets {
          arn
          name
          policy
          tags
          acl
          owner
          public
          cors
          location
          versioning
          logging
          staticWebsiteHosting
          defaultLock
          replication
          encryption
          publicAccessBlock
          exists
        }
      title: Return a list of AWS S3 buckets and the values for specified fields
    - query: "aws.s3.buckets.where( public == true ) {\n  arn\n  name\n  location\n
        \ tags\n  publicAccessBlock \n}\n"
      title: Return a list of AWS S3 buckets that are public and return the values
        for the `arn` `name` `location` `tags` and `publicAccessBlock`
    - query: "aws.s3.buckets.all( \n  replication['Rules'] { _['Status'] == \"Enabled\"
        }\n)\n"
      title: Check whether S3 buckets have cross-region replication enabled
    - query: "aws.s3.buckets.all( \n  encryption['Rules'] { _['ApplyServerSideEncryptionByDefault']['KMSMasterKeyID']
        == /^arn:aws:kms:.*/}\n)\n"
      title: Check that all buckets are encrypted with kms
  aws.s3.bucket:
    docs:
      desc: |
        The `aws.s3.bucket` resource provides fields for assessing the configuration of AWS S3 buckets. For usage, read the `aws.s3` resource documentation.
    fields:
      acl: {}
      arn: {}
      cors: {}
      createdAt:
        min_mondoo_version: 9.0.0
      createdTime:
        min_mondoo_version: 9.0.0
      defaultLock: {}
      encryption: {}
      exists: {}
      location: {}
      logging: {}
      name: {}
      owner: {}
      policy: {}
      public: {}
      publicAccessBlock: {}
      replication: {}
      staticWebsiteHosting: {}
      tags: {}
      versioning: {}
    is_private: true
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
    refs:
    - title: Amazon S3 Product Page
      url: https://aws.amazon.com/s3/
    - title: 'AWS Documentation: Buckets overview'
      url: https://docs.aws.amazon.com/AmazonS3/latest/userguide/UsingBucket.html
  aws.s3.bucket.corsrule:
    fields:
      allowedHeaders: {}
      allowedMethods: {}
      allowedOrigins: {}
      exposeHeaders: {}
      maxAgeSeconds: {}
      name: {}
    is_private: true
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
  aws.s3.bucket.grant:
    fields:
      grantee: {}
      id: {}
      name: {}
      permission: {}
    is_private: true
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
  aws.s3.bucket.policy:
    docs:
      desc: |
        Bucket policies grant permission to your Amazon S3 resources
    fields:
      bucketName:
        min_mondoo_version: 9.0.0
      document: {}
      exists:
        min_mondoo_version: 9.0.0
      id: {}
      name: {}
      statements: {}
      version: {}
    is_private: true
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
    refs:
    - title: 'AWS Documentation: Using bucket policies'
      url: https://docs.aws.amazon.com/AmazonS3/latest/userguide/bucket-policies.html
  aws.s3control:
    fields:
      accountPublicAccessBlock: {}
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
  aws.sagemaker:
    fields:
      endpoints: {}
      notebookInstances: {}
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
  aws.sagemaker.endpoint:
    fields:
      arn: {}
      config: {}
      name: {}
      region: {}
      tags: {}
    is_private: true
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
  aws.sagemaker.notebookinstance:
    fields:
      arn: {}
      details: {}
      name: {}
      region: {}
      tags: {}
    is_private: true
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
  aws.sagemaker.notebookinstancedetails:
    fields:
      arn: {}
      directInternetAccess: {}
      kmsKey: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - aws
  aws.secretsmanager:
    fields:
      secrets: {}
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
  aws.secretsmanager.secret:
    fields:
      arn: {}
      createdAt:
        min_mondoo_version: 9.0.0
      description:
        min_mondoo_version: 9.0.0
      lastChangedDate:
        min_mondoo_version: 9.0.0
      lastRotatedDate:
        min_mondoo_version: 9.0.0
      name: {}
      nextRotationDate:
        min_mondoo_version: 9.0.0
      primaryRegion:
        min_mondoo_version: 9.0.0
      rotationEnabled: {}
      tags: {}
    is_private: true
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
  aws.securityhub:
    fields:
      hubs: {}
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
  aws.securityhub.hub:
    fields:
      arn: {}
      subscribedAt: {}
    is_private: true
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
  aws.sns:
    fields:
      topics: {}
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
  aws.sns.subscription:
    fields:
      arn: {}
      protocol: {}
    is_private: true
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
  aws.sns.topic:
    fields:
      arn: {}
      attributes: {}
      region: {}
      subscriptions: {}
      tags:
        min_mondoo_version: 5.23.0
    is_private: true
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
  aws.sqs:
    fields:
      queues: {}
    min_mondoo_version: 9.0.0
    platform:
      name:
      - aws
  aws.sqs.queue:
    fields:
      arn: {}
      createdAt: {}
      deadLetterQueue: {}
      deliveryDelaySeconds: {}
      kmsKey: {}
      lastModified: {}
      maxReceiveCount: {}
      maximumMessageSize: {}
      messageRetentionPeriodSeconds: {}
      queueType: {}
      receiveMessageWaitTimeSeconds: {}
      region: {}
      sqsManagedSseEnabled: {}
      tags: {}
      url: {}
      visibilityTimeoutSeconds: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - aws
  aws.ssm:
    fields:
      instances: {}
      parameters: {}
    min_mondoo_version: latest
    platform:
      name:
      - aws
  aws.ssm.instance:
    fields:
      arn: {}
      instanceId: {}
      ipAddress: {}
      pingStatus: {}
      platformName: {}
      platformType: {}
      platformVersion: {}
      region: {}
      tags: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - aws
  aws.ssm.parameter:
    fields:
      allowedPattern: {}
      arn: {}
      dataType: {}
      description: {}
      kmsKey: {}
      lastModifiedDate: {}
      name: {}
      region: {}
      tier: {}
      type: {}
      version: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - aws
  aws.timestream.liveanalytics:
    fields:
      databases: {}
      tables: {}
    min_mondoo_version: 9.0.0
    platform:
      name:
      - aws
  aws.timestream.liveanalytics.database:
    fields:
      arn: {}
      createdAt: {}
      kmsKeyId: {}
      name: {}
      region: {}
      tableCount: {}
      updatedAt: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - aws
  aws.timestream.liveanalytics.table:
    fields:
      arn: {}
      createdAt: {}
      databaseName: {}
      magneticStoreWriteProperties: {}
      name: {}
      region: {}
      retentionProperties: {}
      updatedAt: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - aws
  aws.vpc:
    fields:
      arn: {}
      cidrBlock:
        min_mondoo_version: 9.0.0
      endpoints:
        min_mondoo_version: 9.0.0
      flowLogs: {}
      id: {}
      instanceTenancy:
        min_mondoo_version: 9.0.0
      internetGatewayBlockMode:
        min_mondoo_version: 9.0.0
      isDefault: {}
      name:
        min_mondoo_version: 9.0.0
      natGateways:
        min_mondoo_version: 9.0.0
      peeringConnections:
        min_mondoo_version: 9.0.0
      region: {}
      routeTables:
        min_mondoo_version: 9.0.0
      serviceEndpoints:
        min_mondoo_version: 9.0.0
      state: {}
      subnets:
        min_mondoo_version: 9.0.0
      tags: {}
    is_private: true
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
  aws.vpc.endpoint:
    fields:
      createdAt: {}
      id: {}
      policyDocument: {}
      privateDnsEnabled: {}
      region: {}
      serviceName: {}
      state: {}
      subnets: {}
      type: {}
      vpc: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - aws
  aws.vpc.flowlog:
    fields:
      createdAt:
        min_mondoo_version: 9.0.0
      deliverLogsStatus:
        min_mondoo_version: 9.0.0
      destination:
        min_mondoo_version: 9.0.0
      destinationType:
        min_mondoo_version: 9.0.0
      id: {}
      maxAggregationInterval:
        min_mondoo_version: 9.0.0
      region: {}
      status: {}
      tags: {}
      trafficType:
        min_mondoo_version: 9.0.0
      vpc: {}
    is_private: true
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
  aws.vpc.natgateway:
    fields:
      addresses: {}
      createdAt: {}
      natGatewayId: {}
      state: {}
      subnet: {}
      tags: {}
      vpc: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - aws
  aws.vpc.natgateway.address:
    fields:
      allocationId: {}
      isPrimary: {}
      networkInterfaceId: {}
      privateIp: {}
      publicIp: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - aws
  aws.vpc.peeringConnection:
    fields:
      acceptorVpc: {}
      expirationTime: {}
      id: {}
      requestorVpc: {}
      status: {}
      tags: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - aws
  aws.vpc.peeringConnection.peeringVpc:
    fields:
      allowDnsResolutionFromRemoteVpc: {}
      allowEgressFromLocalClassicLinkToRemoteVpc: {}
      allowEgressFromLocalVpcToRemoteClassicLink: {}
      ipv4CiderBlocks: {}
      ipv6CiderBlocks: {}
      ownerID: {}
      region: {}
      vpc: {}
      vpcId: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - aws
  aws.vpc.routetable:
    fields:
      associations:
        min_mondoo_version: 9.0.0
      id: {}
      routes: {}
      tags:
        min_mondoo_version: 9.0.0
    is_private: true
    min_mondoo_version: 5.15.0
    platform:
      name:
      - aws
  aws.vpc.routetable.association:
    fields:
      associationsState: {}
      gatewayId: {}
      main: {}
      routeTableAssociationId: {}
      routeTableId: {}
      subnet: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - aws
  aws.vpc.serviceEndpoint:
    fields:
      acceptanceRequired: {}
      availabilityZones: {}
      dnsNames: {}
      id: {}
      managesVpcEndpoints: {}
      name: {}
      owner: {}
      payerResponsibility: {}
      privateDnsNameVerificationState: {}
      privateDnsNames: {}
      tags: {}
      type: {}
      vpcEndpointPolicySupported: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - aws
  aws.vpc.subnet:
    fields:
      arn: {}
      assignIpv6AddressOnCreation: {}
      availabilityZone: {}
      availableIpAddressCount: {}
      cidrs: {}
      defaultForAvailabilityZone: {}
      id: {}
      internetGatewayBlockMode: {}
      mapPublicIpOnLaunch: {}
      region: {}
      state: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - aws
  aws.waf:
    fields:
      acls: {}
      ipSets: {}
      ruleGroups: {}
      scope: {}
    min_mondoo_version: 9.0.0
    platform:
      name:
      - aws
  aws.waf.acl:
    fields:
      arn: {}
      description: {}
      id: {}
      managedByFirewallManager: {}
      name: {}
      rules: {}
      scope: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - aws
  aws.waf.ipset:
    fields:
      addressType: {}
      addresses: {}
      arn: {}
      description: {}
      id: {}
      name: {}
      scope: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - aws
  aws.waf.rule:
    fields:
      action: {}
      belongsTo: {}
      id: {}
      name: {}
      priority: {}
      statement: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - aws
  aws.waf.rule.action:
    fields:
      action: {}
      responseCode: {}
      ruleName: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - aws
  aws.waf.rule.fieldtomatch:
    fields:
      allQueryArguments: {}
      body: {}
      cookie: {}
      headerOrder: {}
      headers: {}
      ja3Fingerprint: {}
      jsonBody: {}
      method: {}
      queryString: {}
      ruleName: {}
      singleHeader: {}
      singleQueryArgument: {}
      statementID: {}
      target: {}
      uriPath: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - aws
  aws.waf.rule.fieldtomatch.body:
    fields:
      overSizeHandling: {}
      ruleName: {}
      statementID: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - aws
  aws.waf.rule.fieldtomatch.cookie:
    fields:
      overSizeHandling: {}
      ruleName: {}
      statementID: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - aws
  aws.waf.rule.fieldtomatch.headerorder:
    fields:
      overSizeHandling: {}
      ruleName: {}
      statementID: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - aws
  aws.waf.rule.fieldtomatch.headers:
    fields:
      matchPattern: {}
      matchScope: {}
      overSizeHandling: {}
      ruleName: {}
      statementID: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - aws
  aws.waf.rule.fieldtomatch.headers.matchpattern:
    fields:
      all: {}
      excludeHeaders: {}
      includeHeaders: {}
      ruleName: {}
      statementID: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - aws
  aws.waf.rule.fieldtomatch.ja3fingerprint:
    fields:
      fallbackBehavior: {}
      ruleName: {}
      statementID: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - aws
  aws.waf.rule.fieldtomatch.jsonbody:
    fields:
      invalidFallbackBehavior: {}
      matchPattern: {}
      matchScope: {}
      overSizeHandling: {}
      ruleName: {}
      statementID: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - aws
  aws.waf.rule.fieldtomatch.jsonbody.matchpattern:
    fields:
      all: {}
      includePaths: {}
      ruleName: {}
      statementID: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - aws
  aws.waf.rule.fieldtomatch.singleheader:
    fields:
      name: {}
      ruleName: {}
      statementID: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - aws
  aws.waf.rule.fieldtomatch.singlequeryargument:
    fields:
      name: {}
      ruleName: {}
      statementID: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - aws
  aws.waf.rule.statement:
    fields:
      andStatement: {}
      byteMatchStatement: {}
      geoMatchStatement: {}
      id: {}
      ipSetReferenceStatement: {}
      json: {}
      kind: {}
      labelMatchStatement: {}
      managedRuleGroupStatement: {}
      notStatement: {}
      orStatement: {}
      rateBasedStatement: {}
      regexMatchStatement: {}
      regexPatternSetReferenceStatement: {}
      ruleGroupReferenceStatement: {}
      sizeConstraintStatement: {}
      sqliMatchStatement: {}
      xssMatchStatement: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - aws
  aws.waf.rule.statement.andstatement:
    fields:
      ruleName: {}
      statementID: {}
      statements: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - aws
  aws.waf.rule.statement.bytematchstatement:
    fields:
      fieldToMatch: {}
      ruleName: {}
      searchString: {}
      statementID: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - aws
  aws.waf.rule.statement.geomatchstatement:
    fields:
      countryCodes: {}
      ruleName: {}
      statementID: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - aws
  aws.waf.rule.statement.ipsetreferencestatement:
    fields:
      arn: {}
      ipSetForwardedIPConfig: {}
      ruleName: {}
      statementID: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - aws
  aws.waf.rule.statement.ipsetreferencestatement.ipsetforwardedipconfig:
    fields:
      fallbackBehavior: {}
      headerName: {}
      position: {}
      ruleName: {}
      statementID: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - aws
  aws.waf.rule.statement.labelmatchstatement:
    fields:
      key: {}
      ruleName: {}
      scope: {}
      statementID: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - aws
  aws.waf.rule.statement.managedrulegroupstatement:
    fields:
      name: {}
      ruleName: {}
      statementID: {}
      vendorName: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - aws
  aws.waf.rule.statement.notstatement:
    fields:
      ruleName: {}
      statement: {}
      statementID: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - aws
  aws.waf.rule.statement.orstatement:
    fields:
      ruleName: {}
      statementID: {}
      statements: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - aws
  aws.waf.rule.statement.ratebasedstatement:
    fields: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - aws
  aws.waf.rule.statement.regexmatchstatement:
    fields:
      fieldToMatch: {}
      regexString: {}
      ruleName: {}
      statementID: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - aws
  aws.waf.rule.statement.regexpatternsetreferencestatement:
    fields:
      arn: {}
      fieldToMatch: {}
      ruleName: {}
      statementID: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - aws
  aws.waf.rule.statement.rulegroupreferencestatement:
    fields:
      arn: {}
      excludeRules: {}
      ruleName: {}
      statementID: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - aws
  aws.waf.rule.statement.sizeconstraintstatement:
    fields:
      comparisonOperator: {}
      fieldToMatch: {}
      ruleName: {}
      size: {}
      statementID: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - aws
  aws.waf.rule.statement.sqlimatchstatement:
    fields:
      fieldToMatch: {}
      ruleName: {}
      sensitivityLevel: {}
      statementID: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - aws
  aws.waf.rule.statement.xssmatchstatement:
    fields:
      fieldToMatch: {}
      ruleName: {}
      statementID: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - aws
  aws.waf.rulegroup:
    fields:
      arn: {}
      description: {}
      id: {}
      name: {}
      rules: {}
      scope: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - aws
