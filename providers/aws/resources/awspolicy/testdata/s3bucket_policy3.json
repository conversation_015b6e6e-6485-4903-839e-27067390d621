{"Version": "2012-10-17", "Statement": [{"Sid": "AWSCloudTrailAclCheck1234567890", "Effect": "Allow", "Principal": {"Service": "cloudtrail.amazonaws.com"}, "Action": "s3:GetBucketAcl", "Resource": "arn:aws:s3:::temp-cloudtrail-test"}, {"Sid": "AWSCloudTrailWrite1234567890", "Effect": "Allow", "Principal": {"Service": "cloudtrail.amazonaws.com"}, "Action": "s3:PutObject", "Resource": "arn:aws:s3:::temp-cloudtrail-test/AWSLogs/1234567890/*", "Condition": {"StringEquals": {"s3:x-amz-acl": "bucket-owner-full-control"}}}]}