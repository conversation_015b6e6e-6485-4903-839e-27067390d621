// Copyright (c) Mondoo, Inc.
// SPDX-License-Identifier: BUSL-1.1

package awsec2ebsconn

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestParseInstanceId(t *testing.T) {
	path := "account/************/region/us-east-1/instances/i-07f67838ada5879af"
	id, err := ParseInstanceId(path)
	require.NoError(t, err)
	assert.Equal(t, id.Account, "************")
	assert.Equal(t, id.Region, "us-east-1")
	assert.Equal(t, id.Id, "i-07f67838ada5879af")
}
