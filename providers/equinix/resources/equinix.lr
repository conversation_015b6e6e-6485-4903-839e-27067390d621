// Copyright (c) Mondoo, Inc.
// SPDX-License-Identifier: BUSL-1.1

option provider = "go.mondoo.com/cnquery/v9/providers/equinix"
option go_package = "go.mondoo.com/cnquery/v11/providers/equinix/resources"

// Equinix Metal project
equinix.metal.project @defaults("name") {
  // Project ID
  id string
  // Project name
  name string
  // Organization the project belongs to
  organization() equinix.metal.organization
  // When the project was created
  createdAt time
  // When the project was last updated
  updatedAt time
  // URL
  url string
  // SSH keys
  sshKeys() []equinix.metal.sshkey
  // Devices
  devices() []equinix.metal.device
}

// Equinix Metal organization
equinix.metal.organization @defaults("name") {
  // Organization ID
  id string
  // Organization name
  name string
  // Organization description
  description string
  // Organization website URL
  website string
  // Organization X (formerly Twitter) handle
  twitter string
  // When the organization was created
  createdAt time
  // When the organization was last updated
  updatedAt time
  // Address information for the organization
  address dict
  // Organization's tax ID
  taxId string
  // Organization's main phone number
  mainPhone string
  // Organization's billing phone number
  billingPhone string
  // Organization's credit amount
  creditAmount float
  // URL
  url string
  // Users in the organization
 	users() []equinix.metal.user
}

// Equinix Metal user
private equinix.metal.user @defaults("email") {
  // User ID
  id string
  // User's first name
  firstName string
  // User's last name
  lastName string
  // User's full name
  fullName string
  // User's email address
  email string
  // User's two-factor authentication
  twoFactorAuth string
  // User's avatar
  avatarUrl string
  // User's X (formerly Twitter) handle
  twitter string
  // User's facebook account
  facebook string
  // User's LinkedIn account
  linkedin string
  // When the user was created
  createdAt time
  // When the user was last updated
  updatedAt time
  // User's time zone
  timezone string
  // User's phone number
  phoneNumber string
  // URL
  url string
}

// Equinix Metal SSH key
equinix.metal.sshkey @defaults("label") {
  // ID of the SSH key
  id string
  // Label of the SSH key
  label string
  // Key
  key string
  // Finger print
  fingerPrint string
  // When the key was created
  createdAt time
  // When the key was last updated
  updatedAt time
  // URL
  url string
}

// Equinix Metal device
equinix.metal.device {
  // Device ID
  id string
  // Device's short ID
  shortID string
  // Device URL
  url string
  // Device hostname
  hostname string
  // Description of the device
  description string
  // Current state of the device
  state string
  // When the device was created
  createdAt time
  // When the device was last updated
  updatedAt time
  // Whether the device is locked
  locked bool
  // Billing cycle used for the device
  billingCycle string
  // Whether the device is a Spot instance
  spotInstance bool
  // Operating system
  os dict
}
