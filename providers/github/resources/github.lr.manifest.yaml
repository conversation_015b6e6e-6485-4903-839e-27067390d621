# Copyright (c) Mondoo, Inc.
# SPDX-License-Identifier: BUSL-1.1

resources:
  git.commit:
    fields:
      author: {}
      committer: {}
      message: {}
      sha: {}
      signatureVerification: {}
    is_private: true
    min_mondoo_version: 6.11.0
  git.commitAuthor:
    fields:
      date: {}
      email: {}
      name: {}
      sha: {}
    is_private: true
    min_mondoo_version: 6.11.0
  git.gpgSignature:
    fields:
      payload: {}
      reason: {}
      sha: {}
      signature: {}
      verified: {}
    is_private: true
    min_mondoo_version: 6.11.0
  github:
    fields:
      repositories: {}
      user: {}
    is_private: true
    min_mondoo_version: 6.4.0
  github.branch:
    fields:
      headCommit: {}
      headCommitSha:
        min_mondoo_version: 9.0.0
      isDefault:
        min_mondoo_version: 6.8.0
      isProtected:
        min_mondoo_version: 8.10.0
      name: {}
      owner: {}
      protectionRules: {}
      repoName: {}
    is_private: true
    min_mondoo_version: 6.4.0
  github.branchprotection:
    fields:
      allowDeletions: {}
      allowForcePushes: {}
      enforceAdmins: {}
      id: {}
      requireLinearHistory: {}
      requiredConversationResolution: {}
      requiredPullRequestReviews: {}
      requiredSignatures:
        min_mondoo_version: 6.11.0
      requiredStatusChecks: {}
      restrictions: {}
    is_private: true
    min_mondoo_version: 6.4.0
  github.collaborator:
    fields:
      id: {}
      permissions: {}
      user: {}
    is_private: true
    min_mondoo_version: 6.11.0
  github.commit:
    fields:
      author: {}
      authoredDate:
        min_mondoo_version: 9.0.0
      commit:
        min_mondoo_version: 6.11.0
      committedDate:
        min_mondoo_version: 9.0.0
      committer: {}
      owner:
        min_mondoo_version: 6.11.0
      repository:
        min_mondoo_version: 6.11.0
      sha:
        min_mondoo_version: 6.11.0
      stats:
        min_mondoo_version: 6.11.0
      url: {}
    is_private: true
    min_mondoo_version: 6.4.0
  github.file:
    fields:
      content: {}
      downloadUrl:
        min_mondoo_version: 9.0.0
      exists:
        min_mondoo_version: 9.0.0
      files: {}
      isBinary: {}
      name:
        min_mondoo_version: 6.11.0
      ownerName:
        min_mondoo_version: 6.11.0
      path: {}
      repoName: {}
      sha: {}
      type: {}
    is_private: true
    min_mondoo_version: 6.4.0
  github.gist:
    fields:
      createdAt: {}
      description: {}
      files: {}
      id: {}
      owner: {}
      public: {}
      updatedAt: {}
    is_private: true
    min_mondoo_version: 7.14.0
  github.gistfile:
    fields:
      content: {}
      filename: {}
      gistId: {}
      language: {}
      rawUrl: {}
      size: {}
      type: {}
    is_private: true
    min_mondoo_version: 7.14.0
  github.installation:
    fields:
      appId: {}
      appSlug: {}
      createdAt: {}
      id: {}
      updatedAt: {}
    is_private: true
    min_mondoo_version: 5.31.0
  github.issue:
    fields:
      assignees: {}
      body: {}
      closedAt: {}
      closedBy: {}
      createdAt: {}
      id: {}
      number: {}
      state: {}
      title: {}
      updatedAt: {}
      url: {}
    is_private: true
    min_mondoo_version: 9.0.0
  github.license:
    fields:
      key: {}
      name: {}
      spdxId: {}
      url: {}
    is_private: true
    min_mondoo_version: 9.0.0
  github.mergeRequest:
    fields:
      assignees: {}
      commits: {}
      createdAt: {}
      id: {}
      labels: {}
      number: {}
      owner: {}
      repoName: {}
      reviews: {}
      state: {}
      title: {}
    is_private: true
    min_mondoo_version: 6.4.0
  github.organization:
    fields:
      avatarUrl:
        min_mondoo_version: 7.14.0
      billingEmail:
        min_mondoo_version: 6.11.0
      blog: {}
      collaborators: {}
      company: {}
      createdAt:
        min_mondoo_version: 6.11.0
      customProperties:
        min_mondoo_version: 9.0.0
      defaultRepositoryPermission:
        min_mondoo_version: 6.11.0
      description: {}
      diskUsage:
        min_mondoo_version: 6.11.0
      email: {}
      followers:
        min_mondoo_version: 7.14.0
      following:
        min_mondoo_version: 7.14.0
      hasOrganizationProjects:
        min_mondoo_version: 9.0.0
      hasRepositoryProjects:
        min_mondoo_version: 9.0.0
      id: {}
      installations:
        min_mondoo_version: 5.31.0
      isVerified:
        min_mondoo_version: 6.11.0
      location: {}
      login: {}
      members:
        min_mondoo_version: 5.31.0
      membersCanCreateInternalRepositories:
        min_mondoo_version: 6.11.0
      membersCanCreatePages:
        min_mondoo_version: 6.11.0
      membersCanCreatePrivatePages:
        min_mondoo_version: 6.11.0
      membersCanCreatePrivateRepositories:
        min_mondoo_version: 6.11.0
      membersCanCreatePublicPages:
        min_mondoo_version: 6.11.0
      membersCanCreatePublicRepositories:
        min_mondoo_version: 6.11.0
      membersCanCreateRepositories:
        min_mondoo_version: 6.11.0
      membersCanForkPrivateRepos:
        min_mondoo_version: 9.0.0
      name: {}
      nodeId:
        min_mondoo_version: 6.11.0
      owned_private_repos: {}
      ownedPrivateRepos:
        min_mondoo_version: 6.11.0
      owners:
        min_mondoo_version: 5.31.0
      packages:
        min_mondoo_version: 6.11.0
      plan: {}
      privateGists:
        min_mondoo_version: 6.11.0
      repositories:
        min_mondoo_version: 5.31.0
      teams:
        min_mondoo_version: 6.11.0
      totalPrivateRepos:
        min_mondoo_version: 6.11.0
      totalPublicRepos:
        min_mondoo_version: 9.0.0
      twitterUsername:
        min_mondoo_version: 6.11.0
      twoFactorRequirementEnabled:
        min_mondoo_version: 6.11.0
      updatedAt:
        min_mondoo_version: 6.11.0
      webhooks:
        min_mondoo_version: 6.11.0
    min_mondoo_version: 5.15.0
  github.organization.customProperty:
    fields:
      allowedValues: {}
      defaultValue: {}
      description: {}
      name: {}
      required: {}
      sourceType: {}
      valueType: {}
      valuesEditableBy: {}
    is_private: true
    min_mondoo_version: 9.0.0
  github.package:
    fields:
      createdAt: {}
      id: {}
      name: {}
      owner: {}
      packageType: {}
      repository: {}
      updatedAt: {}
      versionCount: {}
      visibility: {}
    is_private: true
    min_mondoo_version: 6.11.0
  github.packages:
    fields:
      internal: {}
      list: {}
      private: {}
      public: {}
    min_mondoo_version: 9.0.0
  github.release:
    fields:
      name: {}
      preRelease: {}
      tagName: {}
      url: {}
    is_private: true
    min_mondoo_version: 6.4.0
  github.repository:
    fields:
      adminCollaborators:
        min_mondoo_version: 9.0.0
      allMergeRequests:
        min_mondoo_version: 9.0.0
      allowAutoMerge:
        min_mondoo_version: 6.4.0
      allowForking:
        min_mondoo_version: 6.4.0
      allowMergeCommit:
        min_mondoo_version: 6.4.0
      allowRebaseMerge:
        min_mondoo_version: 6.4.0
      allowSquashMerge:
        min_mondoo_version: 6.4.0
      archived: {}
      branches:
        min_mondoo_version: 6.4.0
      cloneUrl:
        min_mondoo_version: 7.14.0
      closedIssues:
        min_mondoo_version: 7.14.0
      closedMergeRequests:
        min_mondoo_version: 9.0.0
      codeOfConduct:
        min_mondoo_version: 9.0.0
      codeOfConductFile:
        min_mondoo_version: 9.0.0
      collaborators:
        min_mondoo_version: 6.11.0
      commits:
        min_mondoo_version: 6.4.0
      contributors:
        min_mondoo_version: 6.4.0
      createdAt: {}
      customProperties:
        min_mondoo_version: 9.0.0
      defaultBranch:
        min_mondoo_version: 9.0.0
      defaultBranchName:
        min_mondoo_version: 6.4.0
      description: {}
      disabled: {}
      files:
        min_mondoo_version: 6.4.0
      forks:
        min_mondoo_version: 7.14.0
      forksCount:
        min_mondoo_version: 7.14.0
      fullName: {}
      hasDiscussions:
        min_mondoo_version: 9.0.0
      hasDownloads:
        min_mondoo_version: 7.14.0
      hasIssues:
        min_mondoo_version: 6.4.0
      hasPages:
        min_mondoo_version: 7.14.0
      hasProjects:
        min_mondoo_version: 7.14.0
      hasWiki:
        min_mondoo_version: 7.14.0
      homepage: {}
      id: {}
      isFork:
        min_mondoo_version: 7.14.0
      isTemplate:
        min_mondoo_version: 9.0.0
      language:
        min_mondoo_version: 7.14.0
      license:
        min_mondoo_version: 7.14.0
      name: {}
      openIssues:
        min_mondoo_version: 7.14.0
      openIssuesCount:
        min_mondoo_version: 7.14.0
      openMergeRequests:
        min_mondoo_version: 6.4.0
      organizationName:
        min_mondoo_version: 6.4.0
      owner:
        min_mondoo_version: 6.4.0
      private: {}
      pushedAt:
        min_mondoo_version: 7.14.0
      readme:
        min_mondoo_version: 9.0.0
      releases:
        min_mondoo_version: 6.4.0
      security:
        min_mondoo_version: 9.0.0
      securityFile:
        min_mondoo_version: 9.0.0
      sshUrl:
        min_mondoo_version: 7.14.0
      stargazers:
        min_mondoo_version: 7.14.0
      stargazersCount:
        min_mondoo_version: 7.14.0
      support:
        min_mondoo_version: 9.0.0
      supportFile:
        min_mondoo_version: 9.0.0
      topics:
        min_mondoo_version: 7.14.0
      updatedAt: {}
      visibility: {}
      watchersCount:
        min_mondoo_version: 7.14.0
      webhooks:
        min_mondoo_version: 6.11.0
      workflows:
        min_mondoo_version: 6.11.0
    is_private: true
    min_mondoo_version: 5.31.0
  github.repository.file:
    fields:
      content: {}
      downloadUrl: {}
      files: {}
      isBinary: {}
      name: {}
      ownerName: {}
      path: {}
      repoName: {}
      sha: {}
      type: {}
    min_mondoo_version: 9.0.0
  github.review:
    fields:
      authorAssociation: {}
      state: {}
      url: {}
      user: {}
    is_private: true
    min_mondoo_version: 6.4.0
  github.team:
    fields:
      defaultPermission: {}
      description: {}
      id: {}
      members: {}
      name: {}
      organization: {}
      privacy: {}
      repositories: {}
      slug: {}
    is_private: true
    min_mondoo_version: 6.11.0
  github.user:
    fields:
      avatarUrl:
        min_mondoo_version: 7.14.0
      bio: {}
      blog:
        min_mondoo_version: 7.14.0
      company:
        min_mondoo_version: 6.4.0
      createdAt: {}
      email: {}
      followers:
        min_mondoo_version: 7.14.0
      following:
        min_mondoo_version: 7.14.0
      gists:
        min_mondoo_version: 7.14.0
      id: {}
      location:
        min_mondoo_version: 7.14.0
      login: {}
      name: {}
      repositories:
        min_mondoo_version: 7.14.0
      suspendedAt: {}
      twitterUsername:
        min_mondoo_version: 7.14.0
      updatedAt: {}
    is_private: true
    min_mondoo_version: 5.31.0
  github.webhook:
    fields:
      active: {}
      config: {}
      events: {}
      id: {}
      name: {}
      url: {}
    is_private: true
    min_mondoo_version: 6.11.0
  github.workflow:
    fields:
      configuration: {}
      createdAt: {}
      file: {}
      id: {}
      name: {}
      path: {}
      state: {}
      updatedAt: {}
    is_private: true
    min_mondoo_version: 6.11.0
