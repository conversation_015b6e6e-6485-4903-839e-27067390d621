// Copyright (c) Mondoo, Inc.
// SPDX-License-Identifier: BUSL-1.1

option provider = "go.mondoo.com/cnquery/v9/providers/github"
option go_package = "go.mondoo.com/cnquery/v11/providers/github/resources"

private github {}

// Git commit
private git.commit @defaults("sha") {
  // Git commit SHA
  sha string
  // Git commit message
  message string
  // Git commit author
  author git.commitAuthor
  // Git commit committer
  committer git.commitAuthor
  // The GPG signature of the commit if there is one
  signatureVerification git.gpgSignature
}

// Git commit author
private git.commitAuthor @defaults("name email") {
  // Author shasum
  sha string
  // Author name
  name string
  // Author email
  email string
  // Date of commit
  date time
}

// Git GPG signature
private git.gpgSignature @defaults("sha") {
  // GPG signature shasum
  sha string
  // GPG signature reason
  reason string
  // Whether GPG signature is verified
  verified bool
  // GPG signature payload
  payload string
  // GPG signature
  signature string
}

// GitHub organization
github.organization @defaults("login name") {
  // Organization login
  login string
  // Organization ID
  id int
  // Organization global node ID
  nodeId string
  // Organization name
  name string
  // Organization company
  company string
  // Organization blog
  blog string
  // Organization location
  location string
  // Organization email
  email string
  // Organization Twitter handle
  twitterUsername string
  // Organization profile picture URL
  avatarUrl string
  // Organization's number of followers
  followers int
  // Number of organizations the organization is following
  following int
  // Organization description
  description string
  // Create time for the organization
  createdAt time
  // Update time for the organization
  updatedAt time
  // Number of private repositories
  totalPrivateRepos int
  // Number of public repositories
  totalPublicRepos int
  // Number of owned private repositories for the organization
  ownedPrivateRepos int
  // Number of private gists
  privateGists int
  // Disk usage for the organization
  diskUsage int
  // Number of collaborators for the organization
  collaborators int
  // Organization billing email
  billingEmail string
  // GitHub plan the organization is subscribed to
  plan dict
  // Whether two-factor authentication is required for all members. This value will be null if the API token does not have owner access on the organization.
  twoFactorRequirementEnabled bool
  // Whether the organization is verified by GitHub
  isVerified bool
  // The default repository permission
  defaultRepositoryPermission string
  // Whether members can create repositories
  membersCanCreateRepositories bool
  // Whether members can create public repositories
  membersCanCreatePublicRepositories bool
  // Whether members can create private repositories
  membersCanCreatePrivateRepositories bool
  // Whether members can create internal repositories
  membersCanCreateInternalRepositories bool
  // Whether members can create pages
  membersCanCreatePages bool
  // Whether members can create public pages
  membersCanCreatePublicPages bool
  // Whether members can create private pages
  membersCanCreatePrivatePages bool
  // Whether members can fork private repositories to their own GitHub account
  membersCanForkPrivateRepos bool
  // List of users that are part of the owners group
  owners() []github.user
  // List of users that are part of the members group
  members() []github.user
  // List of users that are part of the teams group
  teams() []github.team
  // List of repositories
  repositories() []github.repository
  // List of GitHub installations
  installations() []github.installation
  // List of webhooks
  webhooks() []github.webhook
  // List of packages
  packages() []github.package
  // Whether the organization has projects
  hasOrganizationProjects bool
  // Whether projects in the organization have projects
  hasRepositoryProjects bool
  // Organization custom properties
  customProperties() []github.organization.customProperty
}

// GitHub organization-level custom property
private github.organization.customProperty @defaults("name required") {
  // The name of the property
  name string
  // Short description of the property
  description string
  // Source type of the property (where it was created)
  sourceType string
  // Type of the value for the property
  valueType string
  // Whether the property is required
  required bool
  // Default value of the property
  defaultValue string
  // An ordered list of the allowed values of the property
  allowedValues []string
  // Who can edit the values of the property
  valuesEditableBy string
}

// GitHub user
private github.user @defaults("login name email company") {
  // User ID
  id int
  // User login
  login string
  // User name
  name string
  // User email
  email string
  // User bio
  bio string
  // User blog URL
  blog string
  // User location
  location string
  // User profile picture URL
  avatarUrl string
  // User followers
  followers int
  // User following
  following int
  // User Twitter handle
  twitterUsername string
  // User create time in UTC
  createdAt time
  // Last user update time in UTC
  updatedAt time
  // When the user was suspended
  suspendedAt time
  // User's company
  company string
  // User's repositories
  repositories() []github.repository
  // User gists
  gists() []github.gist
}

// GitHub team
private github.team @defaults("id name") {
  // Team ID
  id int
  // Team name
  name string
  // Team description
  description string
  // Team slug
  slug string
  // Team privacy configuration
  privacy string
  // Team default permission
  defaultPermission string
  // Team members
  members() []github.user
  // Team owned repositories
  repositories() []github.repository
  // Team organization
  organization github.organization
}

// GitHub collaborator
private github.collaborator @defaults("user.login user.name") {
  // Collaborator ID
  id int
  // Collaborator's user information
  user github.user
  // Collaborator's permissions
  permissions []string
}

// GitHub package
private github.package @defaults("name packageType visibility repository.fullName") {
  // Package ID
  id int
  // Package name
  name string
  // Package type
  packageType string
  // Package owner
  owner github.user
  // Package create time
  createdAt time
  // Package update time
  updatedAt time
  // Package version numbers
  versionCount int
  // Package visibility
  visibility string
  // Package repository information
  repository() github.repository
}

// GitHub packages
github.packages {
  []github.package
  // Public packages
  public() []github.package
  // Private packages
  private() []github.package
  // Internal packages
  internal() []github.package
}

// GitHub repository
private github.repository @defaults("fullName") {
  init(name string) // can only be used when logged in to github as a user
  // Repository ID
  id int
  // Repository name
  name string
  // Repository full name
  fullName string
  // Repository description
  description string
  // Repository clone URL
  cloneUrl string
  // Repository SSH URL
  sshUrl string
  // Repository homepage
  homepage string
  // Repository topics
  topics []string
  // Repository language
  language string
  // Number of users watching the repository
  watchersCount int
  // Number of repository forks
  forksCount int
  // Number of repository stargazers
  stargazersCount int
  // Number of open issues in repository
  openIssuesCount int
  // Repository create time
  createdAt time
  // Repository update time
  updatedAt time
  // Repository pushed time
  pushedAt time
  // Whether the repository is archived
  archived bool
  // Whether the repository is disabled
  disabled bool
  // Whether the repository is private
  private bool
  // Whether the repository is a fork
  isFork bool
  // Repository visibility
  visibility string
  // Whether the repository allows auto merging
  allowAutoMerge bool
  // Whether the repository allows forking
  allowForking bool
  // Whether the repository allows merge commit
  allowMergeCommit bool
  // Whether the repository allows rebase merge
  allowRebaseMerge bool
  // Whether the repository allows squash merge
  allowSquashMerge bool
  // Whether the repository has open issues
  hasIssues bool
  // Whether the repository has projects
  hasProjects bool
  // Whether the repository has a wiki
  hasWiki bool
  // Whether the repository has pages
  hasPages bool
  // Whether the repository has downloads
  hasDownloads bool
  // Whether the repository has discussions
  hasDiscussions bool
  // Whether the repository is an organization repository template
  isTemplate bool
  // Repository custom properties
  customProperties dict
  // List of open merge requests for the repository
  openMergeRequests() []github.mergeRequest
  // List of closed merge requests for the repository
  closedMergeRequests() []github.mergeRequest
  // List of all merge requests for the repository
  allMergeRequests() []github.mergeRequest
  // List of branches for the repository
  branches() []github.branch
  // Default branch name for the repository
  defaultBranchName string
  // Default branch
  defaultBranch() github.branch
  // List of commits for the repository
  commits() []github.commit
  // List of contributors for the repository
  contributors() []github.user
  // List of all collaborators for the repository
  collaborators() []github.collaborator
  // List of admin collaborators for the repository
  adminCollaborators() []github.collaborator
  // List of files in the repository
  files() []github.file
  // List of releases for the repository
  releases() []github.release
  // Repository owner
  owner github.user
  // List of webhooks for the repository
  webhooks() []github.webhook
  // List of workflows for the repository
  workflows() []github.workflow
  // List of repository forks
  forks() []github.repository
  // List of repository stargazers
  stargazers() []github.user
  // List of repository open issues
  openIssues() []github.issue
  // List of repository closed issues
  closedIssues() []github.issue
  // Repository license
  license() github.license
  // Repository code of conduct
  codeOfConductFile() github.file
  // Repository support file
  supportFile() github.file
  // Repository security file
  securityFile() github.file
}

// GitHub license
private github.license @defaults("spdxId") {
  // License key
  key string
  // License name
  name string
  // License URL
  url string
  // License spdx ID
  spdxId string
}

// GitHub repository file
private github.file @defaults("name type") {
  // File path
  path string
  // File name
  name string
  // File type
  type string
  // File shasum
  sha string
  // Whether the file is a binary
  isBinary bool
  // List of files in the directory
  files() []github.file
  // File owner
  ownerName string
  // File repository name
  repoName string
  // File content
  content() string
  // File download URL
  downloadUrl string
  // Whether the file exists in the repository
  exists bool
}

// GitHub release
private github.release @defaults("name tagName") {
  // Release url
  url string
  // Release name
  name string
  // Release tag name
  tagName string
  // Whether the release is a pre-release
  preRelease bool
}

// GitHub webhook
private github.webhook @defaults("id name") {
  // Webhook ID
  id int
  // Webhook name
  name string
  // Webhook URL
  url string
  // List of events for the webhook
  events []string
  // Webhook config
  config dict
  // Whether the webhook is active
  active bool
}

// GitHub workflow
private github.workflow @defaults("id name") {
  // Workflow ID
  id int
  // Workflow name
  name string
  // Workflow path
  path string
  // Workflow state
  state string
  // Workflow create time
  createdAt time
  // Workflow update time
  updatedAt time
  // Workflow file
  file() github.file
  // Workflow configuration
  configuration() dict
}

// GitHub repository branch
private github.branch @defaults("name") {
  // Repository branch name
  name string
  // Whether branch protection is enabled
  isProtected bool
  // Repository branch HEAD commit
  headCommit() github.commit
  // Repository branch HEAD commit SHA sum
  headCommitSha string
  // Repository branch protection rules
  protectionRules() github.branchprotection
  // Repository branch repository name
  repoName string
  // Repository branch owner
  owner github.user
  // Whether the branch is the default branch
  isDefault bool
}

// GitHub repository branch protection
private github.branchprotection @defaults("id") {
  // Repository branch protection ID
  id string
  // Require status checks to pass before merging
  requiredStatusChecks dict
  // Require a pull request before merging
  requiredPullRequestReviews dict
  // Require conversation resolution before merging
  requiredConversationResolution dict
  // Whether signed commits are required
  requiredSignatures bool
  // Require linear history
  requireLinearHistory dict
  // Include administrators
  enforceAdmins dict
  // Restrict who can push to matching branches
  restrictions dict
  // Allow force pushes
  allowForcePushes dict
  // Allow deletions
  allowDeletions dict
}

// GitHub repository commit
private github.commit @defaults("sha") {
  // Commit owner
  owner string
  // Commit repository
  repository string
  // Commit SHA
  sha string
  // Commit URL
  url string
  // Commit author
  author github.user
  // Commit committer
  committer github.user
  // Commit resource object
  commit git.commit
  // Commit stats
  stats dict
  // Authored date
  authoredDate time
  // Committed date
  committedDate time
}

// GitHub repository pull request
private github.mergeRequest @defaults("id state") {
  // Pull request ID
  id int
  // Pull request number
  number int
  // Pull request state
  state string
  // Pull request creation time (in UTC)
  createdAt time
  // Pull request labels
  labels []dict
  // Pull request title
  title string
  // Pull request owner
  owner github.user
  // Pull request assignees
  assignees []github.user
  // Pull request commits
  commits() []github.commit
  // Pull request reviews
  reviews() []github.review
  // Pull request repository name
  repoName string
}

// GitHub repository review
private github.review @defaults("url state") {
  // Review URL
  url string
  // Review state
  state string
  // Author association
  authorAssociation string
  // Review user information
  user github.user
}

// GitHub application installation
private github.installation @defaults("id appId") {
  // Application installation ID
  id int
  // Application configured ID
  appId int
  // Application configured slug
  appSlug string
  // Application installation create time
  createdAt time
  // Application installation update time
  updatedAt time
}

// GitHub gist
private github.gist @defaults("description") {
  // Gist ID
  id string
  // Gist description
  description string
  // Gist create time
  createdAt time
  // Gist update time
  updatedAt time
  // Gist owner
  owner github.user
  // Whether the gist is public
  public bool
  // Gist files
  files []github.gistfile
}

// GitHub gist file
private github.gistfile @defaults("filename") {
  // Gist ID
  gistId string
  // Gist file name
  filename string
  // Gist file type
  type string
  // Gist file language
  language string
  // Gist file raw URL
  rawUrl string
  // Gist file size
  size int
  // Gist file content
  content() string
}

// GitHub issue
private github.issue @defaults("title") {
  // Issue ID
  id int
  // Issue number
  number int
  // Issue title
  title string
  // Issue state
  state string
  // Issue body
  body string
  // Issue URL
  url string
  // Issue create time
  createdAt time
  // Issue update time
  updatedAt time
  // Issue closed time
  closedAt time
  // Users to whom the issue is assigned
  assignees []github.user
  // User who closed the issue
  closedBy github.user
}
