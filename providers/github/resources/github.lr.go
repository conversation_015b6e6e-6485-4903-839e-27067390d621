// Copyright (c) Mondoo, Inc.
// SPDX-License-Identifier: BUSL-1.1

// Code generated by resources. DO NOT EDIT.

package resources

import (
	"errors"
	"time"

	"go.mondoo.com/cnquery/v11/llx"
	"go.mondoo.com/cnquery/v11/providers-sdk/v1/plugin"
	"go.mondoo.com/cnquery/v11/types"
)

var resourceFactories map[string]plugin.ResourceFactory

func init() {
	resourceFactories = map[string]plugin.ResourceFactory {
		"github": {
			// to override args, implement: initGithub(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createGithub,
		},
		"git.commit": {
			// to override args, implement: initGitCommit(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createGitCommit,
		},
		"git.commitAuthor": {
			// to override args, implement: initGitCommitAuthor(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createGitCommitAuthor,
		},
		"git.gpgSignature": {
			// to override args, implement: initGitGpgSignature(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createGitGpgSignature,
		},
		"github.organization": {
			Init: initGithubOrganization,
			Create: createGithubOrganization,
		},
		"github.organization.customProperty": {
			// to override args, implement: initGithubOrganizationCustomProperty(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createGithubOrganizationCustomProperty,
		},
		"github.user": {
			Init: initGithubUser,
			Create: createGithubUser,
		},
		"github.team": {
			// to override args, implement: initGithubTeam(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createGithubTeam,
		},
		"github.collaborator": {
			// to override args, implement: initGithubCollaborator(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createGithubCollaborator,
		},
		"github.package": {
			// to override args, implement: initGithubPackage(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createGithubPackage,
		},
		"github.packages": {
			// to override args, implement: initGithubPackages(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createGithubPackages,
		},
		"github.repository": {
			Init: initGithubRepository,
			Create: createGithubRepository,
		},
		"github.license": {
			// to override args, implement: initGithubLicense(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createGithubLicense,
		},
		"github.file": {
			// to override args, implement: initGithubFile(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createGithubFile,
		},
		"github.release": {
			// to override args, implement: initGithubRelease(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createGithubRelease,
		},
		"github.webhook": {
			// to override args, implement: initGithubWebhook(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createGithubWebhook,
		},
		"github.workflow": {
			// to override args, implement: initGithubWorkflow(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createGithubWorkflow,
		},
		"github.branch": {
			// to override args, implement: initGithubBranch(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createGithubBranch,
		},
		"github.branchprotection": {
			// to override args, implement: initGithubBranchprotection(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createGithubBranchprotection,
		},
		"github.commit": {
			// to override args, implement: initGithubCommit(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createGithubCommit,
		},
		"github.mergeRequest": {
			// to override args, implement: initGithubMergeRequest(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createGithubMergeRequest,
		},
		"github.review": {
			// to override args, implement: initGithubReview(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createGithubReview,
		},
		"github.installation": {
			// to override args, implement: initGithubInstallation(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createGithubInstallation,
		},
		"github.gist": {
			// to override args, implement: initGithubGist(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createGithubGist,
		},
		"github.gistfile": {
			// to override args, implement: initGithubGistfile(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createGithubGistfile,
		},
		"github.issue": {
			// to override args, implement: initGithubIssue(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createGithubIssue,
		},
	}
}

// NewResource is used by the runtime of this plugin to create new resources.
// Its arguments may be provided by users. This function is generally not
// used by initializing resources from recordings or from lists.
func NewResource(runtime *plugin.Runtime, name string, args map[string]*llx.RawData) (plugin.Resource, error) {
	f, ok := resourceFactories[name]
	if !ok {
		return nil, errors.New("cannot find resource " + name + " in this provider")
	}

	if f.Init != nil {
		cargs, res, err := f.Init(runtime, args)
		if err != nil {
			return res, err
		}

		if res != nil {
			id := name+"\x00"+res.MqlID()
			if x, ok := runtime.Resources.Get(id); ok {
				return x, nil
			}
			runtime.Resources.Set(id, res)
			return res, nil
		}

		args = cargs
	}

	res, err := f.Create(runtime, args)
	if err != nil {
		return nil, err
	}

	id := name+"\x00"+res.MqlID()
	if x, ok := runtime.Resources.Get(id); ok {
		return x, nil
	}

	runtime.Resources.Set(id, res)
	return res, nil
}

// CreateResource is used by the runtime of this plugin to create resources.
// Its arguments must be complete and pre-processed. This method is used
// for initializing resources from recordings or from lists.
func CreateResource(runtime *plugin.Runtime, name string, args map[string]*llx.RawData) (plugin.Resource, error) {
	f, ok := resourceFactories[name]
	if !ok {
		return nil, errors.New("cannot find resource " + name + " in this provider")
	}

	res, err := f.Create(runtime, args)
	if err != nil {
		return nil, err
	}

	id := name+"\x00"+res.MqlID()
	if x, ok := runtime.Resources.Get(id); ok {
		return x, nil
	}

	runtime.Resources.Set(id, res)
	return res, nil
}

var getDataFields = map[string]func(r plugin.Resource) *plugin.DataRes{
	"git.commit.sha": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGitCommit).GetSha()).ToDataRes(types.String)
	},
	"git.commit.message": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGitCommit).GetMessage()).ToDataRes(types.String)
	},
	"git.commit.author": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGitCommit).GetAuthor()).ToDataRes(types.Resource("git.commitAuthor"))
	},
	"git.commit.committer": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGitCommit).GetCommitter()).ToDataRes(types.Resource("git.commitAuthor"))
	},
	"git.commit.signatureVerification": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGitCommit).GetSignatureVerification()).ToDataRes(types.Resource("git.gpgSignature"))
	},
	"git.commitAuthor.sha": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGitCommitAuthor).GetSha()).ToDataRes(types.String)
	},
	"git.commitAuthor.name": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGitCommitAuthor).GetName()).ToDataRes(types.String)
	},
	"git.commitAuthor.email": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGitCommitAuthor).GetEmail()).ToDataRes(types.String)
	},
	"git.commitAuthor.date": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGitCommitAuthor).GetDate()).ToDataRes(types.Time)
	},
	"git.gpgSignature.sha": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGitGpgSignature).GetSha()).ToDataRes(types.String)
	},
	"git.gpgSignature.reason": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGitGpgSignature).GetReason()).ToDataRes(types.String)
	},
	"git.gpgSignature.verified": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGitGpgSignature).GetVerified()).ToDataRes(types.Bool)
	},
	"git.gpgSignature.payload": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGitGpgSignature).GetPayload()).ToDataRes(types.String)
	},
	"git.gpgSignature.signature": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGitGpgSignature).GetSignature()).ToDataRes(types.String)
	},
	"github.organization.login": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubOrganization).GetLogin()).ToDataRes(types.String)
	},
	"github.organization.id": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubOrganization).GetId()).ToDataRes(types.Int)
	},
	"github.organization.nodeId": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubOrganization).GetNodeId()).ToDataRes(types.String)
	},
	"github.organization.name": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubOrganization).GetName()).ToDataRes(types.String)
	},
	"github.organization.company": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubOrganization).GetCompany()).ToDataRes(types.String)
	},
	"github.organization.blog": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubOrganization).GetBlog()).ToDataRes(types.String)
	},
	"github.organization.location": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubOrganization).GetLocation()).ToDataRes(types.String)
	},
	"github.organization.email": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubOrganization).GetEmail()).ToDataRes(types.String)
	},
	"github.organization.twitterUsername": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubOrganization).GetTwitterUsername()).ToDataRes(types.String)
	},
	"github.organization.avatarUrl": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubOrganization).GetAvatarUrl()).ToDataRes(types.String)
	},
	"github.organization.followers": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubOrganization).GetFollowers()).ToDataRes(types.Int)
	},
	"github.organization.following": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubOrganization).GetFollowing()).ToDataRes(types.Int)
	},
	"github.organization.description": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubOrganization).GetDescription()).ToDataRes(types.String)
	},
	"github.organization.createdAt": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubOrganization).GetCreatedAt()).ToDataRes(types.Time)
	},
	"github.organization.updatedAt": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubOrganization).GetUpdatedAt()).ToDataRes(types.Time)
	},
	"github.organization.totalPrivateRepos": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubOrganization).GetTotalPrivateRepos()).ToDataRes(types.Int)
	},
	"github.organization.totalPublicRepos": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubOrganization).GetTotalPublicRepos()).ToDataRes(types.Int)
	},
	"github.organization.ownedPrivateRepos": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubOrganization).GetOwnedPrivateRepos()).ToDataRes(types.Int)
	},
	"github.organization.privateGists": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubOrganization).GetPrivateGists()).ToDataRes(types.Int)
	},
	"github.organization.diskUsage": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubOrganization).GetDiskUsage()).ToDataRes(types.Int)
	},
	"github.organization.collaborators": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubOrganization).GetCollaborators()).ToDataRes(types.Int)
	},
	"github.organization.billingEmail": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubOrganization).GetBillingEmail()).ToDataRes(types.String)
	},
	"github.organization.plan": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubOrganization).GetPlan()).ToDataRes(types.Dict)
	},
	"github.organization.twoFactorRequirementEnabled": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubOrganization).GetTwoFactorRequirementEnabled()).ToDataRes(types.Bool)
	},
	"github.organization.isVerified": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubOrganization).GetIsVerified()).ToDataRes(types.Bool)
	},
	"github.organization.defaultRepositoryPermission": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubOrganization).GetDefaultRepositoryPermission()).ToDataRes(types.String)
	},
	"github.organization.membersCanCreateRepositories": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubOrganization).GetMembersCanCreateRepositories()).ToDataRes(types.Bool)
	},
	"github.organization.membersCanCreatePublicRepositories": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubOrganization).GetMembersCanCreatePublicRepositories()).ToDataRes(types.Bool)
	},
	"github.organization.membersCanCreatePrivateRepositories": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubOrganization).GetMembersCanCreatePrivateRepositories()).ToDataRes(types.Bool)
	},
	"github.organization.membersCanCreateInternalRepositories": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubOrganization).GetMembersCanCreateInternalRepositories()).ToDataRes(types.Bool)
	},
	"github.organization.membersCanCreatePages": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubOrganization).GetMembersCanCreatePages()).ToDataRes(types.Bool)
	},
	"github.organization.membersCanCreatePublicPages": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubOrganization).GetMembersCanCreatePublicPages()).ToDataRes(types.Bool)
	},
	"github.organization.membersCanCreatePrivatePages": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubOrganization).GetMembersCanCreatePrivatePages()).ToDataRes(types.Bool)
	},
	"github.organization.membersCanForkPrivateRepos": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubOrganization).GetMembersCanForkPrivateRepos()).ToDataRes(types.Bool)
	},
	"github.organization.owners": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubOrganization).GetOwners()).ToDataRes(types.Array(types.Resource("github.user")))
	},
	"github.organization.members": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubOrganization).GetMembers()).ToDataRes(types.Array(types.Resource("github.user")))
	},
	"github.organization.teams": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubOrganization).GetTeams()).ToDataRes(types.Array(types.Resource("github.team")))
	},
	"github.organization.repositories": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubOrganization).GetRepositories()).ToDataRes(types.Array(types.Resource("github.repository")))
	},
	"github.organization.installations": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubOrganization).GetInstallations()).ToDataRes(types.Array(types.Resource("github.installation")))
	},
	"github.organization.webhooks": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubOrganization).GetWebhooks()).ToDataRes(types.Array(types.Resource("github.webhook")))
	},
	"github.organization.packages": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubOrganization).GetPackages()).ToDataRes(types.Array(types.Resource("github.package")))
	},
	"github.organization.hasOrganizationProjects": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubOrganization).GetHasOrganizationProjects()).ToDataRes(types.Bool)
	},
	"github.organization.hasRepositoryProjects": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubOrganization).GetHasRepositoryProjects()).ToDataRes(types.Bool)
	},
	"github.organization.customProperties": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubOrganization).GetCustomProperties()).ToDataRes(types.Array(types.Resource("github.organization.customProperty")))
	},
	"github.organization.customProperty.name": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubOrganizationCustomProperty).GetName()).ToDataRes(types.String)
	},
	"github.organization.customProperty.description": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubOrganizationCustomProperty).GetDescription()).ToDataRes(types.String)
	},
	"github.organization.customProperty.sourceType": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubOrganizationCustomProperty).GetSourceType()).ToDataRes(types.String)
	},
	"github.organization.customProperty.valueType": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubOrganizationCustomProperty).GetValueType()).ToDataRes(types.String)
	},
	"github.organization.customProperty.required": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubOrganizationCustomProperty).GetRequired()).ToDataRes(types.Bool)
	},
	"github.organization.customProperty.defaultValue": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubOrganizationCustomProperty).GetDefaultValue()).ToDataRes(types.String)
	},
	"github.organization.customProperty.allowedValues": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubOrganizationCustomProperty).GetAllowedValues()).ToDataRes(types.Array(types.String))
	},
	"github.organization.customProperty.valuesEditableBy": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubOrganizationCustomProperty).GetValuesEditableBy()).ToDataRes(types.String)
	},
	"github.user.id": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubUser).GetId()).ToDataRes(types.Int)
	},
	"github.user.login": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubUser).GetLogin()).ToDataRes(types.String)
	},
	"github.user.name": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubUser).GetName()).ToDataRes(types.String)
	},
	"github.user.email": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubUser).GetEmail()).ToDataRes(types.String)
	},
	"github.user.bio": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubUser).GetBio()).ToDataRes(types.String)
	},
	"github.user.blog": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubUser).GetBlog()).ToDataRes(types.String)
	},
	"github.user.location": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubUser).GetLocation()).ToDataRes(types.String)
	},
	"github.user.avatarUrl": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubUser).GetAvatarUrl()).ToDataRes(types.String)
	},
	"github.user.followers": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubUser).GetFollowers()).ToDataRes(types.Int)
	},
	"github.user.following": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubUser).GetFollowing()).ToDataRes(types.Int)
	},
	"github.user.twitterUsername": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubUser).GetTwitterUsername()).ToDataRes(types.String)
	},
	"github.user.createdAt": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubUser).GetCreatedAt()).ToDataRes(types.Time)
	},
	"github.user.updatedAt": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubUser).GetUpdatedAt()).ToDataRes(types.Time)
	},
	"github.user.suspendedAt": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubUser).GetSuspendedAt()).ToDataRes(types.Time)
	},
	"github.user.company": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubUser).GetCompany()).ToDataRes(types.String)
	},
	"github.user.repositories": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubUser).GetRepositories()).ToDataRes(types.Array(types.Resource("github.repository")))
	},
	"github.user.gists": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubUser).GetGists()).ToDataRes(types.Array(types.Resource("github.gist")))
	},
	"github.team.id": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubTeam).GetId()).ToDataRes(types.Int)
	},
	"github.team.name": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubTeam).GetName()).ToDataRes(types.String)
	},
	"github.team.description": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubTeam).GetDescription()).ToDataRes(types.String)
	},
	"github.team.slug": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubTeam).GetSlug()).ToDataRes(types.String)
	},
	"github.team.privacy": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubTeam).GetPrivacy()).ToDataRes(types.String)
	},
	"github.team.defaultPermission": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubTeam).GetDefaultPermission()).ToDataRes(types.String)
	},
	"github.team.members": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubTeam).GetMembers()).ToDataRes(types.Array(types.Resource("github.user")))
	},
	"github.team.repositories": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubTeam).GetRepositories()).ToDataRes(types.Array(types.Resource("github.repository")))
	},
	"github.team.organization": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubTeam).GetOrganization()).ToDataRes(types.Resource("github.organization"))
	},
	"github.collaborator.id": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubCollaborator).GetId()).ToDataRes(types.Int)
	},
	"github.collaborator.user": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubCollaborator).GetUser()).ToDataRes(types.Resource("github.user"))
	},
	"github.collaborator.permissions": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubCollaborator).GetPermissions()).ToDataRes(types.Array(types.String))
	},
	"github.package.id": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubPackage).GetId()).ToDataRes(types.Int)
	},
	"github.package.name": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubPackage).GetName()).ToDataRes(types.String)
	},
	"github.package.packageType": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubPackage).GetPackageType()).ToDataRes(types.String)
	},
	"github.package.owner": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubPackage).GetOwner()).ToDataRes(types.Resource("github.user"))
	},
	"github.package.createdAt": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubPackage).GetCreatedAt()).ToDataRes(types.Time)
	},
	"github.package.updatedAt": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubPackage).GetUpdatedAt()).ToDataRes(types.Time)
	},
	"github.package.versionCount": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubPackage).GetVersionCount()).ToDataRes(types.Int)
	},
	"github.package.visibility": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubPackage).GetVisibility()).ToDataRes(types.String)
	},
	"github.package.repository": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubPackage).GetRepository()).ToDataRes(types.Resource("github.repository"))
	},
	"github.packages.public": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubPackages).GetPublic()).ToDataRes(types.Array(types.Resource("github.package")))
	},
	"github.packages.private": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubPackages).GetPrivate()).ToDataRes(types.Array(types.Resource("github.package")))
	},
	"github.packages.internal": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubPackages).GetInternal()).ToDataRes(types.Array(types.Resource("github.package")))
	},
	"github.packages.list": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubPackages).GetList()).ToDataRes(types.Array(types.Resource("github.package")))
	},
	"github.repository.id": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubRepository).GetId()).ToDataRes(types.Int)
	},
	"github.repository.name": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubRepository).GetName()).ToDataRes(types.String)
	},
	"github.repository.fullName": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubRepository).GetFullName()).ToDataRes(types.String)
	},
	"github.repository.description": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubRepository).GetDescription()).ToDataRes(types.String)
	},
	"github.repository.cloneUrl": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubRepository).GetCloneUrl()).ToDataRes(types.String)
	},
	"github.repository.sshUrl": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubRepository).GetSshUrl()).ToDataRes(types.String)
	},
	"github.repository.homepage": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubRepository).GetHomepage()).ToDataRes(types.String)
	},
	"github.repository.topics": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubRepository).GetTopics()).ToDataRes(types.Array(types.String))
	},
	"github.repository.language": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubRepository).GetLanguage()).ToDataRes(types.String)
	},
	"github.repository.watchersCount": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubRepository).GetWatchersCount()).ToDataRes(types.Int)
	},
	"github.repository.forksCount": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubRepository).GetForksCount()).ToDataRes(types.Int)
	},
	"github.repository.stargazersCount": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubRepository).GetStargazersCount()).ToDataRes(types.Int)
	},
	"github.repository.openIssuesCount": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubRepository).GetOpenIssuesCount()).ToDataRes(types.Int)
	},
	"github.repository.createdAt": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubRepository).GetCreatedAt()).ToDataRes(types.Time)
	},
	"github.repository.updatedAt": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubRepository).GetUpdatedAt()).ToDataRes(types.Time)
	},
	"github.repository.pushedAt": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubRepository).GetPushedAt()).ToDataRes(types.Time)
	},
	"github.repository.archived": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubRepository).GetArchived()).ToDataRes(types.Bool)
	},
	"github.repository.disabled": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubRepository).GetDisabled()).ToDataRes(types.Bool)
	},
	"github.repository.private": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubRepository).GetPrivate()).ToDataRes(types.Bool)
	},
	"github.repository.isFork": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubRepository).GetIsFork()).ToDataRes(types.Bool)
	},
	"github.repository.visibility": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubRepository).GetVisibility()).ToDataRes(types.String)
	},
	"github.repository.allowAutoMerge": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubRepository).GetAllowAutoMerge()).ToDataRes(types.Bool)
	},
	"github.repository.allowForking": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubRepository).GetAllowForking()).ToDataRes(types.Bool)
	},
	"github.repository.allowMergeCommit": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubRepository).GetAllowMergeCommit()).ToDataRes(types.Bool)
	},
	"github.repository.allowRebaseMerge": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubRepository).GetAllowRebaseMerge()).ToDataRes(types.Bool)
	},
	"github.repository.allowSquashMerge": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubRepository).GetAllowSquashMerge()).ToDataRes(types.Bool)
	},
	"github.repository.hasIssues": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubRepository).GetHasIssues()).ToDataRes(types.Bool)
	},
	"github.repository.hasProjects": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubRepository).GetHasProjects()).ToDataRes(types.Bool)
	},
	"github.repository.hasWiki": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubRepository).GetHasWiki()).ToDataRes(types.Bool)
	},
	"github.repository.hasPages": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubRepository).GetHasPages()).ToDataRes(types.Bool)
	},
	"github.repository.hasDownloads": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubRepository).GetHasDownloads()).ToDataRes(types.Bool)
	},
	"github.repository.hasDiscussions": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubRepository).GetHasDiscussions()).ToDataRes(types.Bool)
	},
	"github.repository.isTemplate": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubRepository).GetIsTemplate()).ToDataRes(types.Bool)
	},
	"github.repository.customProperties": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubRepository).GetCustomProperties()).ToDataRes(types.Dict)
	},
	"github.repository.openMergeRequests": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubRepository).GetOpenMergeRequests()).ToDataRes(types.Array(types.Resource("github.mergeRequest")))
	},
	"github.repository.closedMergeRequests": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubRepository).GetClosedMergeRequests()).ToDataRes(types.Array(types.Resource("github.mergeRequest")))
	},
	"github.repository.allMergeRequests": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubRepository).GetAllMergeRequests()).ToDataRes(types.Array(types.Resource("github.mergeRequest")))
	},
	"github.repository.branches": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubRepository).GetBranches()).ToDataRes(types.Array(types.Resource("github.branch")))
	},
	"github.repository.defaultBranchName": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubRepository).GetDefaultBranchName()).ToDataRes(types.String)
	},
	"github.repository.defaultBranch": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubRepository).GetDefaultBranch()).ToDataRes(types.Resource("github.branch"))
	},
	"github.repository.commits": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubRepository).GetCommits()).ToDataRes(types.Array(types.Resource("github.commit")))
	},
	"github.repository.contributors": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubRepository).GetContributors()).ToDataRes(types.Array(types.Resource("github.user")))
	},
	"github.repository.collaborators": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubRepository).GetCollaborators()).ToDataRes(types.Array(types.Resource("github.collaborator")))
	},
	"github.repository.adminCollaborators": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubRepository).GetAdminCollaborators()).ToDataRes(types.Array(types.Resource("github.collaborator")))
	},
	"github.repository.files": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubRepository).GetFiles()).ToDataRes(types.Array(types.Resource("github.file")))
	},
	"github.repository.releases": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubRepository).GetReleases()).ToDataRes(types.Array(types.Resource("github.release")))
	},
	"github.repository.owner": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubRepository).GetOwner()).ToDataRes(types.Resource("github.user"))
	},
	"github.repository.webhooks": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubRepository).GetWebhooks()).ToDataRes(types.Array(types.Resource("github.webhook")))
	},
	"github.repository.workflows": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubRepository).GetWorkflows()).ToDataRes(types.Array(types.Resource("github.workflow")))
	},
	"github.repository.forks": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubRepository).GetForks()).ToDataRes(types.Array(types.Resource("github.repository")))
	},
	"github.repository.stargazers": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubRepository).GetStargazers()).ToDataRes(types.Array(types.Resource("github.user")))
	},
	"github.repository.openIssues": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubRepository).GetOpenIssues()).ToDataRes(types.Array(types.Resource("github.issue")))
	},
	"github.repository.closedIssues": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubRepository).GetClosedIssues()).ToDataRes(types.Array(types.Resource("github.issue")))
	},
	"github.repository.license": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubRepository).GetLicense()).ToDataRes(types.Resource("github.license"))
	},
	"github.repository.codeOfConductFile": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubRepository).GetCodeOfConductFile()).ToDataRes(types.Resource("github.file"))
	},
	"github.repository.supportFile": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubRepository).GetSupportFile()).ToDataRes(types.Resource("github.file"))
	},
	"github.repository.securityFile": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubRepository).GetSecurityFile()).ToDataRes(types.Resource("github.file"))
	},
	"github.license.key": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubLicense).GetKey()).ToDataRes(types.String)
	},
	"github.license.name": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubLicense).GetName()).ToDataRes(types.String)
	},
	"github.license.url": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubLicense).GetUrl()).ToDataRes(types.String)
	},
	"github.license.spdxId": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubLicense).GetSpdxId()).ToDataRes(types.String)
	},
	"github.file.path": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubFile).GetPath()).ToDataRes(types.String)
	},
	"github.file.name": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubFile).GetName()).ToDataRes(types.String)
	},
	"github.file.type": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubFile).GetType()).ToDataRes(types.String)
	},
	"github.file.sha": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubFile).GetSha()).ToDataRes(types.String)
	},
	"github.file.isBinary": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubFile).GetIsBinary()).ToDataRes(types.Bool)
	},
	"github.file.files": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubFile).GetFiles()).ToDataRes(types.Array(types.Resource("github.file")))
	},
	"github.file.ownerName": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubFile).GetOwnerName()).ToDataRes(types.String)
	},
	"github.file.repoName": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubFile).GetRepoName()).ToDataRes(types.String)
	},
	"github.file.content": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubFile).GetContent()).ToDataRes(types.String)
	},
	"github.file.downloadUrl": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubFile).GetDownloadUrl()).ToDataRes(types.String)
	},
	"github.file.exists": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubFile).GetExists()).ToDataRes(types.Bool)
	},
	"github.release.url": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubRelease).GetUrl()).ToDataRes(types.String)
	},
	"github.release.name": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubRelease).GetName()).ToDataRes(types.String)
	},
	"github.release.tagName": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubRelease).GetTagName()).ToDataRes(types.String)
	},
	"github.release.preRelease": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubRelease).GetPreRelease()).ToDataRes(types.Bool)
	},
	"github.webhook.id": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubWebhook).GetId()).ToDataRes(types.Int)
	},
	"github.webhook.name": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubWebhook).GetName()).ToDataRes(types.String)
	},
	"github.webhook.url": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubWebhook).GetUrl()).ToDataRes(types.String)
	},
	"github.webhook.events": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubWebhook).GetEvents()).ToDataRes(types.Array(types.String))
	},
	"github.webhook.config": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubWebhook).GetConfig()).ToDataRes(types.Dict)
	},
	"github.webhook.active": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubWebhook).GetActive()).ToDataRes(types.Bool)
	},
	"github.workflow.id": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubWorkflow).GetId()).ToDataRes(types.Int)
	},
	"github.workflow.name": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubWorkflow).GetName()).ToDataRes(types.String)
	},
	"github.workflow.path": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubWorkflow).GetPath()).ToDataRes(types.String)
	},
	"github.workflow.state": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubWorkflow).GetState()).ToDataRes(types.String)
	},
	"github.workflow.createdAt": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubWorkflow).GetCreatedAt()).ToDataRes(types.Time)
	},
	"github.workflow.updatedAt": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubWorkflow).GetUpdatedAt()).ToDataRes(types.Time)
	},
	"github.workflow.file": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubWorkflow).GetFile()).ToDataRes(types.Resource("github.file"))
	},
	"github.workflow.configuration": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubWorkflow).GetConfiguration()).ToDataRes(types.Dict)
	},
	"github.branch.name": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubBranch).GetName()).ToDataRes(types.String)
	},
	"github.branch.isProtected": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubBranch).GetIsProtected()).ToDataRes(types.Bool)
	},
	"github.branch.headCommit": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubBranch).GetHeadCommit()).ToDataRes(types.Resource("github.commit"))
	},
	"github.branch.headCommitSha": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubBranch).GetHeadCommitSha()).ToDataRes(types.String)
	},
	"github.branch.protectionRules": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubBranch).GetProtectionRules()).ToDataRes(types.Resource("github.branchprotection"))
	},
	"github.branch.repoName": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubBranch).GetRepoName()).ToDataRes(types.String)
	},
	"github.branch.owner": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubBranch).GetOwner()).ToDataRes(types.Resource("github.user"))
	},
	"github.branch.isDefault": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubBranch).GetIsDefault()).ToDataRes(types.Bool)
	},
	"github.branchprotection.id": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubBranchprotection).GetId()).ToDataRes(types.String)
	},
	"github.branchprotection.requiredStatusChecks": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubBranchprotection).GetRequiredStatusChecks()).ToDataRes(types.Dict)
	},
	"github.branchprotection.requiredPullRequestReviews": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubBranchprotection).GetRequiredPullRequestReviews()).ToDataRes(types.Dict)
	},
	"github.branchprotection.requiredConversationResolution": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubBranchprotection).GetRequiredConversationResolution()).ToDataRes(types.Dict)
	},
	"github.branchprotection.requiredSignatures": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubBranchprotection).GetRequiredSignatures()).ToDataRes(types.Bool)
	},
	"github.branchprotection.requireLinearHistory": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubBranchprotection).GetRequireLinearHistory()).ToDataRes(types.Dict)
	},
	"github.branchprotection.enforceAdmins": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubBranchprotection).GetEnforceAdmins()).ToDataRes(types.Dict)
	},
	"github.branchprotection.restrictions": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubBranchprotection).GetRestrictions()).ToDataRes(types.Dict)
	},
	"github.branchprotection.allowForcePushes": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubBranchprotection).GetAllowForcePushes()).ToDataRes(types.Dict)
	},
	"github.branchprotection.allowDeletions": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubBranchprotection).GetAllowDeletions()).ToDataRes(types.Dict)
	},
	"github.commit.owner": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubCommit).GetOwner()).ToDataRes(types.String)
	},
	"github.commit.repository": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubCommit).GetRepository()).ToDataRes(types.String)
	},
	"github.commit.sha": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubCommit).GetSha()).ToDataRes(types.String)
	},
	"github.commit.url": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubCommit).GetUrl()).ToDataRes(types.String)
	},
	"github.commit.author": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubCommit).GetAuthor()).ToDataRes(types.Resource("github.user"))
	},
	"github.commit.committer": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubCommit).GetCommitter()).ToDataRes(types.Resource("github.user"))
	},
	"github.commit.commit": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubCommit).GetCommit()).ToDataRes(types.Resource("git.commit"))
	},
	"github.commit.stats": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubCommit).GetStats()).ToDataRes(types.Dict)
	},
	"github.commit.authoredDate": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubCommit).GetAuthoredDate()).ToDataRes(types.Time)
	},
	"github.commit.committedDate": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubCommit).GetCommittedDate()).ToDataRes(types.Time)
	},
	"github.mergeRequest.id": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubMergeRequest).GetId()).ToDataRes(types.Int)
	},
	"github.mergeRequest.number": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubMergeRequest).GetNumber()).ToDataRes(types.Int)
	},
	"github.mergeRequest.state": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubMergeRequest).GetState()).ToDataRes(types.String)
	},
	"github.mergeRequest.createdAt": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubMergeRequest).GetCreatedAt()).ToDataRes(types.Time)
	},
	"github.mergeRequest.labels": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubMergeRequest).GetLabels()).ToDataRes(types.Array(types.Dict))
	},
	"github.mergeRequest.title": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubMergeRequest).GetTitle()).ToDataRes(types.String)
	},
	"github.mergeRequest.owner": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubMergeRequest).GetOwner()).ToDataRes(types.Resource("github.user"))
	},
	"github.mergeRequest.assignees": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubMergeRequest).GetAssignees()).ToDataRes(types.Array(types.Resource("github.user")))
	},
	"github.mergeRequest.commits": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubMergeRequest).GetCommits()).ToDataRes(types.Array(types.Resource("github.commit")))
	},
	"github.mergeRequest.reviews": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubMergeRequest).GetReviews()).ToDataRes(types.Array(types.Resource("github.review")))
	},
	"github.mergeRequest.repoName": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubMergeRequest).GetRepoName()).ToDataRes(types.String)
	},
	"github.review.url": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubReview).GetUrl()).ToDataRes(types.String)
	},
	"github.review.state": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubReview).GetState()).ToDataRes(types.String)
	},
	"github.review.authorAssociation": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubReview).GetAuthorAssociation()).ToDataRes(types.String)
	},
	"github.review.user": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubReview).GetUser()).ToDataRes(types.Resource("github.user"))
	},
	"github.installation.id": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubInstallation).GetId()).ToDataRes(types.Int)
	},
	"github.installation.appId": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubInstallation).GetAppId()).ToDataRes(types.Int)
	},
	"github.installation.appSlug": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubInstallation).GetAppSlug()).ToDataRes(types.String)
	},
	"github.installation.createdAt": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubInstallation).GetCreatedAt()).ToDataRes(types.Time)
	},
	"github.installation.updatedAt": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubInstallation).GetUpdatedAt()).ToDataRes(types.Time)
	},
	"github.gist.id": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubGist).GetId()).ToDataRes(types.String)
	},
	"github.gist.description": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubGist).GetDescription()).ToDataRes(types.String)
	},
	"github.gist.createdAt": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubGist).GetCreatedAt()).ToDataRes(types.Time)
	},
	"github.gist.updatedAt": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubGist).GetUpdatedAt()).ToDataRes(types.Time)
	},
	"github.gist.owner": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubGist).GetOwner()).ToDataRes(types.Resource("github.user"))
	},
	"github.gist.public": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubGist).GetPublic()).ToDataRes(types.Bool)
	},
	"github.gist.files": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubGist).GetFiles()).ToDataRes(types.Array(types.Resource("github.gistfile")))
	},
	"github.gistfile.gistId": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubGistfile).GetGistId()).ToDataRes(types.String)
	},
	"github.gistfile.filename": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubGistfile).GetFilename()).ToDataRes(types.String)
	},
	"github.gistfile.type": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubGistfile).GetType()).ToDataRes(types.String)
	},
	"github.gistfile.language": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubGistfile).GetLanguage()).ToDataRes(types.String)
	},
	"github.gistfile.rawUrl": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubGistfile).GetRawUrl()).ToDataRes(types.String)
	},
	"github.gistfile.size": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubGistfile).GetSize()).ToDataRes(types.Int)
	},
	"github.gistfile.content": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubGistfile).GetContent()).ToDataRes(types.String)
	},
	"github.issue.id": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubIssue).GetId()).ToDataRes(types.Int)
	},
	"github.issue.number": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubIssue).GetNumber()).ToDataRes(types.Int)
	},
	"github.issue.title": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubIssue).GetTitle()).ToDataRes(types.String)
	},
	"github.issue.state": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubIssue).GetState()).ToDataRes(types.String)
	},
	"github.issue.body": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubIssue).GetBody()).ToDataRes(types.String)
	},
	"github.issue.url": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubIssue).GetUrl()).ToDataRes(types.String)
	},
	"github.issue.createdAt": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubIssue).GetCreatedAt()).ToDataRes(types.Time)
	},
	"github.issue.updatedAt": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubIssue).GetUpdatedAt()).ToDataRes(types.Time)
	},
	"github.issue.closedAt": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubIssue).GetClosedAt()).ToDataRes(types.Time)
	},
	"github.issue.assignees": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubIssue).GetAssignees()).ToDataRes(types.Array(types.Resource("github.user")))
	},
	"github.issue.closedBy": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGithubIssue).GetClosedBy()).ToDataRes(types.Resource("github.user"))
	},
}

func GetData(resource plugin.Resource, field string, args map[string]*llx.RawData) *plugin.DataRes {
	f, ok := getDataFields[resource.MqlName()+"."+field]
	if !ok {
		return &plugin.DataRes{Error: "cannot find '" + field + "' in resource '" + resource.MqlName() + "'"}
	}

	return f(resource)
}

var setDataFields = map[string]func(r plugin.Resource, v *llx.RawData) bool {
	"github.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlGithub).__id, ok = v.Value.(string)
			return
		},
	"git.commit.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlGitCommit).__id, ok = v.Value.(string)
			return
		},
	"git.commit.sha": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGitCommit).Sha, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"git.commit.message": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGitCommit).Message, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"git.commit.author": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGitCommit).Author, ok = plugin.RawToTValue[*mqlGitCommitAuthor](v.Value, v.Error)
		return
	},
	"git.commit.committer": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGitCommit).Committer, ok = plugin.RawToTValue[*mqlGitCommitAuthor](v.Value, v.Error)
		return
	},
	"git.commit.signatureVerification": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGitCommit).SignatureVerification, ok = plugin.RawToTValue[*mqlGitGpgSignature](v.Value, v.Error)
		return
	},
	"git.commitAuthor.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlGitCommitAuthor).__id, ok = v.Value.(string)
			return
		},
	"git.commitAuthor.sha": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGitCommitAuthor).Sha, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"git.commitAuthor.name": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGitCommitAuthor).Name, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"git.commitAuthor.email": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGitCommitAuthor).Email, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"git.commitAuthor.date": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGitCommitAuthor).Date, ok = plugin.RawToTValue[*time.Time](v.Value, v.Error)
		return
	},
	"git.gpgSignature.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlGitGpgSignature).__id, ok = v.Value.(string)
			return
		},
	"git.gpgSignature.sha": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGitGpgSignature).Sha, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"git.gpgSignature.reason": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGitGpgSignature).Reason, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"git.gpgSignature.verified": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGitGpgSignature).Verified, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"git.gpgSignature.payload": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGitGpgSignature).Payload, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"git.gpgSignature.signature": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGitGpgSignature).Signature, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.organization.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlGithubOrganization).__id, ok = v.Value.(string)
			return
		},
	"github.organization.login": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubOrganization).Login, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.organization.id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubOrganization).Id, ok = plugin.RawToTValue[int64](v.Value, v.Error)
		return
	},
	"github.organization.nodeId": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubOrganization).NodeId, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.organization.name": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubOrganization).Name, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.organization.company": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubOrganization).Company, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.organization.blog": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubOrganization).Blog, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.organization.location": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubOrganization).Location, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.organization.email": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubOrganization).Email, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.organization.twitterUsername": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubOrganization).TwitterUsername, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.organization.avatarUrl": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubOrganization).AvatarUrl, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.organization.followers": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubOrganization).Followers, ok = plugin.RawToTValue[int64](v.Value, v.Error)
		return
	},
	"github.organization.following": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubOrganization).Following, ok = plugin.RawToTValue[int64](v.Value, v.Error)
		return
	},
	"github.organization.description": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubOrganization).Description, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.organization.createdAt": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubOrganization).CreatedAt, ok = plugin.RawToTValue[*time.Time](v.Value, v.Error)
		return
	},
	"github.organization.updatedAt": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubOrganization).UpdatedAt, ok = plugin.RawToTValue[*time.Time](v.Value, v.Error)
		return
	},
	"github.organization.totalPrivateRepos": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubOrganization).TotalPrivateRepos, ok = plugin.RawToTValue[int64](v.Value, v.Error)
		return
	},
	"github.organization.totalPublicRepos": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubOrganization).TotalPublicRepos, ok = plugin.RawToTValue[int64](v.Value, v.Error)
		return
	},
	"github.organization.ownedPrivateRepos": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubOrganization).OwnedPrivateRepos, ok = plugin.RawToTValue[int64](v.Value, v.Error)
		return
	},
	"github.organization.privateGists": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubOrganization).PrivateGists, ok = plugin.RawToTValue[int64](v.Value, v.Error)
		return
	},
	"github.organization.diskUsage": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubOrganization).DiskUsage, ok = plugin.RawToTValue[int64](v.Value, v.Error)
		return
	},
	"github.organization.collaborators": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubOrganization).Collaborators, ok = plugin.RawToTValue[int64](v.Value, v.Error)
		return
	},
	"github.organization.billingEmail": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubOrganization).BillingEmail, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.organization.plan": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubOrganization).Plan, ok = plugin.RawToTValue[interface{}](v.Value, v.Error)
		return
	},
	"github.organization.twoFactorRequirementEnabled": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubOrganization).TwoFactorRequirementEnabled, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"github.organization.isVerified": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubOrganization).IsVerified, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"github.organization.defaultRepositoryPermission": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubOrganization).DefaultRepositoryPermission, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.organization.membersCanCreateRepositories": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubOrganization).MembersCanCreateRepositories, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"github.organization.membersCanCreatePublicRepositories": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubOrganization).MembersCanCreatePublicRepositories, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"github.organization.membersCanCreatePrivateRepositories": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubOrganization).MembersCanCreatePrivateRepositories, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"github.organization.membersCanCreateInternalRepositories": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubOrganization).MembersCanCreateInternalRepositories, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"github.organization.membersCanCreatePages": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubOrganization).MembersCanCreatePages, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"github.organization.membersCanCreatePublicPages": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubOrganization).MembersCanCreatePublicPages, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"github.organization.membersCanCreatePrivatePages": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubOrganization).MembersCanCreatePrivatePages, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"github.organization.membersCanForkPrivateRepos": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubOrganization).MembersCanForkPrivateRepos, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"github.organization.owners": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubOrganization).Owners, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"github.organization.members": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubOrganization).Members, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"github.organization.teams": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubOrganization).Teams, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"github.organization.repositories": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubOrganization).Repositories, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"github.organization.installations": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubOrganization).Installations, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"github.organization.webhooks": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubOrganization).Webhooks, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"github.organization.packages": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubOrganization).Packages, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"github.organization.hasOrganizationProjects": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubOrganization).HasOrganizationProjects, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"github.organization.hasRepositoryProjects": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubOrganization).HasRepositoryProjects, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"github.organization.customProperties": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubOrganization).CustomProperties, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"github.organization.customProperty.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlGithubOrganizationCustomProperty).__id, ok = v.Value.(string)
			return
		},
	"github.organization.customProperty.name": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubOrganizationCustomProperty).Name, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.organization.customProperty.description": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubOrganizationCustomProperty).Description, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.organization.customProperty.sourceType": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubOrganizationCustomProperty).SourceType, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.organization.customProperty.valueType": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubOrganizationCustomProperty).ValueType, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.organization.customProperty.required": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubOrganizationCustomProperty).Required, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"github.organization.customProperty.defaultValue": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubOrganizationCustomProperty).DefaultValue, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.organization.customProperty.allowedValues": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubOrganizationCustomProperty).AllowedValues, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"github.organization.customProperty.valuesEditableBy": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubOrganizationCustomProperty).ValuesEditableBy, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.user.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlGithubUser).__id, ok = v.Value.(string)
			return
		},
	"github.user.id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubUser).Id, ok = plugin.RawToTValue[int64](v.Value, v.Error)
		return
	},
	"github.user.login": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubUser).Login, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.user.name": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubUser).Name, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.user.email": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubUser).Email, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.user.bio": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubUser).Bio, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.user.blog": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubUser).Blog, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.user.location": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubUser).Location, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.user.avatarUrl": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubUser).AvatarUrl, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.user.followers": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubUser).Followers, ok = plugin.RawToTValue[int64](v.Value, v.Error)
		return
	},
	"github.user.following": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubUser).Following, ok = plugin.RawToTValue[int64](v.Value, v.Error)
		return
	},
	"github.user.twitterUsername": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubUser).TwitterUsername, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.user.createdAt": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubUser).CreatedAt, ok = plugin.RawToTValue[*time.Time](v.Value, v.Error)
		return
	},
	"github.user.updatedAt": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubUser).UpdatedAt, ok = plugin.RawToTValue[*time.Time](v.Value, v.Error)
		return
	},
	"github.user.suspendedAt": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubUser).SuspendedAt, ok = plugin.RawToTValue[*time.Time](v.Value, v.Error)
		return
	},
	"github.user.company": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubUser).Company, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.user.repositories": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubUser).Repositories, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"github.user.gists": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubUser).Gists, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"github.team.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlGithubTeam).__id, ok = v.Value.(string)
			return
		},
	"github.team.id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubTeam).Id, ok = plugin.RawToTValue[int64](v.Value, v.Error)
		return
	},
	"github.team.name": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubTeam).Name, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.team.description": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubTeam).Description, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.team.slug": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubTeam).Slug, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.team.privacy": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubTeam).Privacy, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.team.defaultPermission": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubTeam).DefaultPermission, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.team.members": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubTeam).Members, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"github.team.repositories": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubTeam).Repositories, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"github.team.organization": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubTeam).Organization, ok = plugin.RawToTValue[*mqlGithubOrganization](v.Value, v.Error)
		return
	},
	"github.collaborator.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlGithubCollaborator).__id, ok = v.Value.(string)
			return
		},
	"github.collaborator.id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubCollaborator).Id, ok = plugin.RawToTValue[int64](v.Value, v.Error)
		return
	},
	"github.collaborator.user": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubCollaborator).User, ok = plugin.RawToTValue[*mqlGithubUser](v.Value, v.Error)
		return
	},
	"github.collaborator.permissions": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubCollaborator).Permissions, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"github.package.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlGithubPackage).__id, ok = v.Value.(string)
			return
		},
	"github.package.id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubPackage).Id, ok = plugin.RawToTValue[int64](v.Value, v.Error)
		return
	},
	"github.package.name": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubPackage).Name, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.package.packageType": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubPackage).PackageType, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.package.owner": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubPackage).Owner, ok = plugin.RawToTValue[*mqlGithubUser](v.Value, v.Error)
		return
	},
	"github.package.createdAt": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubPackage).CreatedAt, ok = plugin.RawToTValue[*time.Time](v.Value, v.Error)
		return
	},
	"github.package.updatedAt": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubPackage).UpdatedAt, ok = plugin.RawToTValue[*time.Time](v.Value, v.Error)
		return
	},
	"github.package.versionCount": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubPackage).VersionCount, ok = plugin.RawToTValue[int64](v.Value, v.Error)
		return
	},
	"github.package.visibility": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubPackage).Visibility, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.package.repository": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubPackage).Repository, ok = plugin.RawToTValue[*mqlGithubRepository](v.Value, v.Error)
		return
	},
	"github.packages.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlGithubPackages).__id, ok = v.Value.(string)
			return
		},
	"github.packages.public": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubPackages).Public, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"github.packages.private": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubPackages).Private, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"github.packages.internal": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubPackages).Internal, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"github.packages.list": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubPackages).List, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"github.repository.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlGithubRepository).__id, ok = v.Value.(string)
			return
		},
	"github.repository.id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubRepository).Id, ok = plugin.RawToTValue[int64](v.Value, v.Error)
		return
	},
	"github.repository.name": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubRepository).Name, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.repository.fullName": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubRepository).FullName, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.repository.description": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubRepository).Description, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.repository.cloneUrl": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubRepository).CloneUrl, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.repository.sshUrl": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubRepository).SshUrl, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.repository.homepage": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubRepository).Homepage, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.repository.topics": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubRepository).Topics, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"github.repository.language": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubRepository).Language, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.repository.watchersCount": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubRepository).WatchersCount, ok = plugin.RawToTValue[int64](v.Value, v.Error)
		return
	},
	"github.repository.forksCount": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubRepository).ForksCount, ok = plugin.RawToTValue[int64](v.Value, v.Error)
		return
	},
	"github.repository.stargazersCount": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubRepository).StargazersCount, ok = plugin.RawToTValue[int64](v.Value, v.Error)
		return
	},
	"github.repository.openIssuesCount": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubRepository).OpenIssuesCount, ok = plugin.RawToTValue[int64](v.Value, v.Error)
		return
	},
	"github.repository.createdAt": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubRepository).CreatedAt, ok = plugin.RawToTValue[*time.Time](v.Value, v.Error)
		return
	},
	"github.repository.updatedAt": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubRepository).UpdatedAt, ok = plugin.RawToTValue[*time.Time](v.Value, v.Error)
		return
	},
	"github.repository.pushedAt": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubRepository).PushedAt, ok = plugin.RawToTValue[*time.Time](v.Value, v.Error)
		return
	},
	"github.repository.archived": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubRepository).Archived, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"github.repository.disabled": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubRepository).Disabled, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"github.repository.private": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubRepository).Private, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"github.repository.isFork": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubRepository).IsFork, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"github.repository.visibility": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubRepository).Visibility, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.repository.allowAutoMerge": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubRepository).AllowAutoMerge, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"github.repository.allowForking": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubRepository).AllowForking, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"github.repository.allowMergeCommit": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubRepository).AllowMergeCommit, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"github.repository.allowRebaseMerge": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubRepository).AllowRebaseMerge, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"github.repository.allowSquashMerge": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubRepository).AllowSquashMerge, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"github.repository.hasIssues": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubRepository).HasIssues, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"github.repository.hasProjects": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubRepository).HasProjects, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"github.repository.hasWiki": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubRepository).HasWiki, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"github.repository.hasPages": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubRepository).HasPages, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"github.repository.hasDownloads": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubRepository).HasDownloads, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"github.repository.hasDiscussions": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubRepository).HasDiscussions, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"github.repository.isTemplate": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubRepository).IsTemplate, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"github.repository.customProperties": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubRepository).CustomProperties, ok = plugin.RawToTValue[interface{}](v.Value, v.Error)
		return
	},
	"github.repository.openMergeRequests": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubRepository).OpenMergeRequests, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"github.repository.closedMergeRequests": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubRepository).ClosedMergeRequests, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"github.repository.allMergeRequests": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubRepository).AllMergeRequests, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"github.repository.branches": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubRepository).Branches, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"github.repository.defaultBranchName": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubRepository).DefaultBranchName, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.repository.defaultBranch": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubRepository).DefaultBranch, ok = plugin.RawToTValue[*mqlGithubBranch](v.Value, v.Error)
		return
	},
	"github.repository.commits": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubRepository).Commits, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"github.repository.contributors": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubRepository).Contributors, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"github.repository.collaborators": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubRepository).Collaborators, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"github.repository.adminCollaborators": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubRepository).AdminCollaborators, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"github.repository.files": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubRepository).Files, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"github.repository.releases": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubRepository).Releases, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"github.repository.owner": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubRepository).Owner, ok = plugin.RawToTValue[*mqlGithubUser](v.Value, v.Error)
		return
	},
	"github.repository.webhooks": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubRepository).Webhooks, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"github.repository.workflows": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubRepository).Workflows, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"github.repository.forks": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubRepository).Forks, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"github.repository.stargazers": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubRepository).Stargazers, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"github.repository.openIssues": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubRepository).OpenIssues, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"github.repository.closedIssues": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubRepository).ClosedIssues, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"github.repository.license": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubRepository).License, ok = plugin.RawToTValue[*mqlGithubLicense](v.Value, v.Error)
		return
	},
	"github.repository.codeOfConductFile": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubRepository).CodeOfConductFile, ok = plugin.RawToTValue[*mqlGithubFile](v.Value, v.Error)
		return
	},
	"github.repository.supportFile": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubRepository).SupportFile, ok = plugin.RawToTValue[*mqlGithubFile](v.Value, v.Error)
		return
	},
	"github.repository.securityFile": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubRepository).SecurityFile, ok = plugin.RawToTValue[*mqlGithubFile](v.Value, v.Error)
		return
	},
	"github.license.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlGithubLicense).__id, ok = v.Value.(string)
			return
		},
	"github.license.key": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubLicense).Key, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.license.name": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubLicense).Name, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.license.url": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubLicense).Url, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.license.spdxId": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubLicense).SpdxId, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.file.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlGithubFile).__id, ok = v.Value.(string)
			return
		},
	"github.file.path": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubFile).Path, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.file.name": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubFile).Name, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.file.type": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubFile).Type, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.file.sha": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubFile).Sha, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.file.isBinary": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubFile).IsBinary, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"github.file.files": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubFile).Files, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"github.file.ownerName": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubFile).OwnerName, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.file.repoName": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubFile).RepoName, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.file.content": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubFile).Content, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.file.downloadUrl": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubFile).DownloadUrl, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.file.exists": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubFile).Exists, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"github.release.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlGithubRelease).__id, ok = v.Value.(string)
			return
		},
	"github.release.url": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubRelease).Url, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.release.name": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubRelease).Name, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.release.tagName": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubRelease).TagName, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.release.preRelease": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubRelease).PreRelease, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"github.webhook.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlGithubWebhook).__id, ok = v.Value.(string)
			return
		},
	"github.webhook.id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubWebhook).Id, ok = plugin.RawToTValue[int64](v.Value, v.Error)
		return
	},
	"github.webhook.name": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubWebhook).Name, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.webhook.url": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubWebhook).Url, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.webhook.events": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubWebhook).Events, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"github.webhook.config": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubWebhook).Config, ok = plugin.RawToTValue[interface{}](v.Value, v.Error)
		return
	},
	"github.webhook.active": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubWebhook).Active, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"github.workflow.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlGithubWorkflow).__id, ok = v.Value.(string)
			return
		},
	"github.workflow.id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubWorkflow).Id, ok = plugin.RawToTValue[int64](v.Value, v.Error)
		return
	},
	"github.workflow.name": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubWorkflow).Name, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.workflow.path": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubWorkflow).Path, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.workflow.state": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubWorkflow).State, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.workflow.createdAt": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubWorkflow).CreatedAt, ok = plugin.RawToTValue[*time.Time](v.Value, v.Error)
		return
	},
	"github.workflow.updatedAt": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubWorkflow).UpdatedAt, ok = plugin.RawToTValue[*time.Time](v.Value, v.Error)
		return
	},
	"github.workflow.file": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubWorkflow).File, ok = plugin.RawToTValue[*mqlGithubFile](v.Value, v.Error)
		return
	},
	"github.workflow.configuration": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubWorkflow).Configuration, ok = plugin.RawToTValue[interface{}](v.Value, v.Error)
		return
	},
	"github.branch.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlGithubBranch).__id, ok = v.Value.(string)
			return
		},
	"github.branch.name": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubBranch).Name, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.branch.isProtected": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubBranch).IsProtected, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"github.branch.headCommit": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubBranch).HeadCommit, ok = plugin.RawToTValue[*mqlGithubCommit](v.Value, v.Error)
		return
	},
	"github.branch.headCommitSha": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubBranch).HeadCommitSha, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.branch.protectionRules": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubBranch).ProtectionRules, ok = plugin.RawToTValue[*mqlGithubBranchprotection](v.Value, v.Error)
		return
	},
	"github.branch.repoName": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubBranch).RepoName, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.branch.owner": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubBranch).Owner, ok = plugin.RawToTValue[*mqlGithubUser](v.Value, v.Error)
		return
	},
	"github.branch.isDefault": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubBranch).IsDefault, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"github.branchprotection.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlGithubBranchprotection).__id, ok = v.Value.(string)
			return
		},
	"github.branchprotection.id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubBranchprotection).Id, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.branchprotection.requiredStatusChecks": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubBranchprotection).RequiredStatusChecks, ok = plugin.RawToTValue[interface{}](v.Value, v.Error)
		return
	},
	"github.branchprotection.requiredPullRequestReviews": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubBranchprotection).RequiredPullRequestReviews, ok = plugin.RawToTValue[interface{}](v.Value, v.Error)
		return
	},
	"github.branchprotection.requiredConversationResolution": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubBranchprotection).RequiredConversationResolution, ok = plugin.RawToTValue[interface{}](v.Value, v.Error)
		return
	},
	"github.branchprotection.requiredSignatures": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubBranchprotection).RequiredSignatures, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"github.branchprotection.requireLinearHistory": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubBranchprotection).RequireLinearHistory, ok = plugin.RawToTValue[interface{}](v.Value, v.Error)
		return
	},
	"github.branchprotection.enforceAdmins": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubBranchprotection).EnforceAdmins, ok = plugin.RawToTValue[interface{}](v.Value, v.Error)
		return
	},
	"github.branchprotection.restrictions": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubBranchprotection).Restrictions, ok = plugin.RawToTValue[interface{}](v.Value, v.Error)
		return
	},
	"github.branchprotection.allowForcePushes": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubBranchprotection).AllowForcePushes, ok = plugin.RawToTValue[interface{}](v.Value, v.Error)
		return
	},
	"github.branchprotection.allowDeletions": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubBranchprotection).AllowDeletions, ok = plugin.RawToTValue[interface{}](v.Value, v.Error)
		return
	},
	"github.commit.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlGithubCommit).__id, ok = v.Value.(string)
			return
		},
	"github.commit.owner": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubCommit).Owner, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.commit.repository": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubCommit).Repository, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.commit.sha": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubCommit).Sha, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.commit.url": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubCommit).Url, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.commit.author": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubCommit).Author, ok = plugin.RawToTValue[*mqlGithubUser](v.Value, v.Error)
		return
	},
	"github.commit.committer": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubCommit).Committer, ok = plugin.RawToTValue[*mqlGithubUser](v.Value, v.Error)
		return
	},
	"github.commit.commit": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubCommit).Commit, ok = plugin.RawToTValue[*mqlGitCommit](v.Value, v.Error)
		return
	},
	"github.commit.stats": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubCommit).Stats, ok = plugin.RawToTValue[interface{}](v.Value, v.Error)
		return
	},
	"github.commit.authoredDate": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubCommit).AuthoredDate, ok = plugin.RawToTValue[*time.Time](v.Value, v.Error)
		return
	},
	"github.commit.committedDate": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubCommit).CommittedDate, ok = plugin.RawToTValue[*time.Time](v.Value, v.Error)
		return
	},
	"github.mergeRequest.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlGithubMergeRequest).__id, ok = v.Value.(string)
			return
		},
	"github.mergeRequest.id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubMergeRequest).Id, ok = plugin.RawToTValue[int64](v.Value, v.Error)
		return
	},
	"github.mergeRequest.number": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubMergeRequest).Number, ok = plugin.RawToTValue[int64](v.Value, v.Error)
		return
	},
	"github.mergeRequest.state": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubMergeRequest).State, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.mergeRequest.createdAt": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubMergeRequest).CreatedAt, ok = plugin.RawToTValue[*time.Time](v.Value, v.Error)
		return
	},
	"github.mergeRequest.labels": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubMergeRequest).Labels, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"github.mergeRequest.title": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubMergeRequest).Title, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.mergeRequest.owner": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubMergeRequest).Owner, ok = plugin.RawToTValue[*mqlGithubUser](v.Value, v.Error)
		return
	},
	"github.mergeRequest.assignees": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubMergeRequest).Assignees, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"github.mergeRequest.commits": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubMergeRequest).Commits, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"github.mergeRequest.reviews": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubMergeRequest).Reviews, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"github.mergeRequest.repoName": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubMergeRequest).RepoName, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.review.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlGithubReview).__id, ok = v.Value.(string)
			return
		},
	"github.review.url": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubReview).Url, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.review.state": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubReview).State, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.review.authorAssociation": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubReview).AuthorAssociation, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.review.user": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubReview).User, ok = plugin.RawToTValue[*mqlGithubUser](v.Value, v.Error)
		return
	},
	"github.installation.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlGithubInstallation).__id, ok = v.Value.(string)
			return
		},
	"github.installation.id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubInstallation).Id, ok = plugin.RawToTValue[int64](v.Value, v.Error)
		return
	},
	"github.installation.appId": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubInstallation).AppId, ok = plugin.RawToTValue[int64](v.Value, v.Error)
		return
	},
	"github.installation.appSlug": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubInstallation).AppSlug, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.installation.createdAt": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubInstallation).CreatedAt, ok = plugin.RawToTValue[*time.Time](v.Value, v.Error)
		return
	},
	"github.installation.updatedAt": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubInstallation).UpdatedAt, ok = plugin.RawToTValue[*time.Time](v.Value, v.Error)
		return
	},
	"github.gist.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlGithubGist).__id, ok = v.Value.(string)
			return
		},
	"github.gist.id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubGist).Id, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.gist.description": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubGist).Description, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.gist.createdAt": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubGist).CreatedAt, ok = plugin.RawToTValue[*time.Time](v.Value, v.Error)
		return
	},
	"github.gist.updatedAt": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubGist).UpdatedAt, ok = plugin.RawToTValue[*time.Time](v.Value, v.Error)
		return
	},
	"github.gist.owner": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubGist).Owner, ok = plugin.RawToTValue[*mqlGithubUser](v.Value, v.Error)
		return
	},
	"github.gist.public": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubGist).Public, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"github.gist.files": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubGist).Files, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"github.gistfile.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlGithubGistfile).__id, ok = v.Value.(string)
			return
		},
	"github.gistfile.gistId": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubGistfile).GistId, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.gistfile.filename": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubGistfile).Filename, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.gistfile.type": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubGistfile).Type, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.gistfile.language": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubGistfile).Language, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.gistfile.rawUrl": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubGistfile).RawUrl, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.gistfile.size": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubGistfile).Size, ok = plugin.RawToTValue[int64](v.Value, v.Error)
		return
	},
	"github.gistfile.content": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubGistfile).Content, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.issue.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlGithubIssue).__id, ok = v.Value.(string)
			return
		},
	"github.issue.id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubIssue).Id, ok = plugin.RawToTValue[int64](v.Value, v.Error)
		return
	},
	"github.issue.number": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubIssue).Number, ok = plugin.RawToTValue[int64](v.Value, v.Error)
		return
	},
	"github.issue.title": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubIssue).Title, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.issue.state": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubIssue).State, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.issue.body": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubIssue).Body, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.issue.url": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubIssue).Url, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"github.issue.createdAt": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubIssue).CreatedAt, ok = plugin.RawToTValue[*time.Time](v.Value, v.Error)
		return
	},
	"github.issue.updatedAt": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubIssue).UpdatedAt, ok = plugin.RawToTValue[*time.Time](v.Value, v.Error)
		return
	},
	"github.issue.closedAt": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubIssue).ClosedAt, ok = plugin.RawToTValue[*time.Time](v.Value, v.Error)
		return
	},
	"github.issue.assignees": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubIssue).Assignees, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"github.issue.closedBy": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGithubIssue).ClosedBy, ok = plugin.RawToTValue[*mqlGithubUser](v.Value, v.Error)
		return
	},
}

func SetData(resource plugin.Resource, field string, val *llx.RawData) error {
	f, ok := setDataFields[resource.MqlName() + "." + field]
	if !ok {
		return errors.New("[github] cannot set '"+field+"' in resource '"+resource.MqlName()+"', field not found")
	}

	if ok := f(resource, val); !ok {
		return errors.New("[github] cannot set '"+field+"' in resource '"+resource.MqlName()+"', type does not match")
	}
	return nil
}

func SetAllData(resource plugin.Resource, args map[string]*llx.RawData) error {
	var err error
	for k, v := range args {
		if err = SetData(resource, k, v); err != nil {
			return err
		}
	}
	return nil
}

// mqlGithub for the github resource
type mqlGithub struct {
	MqlRuntime *plugin.Runtime
	__id string
	mqlGithubInternal
}

// createGithub creates a new instance of this resource
func createGithub(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlGithub{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	// to override __id implement: id() (string, error)

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("github", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlGithub) MqlName() string {
	return "github"
}

func (c *mqlGithub) MqlID() string {
	return c.__id
}

// mqlGitCommit for the git.commit resource
type mqlGitCommit struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlGitCommitInternal it will be used here
	Sha plugin.TValue[string]
	Message plugin.TValue[string]
	Author plugin.TValue[*mqlGitCommitAuthor]
	Committer plugin.TValue[*mqlGitCommitAuthor]
	SignatureVerification plugin.TValue[*mqlGitGpgSignature]
}

// createGitCommit creates a new instance of this resource
func createGitCommit(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlGitCommit{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	if res.__id == "" {
	res.__id, err = res.id()
		if err != nil {
			return nil, err
		}
	}

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("git.commit", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlGitCommit) MqlName() string {
	return "git.commit"
}

func (c *mqlGitCommit) MqlID() string {
	return c.__id
}

func (c *mqlGitCommit) GetSha() *plugin.TValue[string] {
	return &c.Sha
}

func (c *mqlGitCommit) GetMessage() *plugin.TValue[string] {
	return &c.Message
}

func (c *mqlGitCommit) GetAuthor() *plugin.TValue[*mqlGitCommitAuthor] {
	return &c.Author
}

func (c *mqlGitCommit) GetCommitter() *plugin.TValue[*mqlGitCommitAuthor] {
	return &c.Committer
}

func (c *mqlGitCommit) GetSignatureVerification() *plugin.TValue[*mqlGitGpgSignature] {
	return &c.SignatureVerification
}

// mqlGitCommitAuthor for the git.commitAuthor resource
type mqlGitCommitAuthor struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlGitCommitAuthorInternal it will be used here
	Sha plugin.TValue[string]
	Name plugin.TValue[string]
	Email plugin.TValue[string]
	Date plugin.TValue[*time.Time]
}

// createGitCommitAuthor creates a new instance of this resource
func createGitCommitAuthor(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlGitCommitAuthor{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	if res.__id == "" {
	res.__id, err = res.id()
		if err != nil {
			return nil, err
		}
	}

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("git.commitAuthor", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlGitCommitAuthor) MqlName() string {
	return "git.commitAuthor"
}

func (c *mqlGitCommitAuthor) MqlID() string {
	return c.__id
}

func (c *mqlGitCommitAuthor) GetSha() *plugin.TValue[string] {
	return &c.Sha
}

func (c *mqlGitCommitAuthor) GetName() *plugin.TValue[string] {
	return &c.Name
}

func (c *mqlGitCommitAuthor) GetEmail() *plugin.TValue[string] {
	return &c.Email
}

func (c *mqlGitCommitAuthor) GetDate() *plugin.TValue[*time.Time] {
	return &c.Date
}

// mqlGitGpgSignature for the git.gpgSignature resource
type mqlGitGpgSignature struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlGitGpgSignatureInternal it will be used here
	Sha plugin.TValue[string]
	Reason plugin.TValue[string]
	Verified plugin.TValue[bool]
	Payload plugin.TValue[string]
	Signature plugin.TValue[string]
}

// createGitGpgSignature creates a new instance of this resource
func createGitGpgSignature(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlGitGpgSignature{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	if res.__id == "" {
	res.__id, err = res.id()
		if err != nil {
			return nil, err
		}
	}

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("git.gpgSignature", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlGitGpgSignature) MqlName() string {
	return "git.gpgSignature"
}

func (c *mqlGitGpgSignature) MqlID() string {
	return c.__id
}

func (c *mqlGitGpgSignature) GetSha() *plugin.TValue[string] {
	return &c.Sha
}

func (c *mqlGitGpgSignature) GetReason() *plugin.TValue[string] {
	return &c.Reason
}

func (c *mqlGitGpgSignature) GetVerified() *plugin.TValue[bool] {
	return &c.Verified
}

func (c *mqlGitGpgSignature) GetPayload() *plugin.TValue[string] {
	return &c.Payload
}

func (c *mqlGitGpgSignature) GetSignature() *plugin.TValue[string] {
	return &c.Signature
}

// mqlGithubOrganization for the github.organization resource
type mqlGithubOrganization struct {
	MqlRuntime *plugin.Runtime
	__id string
	mqlGithubOrganizationInternal
	Login plugin.TValue[string]
	Id plugin.TValue[int64]
	NodeId plugin.TValue[string]
	Name plugin.TValue[string]
	Company plugin.TValue[string]
	Blog plugin.TValue[string]
	Location plugin.TValue[string]
	Email plugin.TValue[string]
	TwitterUsername plugin.TValue[string]
	AvatarUrl plugin.TValue[string]
	Followers plugin.TValue[int64]
	Following plugin.TValue[int64]
	Description plugin.TValue[string]
	CreatedAt plugin.TValue[*time.Time]
	UpdatedAt plugin.TValue[*time.Time]
	TotalPrivateRepos plugin.TValue[int64]
	TotalPublicRepos plugin.TValue[int64]
	OwnedPrivateRepos plugin.TValue[int64]
	PrivateGists plugin.TValue[int64]
	DiskUsage plugin.TValue[int64]
	Collaborators plugin.TValue[int64]
	BillingEmail plugin.TValue[string]
	Plan plugin.TValue[interface{}]
	TwoFactorRequirementEnabled plugin.TValue[bool]
	IsVerified plugin.TValue[bool]
	DefaultRepositoryPermission plugin.TValue[string]
	MembersCanCreateRepositories plugin.TValue[bool]
	MembersCanCreatePublicRepositories plugin.TValue[bool]
	MembersCanCreatePrivateRepositories plugin.TValue[bool]
	MembersCanCreateInternalRepositories plugin.TValue[bool]
	MembersCanCreatePages plugin.TValue[bool]
	MembersCanCreatePublicPages plugin.TValue[bool]
	MembersCanCreatePrivatePages plugin.TValue[bool]
	MembersCanForkPrivateRepos plugin.TValue[bool]
	Owners plugin.TValue[[]interface{}]
	Members plugin.TValue[[]interface{}]
	Teams plugin.TValue[[]interface{}]
	Repositories plugin.TValue[[]interface{}]
	Installations plugin.TValue[[]interface{}]
	Webhooks plugin.TValue[[]interface{}]
	Packages plugin.TValue[[]interface{}]
	HasOrganizationProjects plugin.TValue[bool]
	HasRepositoryProjects plugin.TValue[bool]
	CustomProperties plugin.TValue[[]interface{}]
}

// createGithubOrganization creates a new instance of this resource
func createGithubOrganization(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlGithubOrganization{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	if res.__id == "" {
	res.__id, err = res.id()
		if err != nil {
			return nil, err
		}
	}

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("github.organization", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlGithubOrganization) MqlName() string {
	return "github.organization"
}

func (c *mqlGithubOrganization) MqlID() string {
	return c.__id
}

func (c *mqlGithubOrganization) GetLogin() *plugin.TValue[string] {
	return &c.Login
}

func (c *mqlGithubOrganization) GetId() *plugin.TValue[int64] {
	return &c.Id
}

func (c *mqlGithubOrganization) GetNodeId() *plugin.TValue[string] {
	return &c.NodeId
}

func (c *mqlGithubOrganization) GetName() *plugin.TValue[string] {
	return &c.Name
}

func (c *mqlGithubOrganization) GetCompany() *plugin.TValue[string] {
	return &c.Company
}

func (c *mqlGithubOrganization) GetBlog() *plugin.TValue[string] {
	return &c.Blog
}

func (c *mqlGithubOrganization) GetLocation() *plugin.TValue[string] {
	return &c.Location
}

func (c *mqlGithubOrganization) GetEmail() *plugin.TValue[string] {
	return &c.Email
}

func (c *mqlGithubOrganization) GetTwitterUsername() *plugin.TValue[string] {
	return &c.TwitterUsername
}

func (c *mqlGithubOrganization) GetAvatarUrl() *plugin.TValue[string] {
	return &c.AvatarUrl
}

func (c *mqlGithubOrganization) GetFollowers() *plugin.TValue[int64] {
	return &c.Followers
}

func (c *mqlGithubOrganization) GetFollowing() *plugin.TValue[int64] {
	return &c.Following
}

func (c *mqlGithubOrganization) GetDescription() *plugin.TValue[string] {
	return &c.Description
}

func (c *mqlGithubOrganization) GetCreatedAt() *plugin.TValue[*time.Time] {
	return &c.CreatedAt
}

func (c *mqlGithubOrganization) GetUpdatedAt() *plugin.TValue[*time.Time] {
	return &c.UpdatedAt
}

func (c *mqlGithubOrganization) GetTotalPrivateRepos() *plugin.TValue[int64] {
	return &c.TotalPrivateRepos
}

func (c *mqlGithubOrganization) GetTotalPublicRepos() *plugin.TValue[int64] {
	return &c.TotalPublicRepos
}

func (c *mqlGithubOrganization) GetOwnedPrivateRepos() *plugin.TValue[int64] {
	return &c.OwnedPrivateRepos
}

func (c *mqlGithubOrganization) GetPrivateGists() *plugin.TValue[int64] {
	return &c.PrivateGists
}

func (c *mqlGithubOrganization) GetDiskUsage() *plugin.TValue[int64] {
	return &c.DiskUsage
}

func (c *mqlGithubOrganization) GetCollaborators() *plugin.TValue[int64] {
	return &c.Collaborators
}

func (c *mqlGithubOrganization) GetBillingEmail() *plugin.TValue[string] {
	return &c.BillingEmail
}

func (c *mqlGithubOrganization) GetPlan() *plugin.TValue[interface{}] {
	return &c.Plan
}

func (c *mqlGithubOrganization) GetTwoFactorRequirementEnabled() *plugin.TValue[bool] {
	return &c.TwoFactorRequirementEnabled
}

func (c *mqlGithubOrganization) GetIsVerified() *plugin.TValue[bool] {
	return &c.IsVerified
}

func (c *mqlGithubOrganization) GetDefaultRepositoryPermission() *plugin.TValue[string] {
	return &c.DefaultRepositoryPermission
}

func (c *mqlGithubOrganization) GetMembersCanCreateRepositories() *plugin.TValue[bool] {
	return &c.MembersCanCreateRepositories
}

func (c *mqlGithubOrganization) GetMembersCanCreatePublicRepositories() *plugin.TValue[bool] {
	return &c.MembersCanCreatePublicRepositories
}

func (c *mqlGithubOrganization) GetMembersCanCreatePrivateRepositories() *plugin.TValue[bool] {
	return &c.MembersCanCreatePrivateRepositories
}

func (c *mqlGithubOrganization) GetMembersCanCreateInternalRepositories() *plugin.TValue[bool] {
	return &c.MembersCanCreateInternalRepositories
}

func (c *mqlGithubOrganization) GetMembersCanCreatePages() *plugin.TValue[bool] {
	return &c.MembersCanCreatePages
}

func (c *mqlGithubOrganization) GetMembersCanCreatePublicPages() *plugin.TValue[bool] {
	return &c.MembersCanCreatePublicPages
}

func (c *mqlGithubOrganization) GetMembersCanCreatePrivatePages() *plugin.TValue[bool] {
	return &c.MembersCanCreatePrivatePages
}

func (c *mqlGithubOrganization) GetMembersCanForkPrivateRepos() *plugin.TValue[bool] {
	return &c.MembersCanForkPrivateRepos
}

func (c *mqlGithubOrganization) GetOwners() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.Owners, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("github.organization", c.__id, "owners")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.owners()
	})
}

func (c *mqlGithubOrganization) GetMembers() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.Members, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("github.organization", c.__id, "members")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.members()
	})
}

func (c *mqlGithubOrganization) GetTeams() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.Teams, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("github.organization", c.__id, "teams")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.teams()
	})
}

func (c *mqlGithubOrganization) GetRepositories() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.Repositories, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("github.organization", c.__id, "repositories")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.repositories()
	})
}

func (c *mqlGithubOrganization) GetInstallations() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.Installations, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("github.organization", c.__id, "installations")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.installations()
	})
}

func (c *mqlGithubOrganization) GetWebhooks() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.Webhooks, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("github.organization", c.__id, "webhooks")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.webhooks()
	})
}

func (c *mqlGithubOrganization) GetPackages() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.Packages, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("github.organization", c.__id, "packages")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.packages()
	})
}

func (c *mqlGithubOrganization) GetHasOrganizationProjects() *plugin.TValue[bool] {
	return &c.HasOrganizationProjects
}

func (c *mqlGithubOrganization) GetHasRepositoryProjects() *plugin.TValue[bool] {
	return &c.HasRepositoryProjects
}

func (c *mqlGithubOrganization) GetCustomProperties() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.CustomProperties, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("github.organization", c.__id, "customProperties")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.customProperties()
	})
}

// mqlGithubOrganizationCustomProperty for the github.organization.customProperty resource
type mqlGithubOrganizationCustomProperty struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlGithubOrganizationCustomPropertyInternal it will be used here
	Name plugin.TValue[string]
	Description plugin.TValue[string]
	SourceType plugin.TValue[string]
	ValueType plugin.TValue[string]
	Required plugin.TValue[bool]
	DefaultValue plugin.TValue[string]
	AllowedValues plugin.TValue[[]interface{}]
	ValuesEditableBy plugin.TValue[string]
}

// createGithubOrganizationCustomProperty creates a new instance of this resource
func createGithubOrganizationCustomProperty(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlGithubOrganizationCustomProperty{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	if res.__id == "" {
	res.__id, err = res.id()
		if err != nil {
			return nil, err
		}
	}

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("github.organization.customProperty", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlGithubOrganizationCustomProperty) MqlName() string {
	return "github.organization.customProperty"
}

func (c *mqlGithubOrganizationCustomProperty) MqlID() string {
	return c.__id
}

func (c *mqlGithubOrganizationCustomProperty) GetName() *plugin.TValue[string] {
	return &c.Name
}

func (c *mqlGithubOrganizationCustomProperty) GetDescription() *plugin.TValue[string] {
	return &c.Description
}

func (c *mqlGithubOrganizationCustomProperty) GetSourceType() *plugin.TValue[string] {
	return &c.SourceType
}

func (c *mqlGithubOrganizationCustomProperty) GetValueType() *plugin.TValue[string] {
	return &c.ValueType
}

func (c *mqlGithubOrganizationCustomProperty) GetRequired() *plugin.TValue[bool] {
	return &c.Required
}

func (c *mqlGithubOrganizationCustomProperty) GetDefaultValue() *plugin.TValue[string] {
	return &c.DefaultValue
}

func (c *mqlGithubOrganizationCustomProperty) GetAllowedValues() *plugin.TValue[[]interface{}] {
	return &c.AllowedValues
}

func (c *mqlGithubOrganizationCustomProperty) GetValuesEditableBy() *plugin.TValue[string] {
	return &c.ValuesEditableBy
}

// mqlGithubUser for the github.user resource
type mqlGithubUser struct {
	MqlRuntime *plugin.Runtime
	__id string
	mqlGithubUserInternal
	Id plugin.TValue[int64]
	Login plugin.TValue[string]
	Name plugin.TValue[string]
	Email plugin.TValue[string]
	Bio plugin.TValue[string]
	Blog plugin.TValue[string]
	Location plugin.TValue[string]
	AvatarUrl plugin.TValue[string]
	Followers plugin.TValue[int64]
	Following plugin.TValue[int64]
	TwitterUsername plugin.TValue[string]
	CreatedAt plugin.TValue[*time.Time]
	UpdatedAt plugin.TValue[*time.Time]
	SuspendedAt plugin.TValue[*time.Time]
	Company plugin.TValue[string]
	Repositories plugin.TValue[[]interface{}]
	Gists plugin.TValue[[]interface{}]
}

// createGithubUser creates a new instance of this resource
func createGithubUser(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlGithubUser{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	if res.__id == "" {
	res.__id, err = res.id()
		if err != nil {
			return nil, err
		}
	}

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("github.user", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlGithubUser) MqlName() string {
	return "github.user"
}

func (c *mqlGithubUser) MqlID() string {
	return c.__id
}

func (c *mqlGithubUser) GetId() *plugin.TValue[int64] {
	return &c.Id
}

func (c *mqlGithubUser) GetLogin() *plugin.TValue[string] {
	return &c.Login
}

func (c *mqlGithubUser) GetName() *plugin.TValue[string] {
	return &c.Name
}

func (c *mqlGithubUser) GetEmail() *plugin.TValue[string] {
	return &c.Email
}

func (c *mqlGithubUser) GetBio() *plugin.TValue[string] {
	return &c.Bio
}

func (c *mqlGithubUser) GetBlog() *plugin.TValue[string] {
	return &c.Blog
}

func (c *mqlGithubUser) GetLocation() *plugin.TValue[string] {
	return &c.Location
}

func (c *mqlGithubUser) GetAvatarUrl() *plugin.TValue[string] {
	return &c.AvatarUrl
}

func (c *mqlGithubUser) GetFollowers() *plugin.TValue[int64] {
	return &c.Followers
}

func (c *mqlGithubUser) GetFollowing() *plugin.TValue[int64] {
	return &c.Following
}

func (c *mqlGithubUser) GetTwitterUsername() *plugin.TValue[string] {
	return &c.TwitterUsername
}

func (c *mqlGithubUser) GetCreatedAt() *plugin.TValue[*time.Time] {
	return &c.CreatedAt
}

func (c *mqlGithubUser) GetUpdatedAt() *plugin.TValue[*time.Time] {
	return &c.UpdatedAt
}

func (c *mqlGithubUser) GetSuspendedAt() *plugin.TValue[*time.Time] {
	return &c.SuspendedAt
}

func (c *mqlGithubUser) GetCompany() *plugin.TValue[string] {
	return &c.Company
}

func (c *mqlGithubUser) GetRepositories() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.Repositories, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("github.user", c.__id, "repositories")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.repositories()
	})
}

func (c *mqlGithubUser) GetGists() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.Gists, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("github.user", c.__id, "gists")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.gists()
	})
}

// mqlGithubTeam for the github.team resource
type mqlGithubTeam struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlGithubTeamInternal it will be used here
	Id plugin.TValue[int64]
	Name plugin.TValue[string]
	Description plugin.TValue[string]
	Slug plugin.TValue[string]
	Privacy plugin.TValue[string]
	DefaultPermission plugin.TValue[string]
	Members plugin.TValue[[]interface{}]
	Repositories plugin.TValue[[]interface{}]
	Organization plugin.TValue[*mqlGithubOrganization]
}

// createGithubTeam creates a new instance of this resource
func createGithubTeam(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlGithubTeam{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	if res.__id == "" {
	res.__id, err = res.id()
		if err != nil {
			return nil, err
		}
	}

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("github.team", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlGithubTeam) MqlName() string {
	return "github.team"
}

func (c *mqlGithubTeam) MqlID() string {
	return c.__id
}

func (c *mqlGithubTeam) GetId() *plugin.TValue[int64] {
	return &c.Id
}

func (c *mqlGithubTeam) GetName() *plugin.TValue[string] {
	return &c.Name
}

func (c *mqlGithubTeam) GetDescription() *plugin.TValue[string] {
	return &c.Description
}

func (c *mqlGithubTeam) GetSlug() *plugin.TValue[string] {
	return &c.Slug
}

func (c *mqlGithubTeam) GetPrivacy() *plugin.TValue[string] {
	return &c.Privacy
}

func (c *mqlGithubTeam) GetDefaultPermission() *plugin.TValue[string] {
	return &c.DefaultPermission
}

func (c *mqlGithubTeam) GetMembers() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.Members, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("github.team", c.__id, "members")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.members()
	})
}

func (c *mqlGithubTeam) GetRepositories() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.Repositories, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("github.team", c.__id, "repositories")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.repositories()
	})
}

func (c *mqlGithubTeam) GetOrganization() *plugin.TValue[*mqlGithubOrganization] {
	return &c.Organization
}

// mqlGithubCollaborator for the github.collaborator resource
type mqlGithubCollaborator struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlGithubCollaboratorInternal it will be used here
	Id plugin.TValue[int64]
	User plugin.TValue[*mqlGithubUser]
	Permissions plugin.TValue[[]interface{}]
}

// createGithubCollaborator creates a new instance of this resource
func createGithubCollaborator(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlGithubCollaborator{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	if res.__id == "" {
	res.__id, err = res.id()
		if err != nil {
			return nil, err
		}
	}

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("github.collaborator", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlGithubCollaborator) MqlName() string {
	return "github.collaborator"
}

func (c *mqlGithubCollaborator) MqlID() string {
	return c.__id
}

func (c *mqlGithubCollaborator) GetId() *plugin.TValue[int64] {
	return &c.Id
}

func (c *mqlGithubCollaborator) GetUser() *plugin.TValue[*mqlGithubUser] {
	return &c.User
}

func (c *mqlGithubCollaborator) GetPermissions() *plugin.TValue[[]interface{}] {
	return &c.Permissions
}

// mqlGithubPackage for the github.package resource
type mqlGithubPackage struct {
	MqlRuntime *plugin.Runtime
	__id string
	mqlGithubPackageInternal
	Id plugin.TValue[int64]
	Name plugin.TValue[string]
	PackageType plugin.TValue[string]
	Owner plugin.TValue[*mqlGithubUser]
	CreatedAt plugin.TValue[*time.Time]
	UpdatedAt plugin.TValue[*time.Time]
	VersionCount plugin.TValue[int64]
	Visibility plugin.TValue[string]
	Repository plugin.TValue[*mqlGithubRepository]
}

// createGithubPackage creates a new instance of this resource
func createGithubPackage(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlGithubPackage{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	if res.__id == "" {
	res.__id, err = res.id()
		if err != nil {
			return nil, err
		}
	}

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("github.package", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlGithubPackage) MqlName() string {
	return "github.package"
}

func (c *mqlGithubPackage) MqlID() string {
	return c.__id
}

func (c *mqlGithubPackage) GetId() *plugin.TValue[int64] {
	return &c.Id
}

func (c *mqlGithubPackage) GetName() *plugin.TValue[string] {
	return &c.Name
}

func (c *mqlGithubPackage) GetPackageType() *plugin.TValue[string] {
	return &c.PackageType
}

func (c *mqlGithubPackage) GetOwner() *plugin.TValue[*mqlGithubUser] {
	return &c.Owner
}

func (c *mqlGithubPackage) GetCreatedAt() *plugin.TValue[*time.Time] {
	return &c.CreatedAt
}

func (c *mqlGithubPackage) GetUpdatedAt() *plugin.TValue[*time.Time] {
	return &c.UpdatedAt
}

func (c *mqlGithubPackage) GetVersionCount() *plugin.TValue[int64] {
	return &c.VersionCount
}

func (c *mqlGithubPackage) GetVisibility() *plugin.TValue[string] {
	return &c.Visibility
}

func (c *mqlGithubPackage) GetRepository() *plugin.TValue[*mqlGithubRepository] {
	return plugin.GetOrCompute[*mqlGithubRepository](&c.Repository, func() (*mqlGithubRepository, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("github.package", c.__id, "repository")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.(*mqlGithubRepository), nil
			}
		}

		return c.repository()
	})
}

// mqlGithubPackages for the github.packages resource
type mqlGithubPackages struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlGithubPackagesInternal it will be used here
	Public plugin.TValue[[]interface{}]
	Private plugin.TValue[[]interface{}]
	Internal plugin.TValue[[]interface{}]
	List plugin.TValue[[]interface{}]
}

// createGithubPackages creates a new instance of this resource
func createGithubPackages(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlGithubPackages{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	if res.__id == "" {
	res.__id, err = res.id()
		if err != nil {
			return nil, err
		}
	}

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("github.packages", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlGithubPackages) MqlName() string {
	return "github.packages"
}

func (c *mqlGithubPackages) MqlID() string {
	return c.__id
}

func (c *mqlGithubPackages) GetPublic() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.Public, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("github.packages", c.__id, "public")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.public()
	})
}

func (c *mqlGithubPackages) GetPrivate() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.Private, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("github.packages", c.__id, "private")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.private()
	})
}

func (c *mqlGithubPackages) GetInternal() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.Internal, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("github.packages", c.__id, "internal")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.internal()
	})
}

func (c *mqlGithubPackages) GetList() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.List, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("github.packages", c.__id, "list")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.list()
	})
}

// mqlGithubRepository for the github.repository resource
type mqlGithubRepository struct {
	MqlRuntime *plugin.Runtime
	__id string
	mqlGithubRepositoryInternal
	Id plugin.TValue[int64]
	Name plugin.TValue[string]
	FullName plugin.TValue[string]
	Description plugin.TValue[string]
	CloneUrl plugin.TValue[string]
	SshUrl plugin.TValue[string]
	Homepage plugin.TValue[string]
	Topics plugin.TValue[[]interface{}]
	Language plugin.TValue[string]
	WatchersCount plugin.TValue[int64]
	ForksCount plugin.TValue[int64]
	StargazersCount plugin.TValue[int64]
	OpenIssuesCount plugin.TValue[int64]
	CreatedAt plugin.TValue[*time.Time]
	UpdatedAt plugin.TValue[*time.Time]
	PushedAt plugin.TValue[*time.Time]
	Archived plugin.TValue[bool]
	Disabled plugin.TValue[bool]
	Private plugin.TValue[bool]
	IsFork plugin.TValue[bool]
	Visibility plugin.TValue[string]
	AllowAutoMerge plugin.TValue[bool]
	AllowForking plugin.TValue[bool]
	AllowMergeCommit plugin.TValue[bool]
	AllowRebaseMerge plugin.TValue[bool]
	AllowSquashMerge plugin.TValue[bool]
	HasIssues plugin.TValue[bool]
	HasProjects plugin.TValue[bool]
	HasWiki plugin.TValue[bool]
	HasPages plugin.TValue[bool]
	HasDownloads plugin.TValue[bool]
	HasDiscussions plugin.TValue[bool]
	IsTemplate plugin.TValue[bool]
	CustomProperties plugin.TValue[interface{}]
	OpenMergeRequests plugin.TValue[[]interface{}]
	ClosedMergeRequests plugin.TValue[[]interface{}]
	AllMergeRequests plugin.TValue[[]interface{}]
	Branches plugin.TValue[[]interface{}]
	DefaultBranchName plugin.TValue[string]
	DefaultBranch plugin.TValue[*mqlGithubBranch]
	Commits plugin.TValue[[]interface{}]
	Contributors plugin.TValue[[]interface{}]
	Collaborators plugin.TValue[[]interface{}]
	AdminCollaborators plugin.TValue[[]interface{}]
	Files plugin.TValue[[]interface{}]
	Releases plugin.TValue[[]interface{}]
	Owner plugin.TValue[*mqlGithubUser]
	Webhooks plugin.TValue[[]interface{}]
	Workflows plugin.TValue[[]interface{}]
	Forks plugin.TValue[[]interface{}]
	Stargazers plugin.TValue[[]interface{}]
	OpenIssues plugin.TValue[[]interface{}]
	ClosedIssues plugin.TValue[[]interface{}]
	License plugin.TValue[*mqlGithubLicense]
	CodeOfConductFile plugin.TValue[*mqlGithubFile]
	SupportFile plugin.TValue[*mqlGithubFile]
	SecurityFile plugin.TValue[*mqlGithubFile]
}

// createGithubRepository creates a new instance of this resource
func createGithubRepository(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlGithubRepository{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	if res.__id == "" {
	res.__id, err = res.id()
		if err != nil {
			return nil, err
		}
	}

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("github.repository", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlGithubRepository) MqlName() string {
	return "github.repository"
}

func (c *mqlGithubRepository) MqlID() string {
	return c.__id
}

func (c *mqlGithubRepository) GetId() *plugin.TValue[int64] {
	return &c.Id
}

func (c *mqlGithubRepository) GetName() *plugin.TValue[string] {
	return &c.Name
}

func (c *mqlGithubRepository) GetFullName() *plugin.TValue[string] {
	return &c.FullName
}

func (c *mqlGithubRepository) GetDescription() *plugin.TValue[string] {
	return &c.Description
}

func (c *mqlGithubRepository) GetCloneUrl() *plugin.TValue[string] {
	return &c.CloneUrl
}

func (c *mqlGithubRepository) GetSshUrl() *plugin.TValue[string] {
	return &c.SshUrl
}

func (c *mqlGithubRepository) GetHomepage() *plugin.TValue[string] {
	return &c.Homepage
}

func (c *mqlGithubRepository) GetTopics() *plugin.TValue[[]interface{}] {
	return &c.Topics
}

func (c *mqlGithubRepository) GetLanguage() *plugin.TValue[string] {
	return &c.Language
}

func (c *mqlGithubRepository) GetWatchersCount() *plugin.TValue[int64] {
	return &c.WatchersCount
}

func (c *mqlGithubRepository) GetForksCount() *plugin.TValue[int64] {
	return &c.ForksCount
}

func (c *mqlGithubRepository) GetStargazersCount() *plugin.TValue[int64] {
	return &c.StargazersCount
}

func (c *mqlGithubRepository) GetOpenIssuesCount() *plugin.TValue[int64] {
	return &c.OpenIssuesCount
}

func (c *mqlGithubRepository) GetCreatedAt() *plugin.TValue[*time.Time] {
	return &c.CreatedAt
}

func (c *mqlGithubRepository) GetUpdatedAt() *plugin.TValue[*time.Time] {
	return &c.UpdatedAt
}

func (c *mqlGithubRepository) GetPushedAt() *plugin.TValue[*time.Time] {
	return &c.PushedAt
}

func (c *mqlGithubRepository) GetArchived() *plugin.TValue[bool] {
	return &c.Archived
}

func (c *mqlGithubRepository) GetDisabled() *plugin.TValue[bool] {
	return &c.Disabled
}

func (c *mqlGithubRepository) GetPrivate() *plugin.TValue[bool] {
	return &c.Private
}

func (c *mqlGithubRepository) GetIsFork() *plugin.TValue[bool] {
	return &c.IsFork
}

func (c *mqlGithubRepository) GetVisibility() *plugin.TValue[string] {
	return &c.Visibility
}

func (c *mqlGithubRepository) GetAllowAutoMerge() *plugin.TValue[bool] {
	return &c.AllowAutoMerge
}

func (c *mqlGithubRepository) GetAllowForking() *plugin.TValue[bool] {
	return &c.AllowForking
}

func (c *mqlGithubRepository) GetAllowMergeCommit() *plugin.TValue[bool] {
	return &c.AllowMergeCommit
}

func (c *mqlGithubRepository) GetAllowRebaseMerge() *plugin.TValue[bool] {
	return &c.AllowRebaseMerge
}

func (c *mqlGithubRepository) GetAllowSquashMerge() *plugin.TValue[bool] {
	return &c.AllowSquashMerge
}

func (c *mqlGithubRepository) GetHasIssues() *plugin.TValue[bool] {
	return &c.HasIssues
}

func (c *mqlGithubRepository) GetHasProjects() *plugin.TValue[bool] {
	return &c.HasProjects
}

func (c *mqlGithubRepository) GetHasWiki() *plugin.TValue[bool] {
	return &c.HasWiki
}

func (c *mqlGithubRepository) GetHasPages() *plugin.TValue[bool] {
	return &c.HasPages
}

func (c *mqlGithubRepository) GetHasDownloads() *plugin.TValue[bool] {
	return &c.HasDownloads
}

func (c *mqlGithubRepository) GetHasDiscussions() *plugin.TValue[bool] {
	return &c.HasDiscussions
}

func (c *mqlGithubRepository) GetIsTemplate() *plugin.TValue[bool] {
	return &c.IsTemplate
}

func (c *mqlGithubRepository) GetCustomProperties() *plugin.TValue[interface{}] {
	return &c.CustomProperties
}

func (c *mqlGithubRepository) GetOpenMergeRequests() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.OpenMergeRequests, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("github.repository", c.__id, "openMergeRequests")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.openMergeRequests()
	})
}

func (c *mqlGithubRepository) GetClosedMergeRequests() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.ClosedMergeRequests, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("github.repository", c.__id, "closedMergeRequests")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.closedMergeRequests()
	})
}

func (c *mqlGithubRepository) GetAllMergeRequests() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.AllMergeRequests, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("github.repository", c.__id, "allMergeRequests")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.allMergeRequests()
	})
}

func (c *mqlGithubRepository) GetBranches() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.Branches, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("github.repository", c.__id, "branches")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.branches()
	})
}

func (c *mqlGithubRepository) GetDefaultBranchName() *plugin.TValue[string] {
	return &c.DefaultBranchName
}

func (c *mqlGithubRepository) GetDefaultBranch() *plugin.TValue[*mqlGithubBranch] {
	return plugin.GetOrCompute[*mqlGithubBranch](&c.DefaultBranch, func() (*mqlGithubBranch, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("github.repository", c.__id, "defaultBranch")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.(*mqlGithubBranch), nil
			}
		}

		return c.defaultBranch()
	})
}

func (c *mqlGithubRepository) GetCommits() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.Commits, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("github.repository", c.__id, "commits")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.commits()
	})
}

func (c *mqlGithubRepository) GetContributors() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.Contributors, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("github.repository", c.__id, "contributors")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.contributors()
	})
}

func (c *mqlGithubRepository) GetCollaborators() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.Collaborators, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("github.repository", c.__id, "collaborators")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.collaborators()
	})
}

func (c *mqlGithubRepository) GetAdminCollaborators() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.AdminCollaborators, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("github.repository", c.__id, "adminCollaborators")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.adminCollaborators()
	})
}

func (c *mqlGithubRepository) GetFiles() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.Files, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("github.repository", c.__id, "files")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.files()
	})
}

func (c *mqlGithubRepository) GetReleases() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.Releases, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("github.repository", c.__id, "releases")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.releases()
	})
}

func (c *mqlGithubRepository) GetOwner() *plugin.TValue[*mqlGithubUser] {
	return &c.Owner
}

func (c *mqlGithubRepository) GetWebhooks() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.Webhooks, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("github.repository", c.__id, "webhooks")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.webhooks()
	})
}

func (c *mqlGithubRepository) GetWorkflows() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.Workflows, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("github.repository", c.__id, "workflows")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.workflows()
	})
}

func (c *mqlGithubRepository) GetForks() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.Forks, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("github.repository", c.__id, "forks")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.forks()
	})
}

func (c *mqlGithubRepository) GetStargazers() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.Stargazers, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("github.repository", c.__id, "stargazers")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.stargazers()
	})
}

func (c *mqlGithubRepository) GetOpenIssues() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.OpenIssues, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("github.repository", c.__id, "openIssues")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.openIssues()
	})
}

func (c *mqlGithubRepository) GetClosedIssues() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.ClosedIssues, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("github.repository", c.__id, "closedIssues")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.closedIssues()
	})
}

func (c *mqlGithubRepository) GetLicense() *plugin.TValue[*mqlGithubLicense] {
	return plugin.GetOrCompute[*mqlGithubLicense](&c.License, func() (*mqlGithubLicense, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("github.repository", c.__id, "license")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.(*mqlGithubLicense), nil
			}
		}

		return c.license()
	})
}

func (c *mqlGithubRepository) GetCodeOfConductFile() *plugin.TValue[*mqlGithubFile] {
	return plugin.GetOrCompute[*mqlGithubFile](&c.CodeOfConductFile, func() (*mqlGithubFile, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("github.repository", c.__id, "codeOfConductFile")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.(*mqlGithubFile), nil
			}
		}

		return c.codeOfConductFile()
	})
}

func (c *mqlGithubRepository) GetSupportFile() *plugin.TValue[*mqlGithubFile] {
	return plugin.GetOrCompute[*mqlGithubFile](&c.SupportFile, func() (*mqlGithubFile, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("github.repository", c.__id, "supportFile")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.(*mqlGithubFile), nil
			}
		}

		return c.supportFile()
	})
}

func (c *mqlGithubRepository) GetSecurityFile() *plugin.TValue[*mqlGithubFile] {
	return plugin.GetOrCompute[*mqlGithubFile](&c.SecurityFile, func() (*mqlGithubFile, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("github.repository", c.__id, "securityFile")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.(*mqlGithubFile), nil
			}
		}

		return c.securityFile()
	})
}

// mqlGithubLicense for the github.license resource
type mqlGithubLicense struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlGithubLicenseInternal it will be used here
	Key plugin.TValue[string]
	Name plugin.TValue[string]
	Url plugin.TValue[string]
	SpdxId plugin.TValue[string]
}

// createGithubLicense creates a new instance of this resource
func createGithubLicense(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlGithubLicense{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	if res.__id == "" {
	res.__id, err = res.id()
		if err != nil {
			return nil, err
		}
	}

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("github.license", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlGithubLicense) MqlName() string {
	return "github.license"
}

func (c *mqlGithubLicense) MqlID() string {
	return c.__id
}

func (c *mqlGithubLicense) GetKey() *plugin.TValue[string] {
	return &c.Key
}

func (c *mqlGithubLicense) GetName() *plugin.TValue[string] {
	return &c.Name
}

func (c *mqlGithubLicense) GetUrl() *plugin.TValue[string] {
	return &c.Url
}

func (c *mqlGithubLicense) GetSpdxId() *plugin.TValue[string] {
	return &c.SpdxId
}

// mqlGithubFile for the github.file resource
type mqlGithubFile struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlGithubFileInternal it will be used here
	Path plugin.TValue[string]
	Name plugin.TValue[string]
	Type plugin.TValue[string]
	Sha plugin.TValue[string]
	IsBinary plugin.TValue[bool]
	Files plugin.TValue[[]interface{}]
	OwnerName plugin.TValue[string]
	RepoName plugin.TValue[string]
	Content plugin.TValue[string]
	DownloadUrl plugin.TValue[string]
	Exists plugin.TValue[bool]
}

// createGithubFile creates a new instance of this resource
func createGithubFile(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlGithubFile{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	if res.__id == "" {
	res.__id, err = res.id()
		if err != nil {
			return nil, err
		}
	}

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("github.file", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlGithubFile) MqlName() string {
	return "github.file"
}

func (c *mqlGithubFile) MqlID() string {
	return c.__id
}

func (c *mqlGithubFile) GetPath() *plugin.TValue[string] {
	return &c.Path
}

func (c *mqlGithubFile) GetName() *plugin.TValue[string] {
	return &c.Name
}

func (c *mqlGithubFile) GetType() *plugin.TValue[string] {
	return &c.Type
}

func (c *mqlGithubFile) GetSha() *plugin.TValue[string] {
	return &c.Sha
}

func (c *mqlGithubFile) GetIsBinary() *plugin.TValue[bool] {
	return &c.IsBinary
}

func (c *mqlGithubFile) GetFiles() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.Files, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("github.file", c.__id, "files")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.files()
	})
}

func (c *mqlGithubFile) GetOwnerName() *plugin.TValue[string] {
	return &c.OwnerName
}

func (c *mqlGithubFile) GetRepoName() *plugin.TValue[string] {
	return &c.RepoName
}

func (c *mqlGithubFile) GetContent() *plugin.TValue[string] {
	return plugin.GetOrCompute[string](&c.Content, func() (string, error) {
		return c.content()
	})
}

func (c *mqlGithubFile) GetDownloadUrl() *plugin.TValue[string] {
	return &c.DownloadUrl
}

func (c *mqlGithubFile) GetExists() *plugin.TValue[bool] {
	return &c.Exists
}

// mqlGithubRelease for the github.release resource
type mqlGithubRelease struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlGithubReleaseInternal it will be used here
	Url plugin.TValue[string]
	Name plugin.TValue[string]
	TagName plugin.TValue[string]
	PreRelease plugin.TValue[bool]
}

// createGithubRelease creates a new instance of this resource
func createGithubRelease(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlGithubRelease{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	if res.__id == "" {
	res.__id, err = res.id()
		if err != nil {
			return nil, err
		}
	}

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("github.release", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlGithubRelease) MqlName() string {
	return "github.release"
}

func (c *mqlGithubRelease) MqlID() string {
	return c.__id
}

func (c *mqlGithubRelease) GetUrl() *plugin.TValue[string] {
	return &c.Url
}

func (c *mqlGithubRelease) GetName() *plugin.TValue[string] {
	return &c.Name
}

func (c *mqlGithubRelease) GetTagName() *plugin.TValue[string] {
	return &c.TagName
}

func (c *mqlGithubRelease) GetPreRelease() *plugin.TValue[bool] {
	return &c.PreRelease
}

// mqlGithubWebhook for the github.webhook resource
type mqlGithubWebhook struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlGithubWebhookInternal it will be used here
	Id plugin.TValue[int64]
	Name plugin.TValue[string]
	Url plugin.TValue[string]
	Events plugin.TValue[[]interface{}]
	Config plugin.TValue[interface{}]
	Active plugin.TValue[bool]
}

// createGithubWebhook creates a new instance of this resource
func createGithubWebhook(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlGithubWebhook{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	if res.__id == "" {
	res.__id, err = res.id()
		if err != nil {
			return nil, err
		}
	}

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("github.webhook", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlGithubWebhook) MqlName() string {
	return "github.webhook"
}

func (c *mqlGithubWebhook) MqlID() string {
	return c.__id
}

func (c *mqlGithubWebhook) GetId() *plugin.TValue[int64] {
	return &c.Id
}

func (c *mqlGithubWebhook) GetName() *plugin.TValue[string] {
	return &c.Name
}

func (c *mqlGithubWebhook) GetUrl() *plugin.TValue[string] {
	return &c.Url
}

func (c *mqlGithubWebhook) GetEvents() *plugin.TValue[[]interface{}] {
	return &c.Events
}

func (c *mqlGithubWebhook) GetConfig() *plugin.TValue[interface{}] {
	return &c.Config
}

func (c *mqlGithubWebhook) GetActive() *plugin.TValue[bool] {
	return &c.Active
}

// mqlGithubWorkflow for the github.workflow resource
type mqlGithubWorkflow struct {
	MqlRuntime *plugin.Runtime
	__id string
	mqlGithubWorkflowInternal
	Id plugin.TValue[int64]
	Name plugin.TValue[string]
	Path plugin.TValue[string]
	State plugin.TValue[string]
	CreatedAt plugin.TValue[*time.Time]
	UpdatedAt plugin.TValue[*time.Time]
	File plugin.TValue[*mqlGithubFile]
	Configuration plugin.TValue[interface{}]
}

// createGithubWorkflow creates a new instance of this resource
func createGithubWorkflow(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlGithubWorkflow{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	if res.__id == "" {
	res.__id, err = res.id()
		if err != nil {
			return nil, err
		}
	}

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("github.workflow", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlGithubWorkflow) MqlName() string {
	return "github.workflow"
}

func (c *mqlGithubWorkflow) MqlID() string {
	return c.__id
}

func (c *mqlGithubWorkflow) GetId() *plugin.TValue[int64] {
	return &c.Id
}

func (c *mqlGithubWorkflow) GetName() *plugin.TValue[string] {
	return &c.Name
}

func (c *mqlGithubWorkflow) GetPath() *plugin.TValue[string] {
	return &c.Path
}

func (c *mqlGithubWorkflow) GetState() *plugin.TValue[string] {
	return &c.State
}

func (c *mqlGithubWorkflow) GetCreatedAt() *plugin.TValue[*time.Time] {
	return &c.CreatedAt
}

func (c *mqlGithubWorkflow) GetUpdatedAt() *plugin.TValue[*time.Time] {
	return &c.UpdatedAt
}

func (c *mqlGithubWorkflow) GetFile() *plugin.TValue[*mqlGithubFile] {
	return plugin.GetOrCompute[*mqlGithubFile](&c.File, func() (*mqlGithubFile, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("github.workflow", c.__id, "file")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.(*mqlGithubFile), nil
			}
		}

		return c.file()
	})
}

func (c *mqlGithubWorkflow) GetConfiguration() *plugin.TValue[interface{}] {
	return plugin.GetOrCompute[interface{}](&c.Configuration, func() (interface{}, error) {
		return c.configuration()
	})
}

// mqlGithubBranch for the github.branch resource
type mqlGithubBranch struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlGithubBranchInternal it will be used here
	Name plugin.TValue[string]
	IsProtected plugin.TValue[bool]
	HeadCommit plugin.TValue[*mqlGithubCommit]
	HeadCommitSha plugin.TValue[string]
	ProtectionRules plugin.TValue[*mqlGithubBranchprotection]
	RepoName plugin.TValue[string]
	Owner plugin.TValue[*mqlGithubUser]
	IsDefault plugin.TValue[bool]
}

// createGithubBranch creates a new instance of this resource
func createGithubBranch(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlGithubBranch{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	if res.__id == "" {
	res.__id, err = res.id()
		if err != nil {
			return nil, err
		}
	}

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("github.branch", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlGithubBranch) MqlName() string {
	return "github.branch"
}

func (c *mqlGithubBranch) MqlID() string {
	return c.__id
}

func (c *mqlGithubBranch) GetName() *plugin.TValue[string] {
	return &c.Name
}

func (c *mqlGithubBranch) GetIsProtected() *plugin.TValue[bool] {
	return &c.IsProtected
}

func (c *mqlGithubBranch) GetHeadCommit() *plugin.TValue[*mqlGithubCommit] {
	return plugin.GetOrCompute[*mqlGithubCommit](&c.HeadCommit, func() (*mqlGithubCommit, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("github.branch", c.__id, "headCommit")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.(*mqlGithubCommit), nil
			}
		}

		return c.headCommit()
	})
}

func (c *mqlGithubBranch) GetHeadCommitSha() *plugin.TValue[string] {
	return &c.HeadCommitSha
}

func (c *mqlGithubBranch) GetProtectionRules() *plugin.TValue[*mqlGithubBranchprotection] {
	return plugin.GetOrCompute[*mqlGithubBranchprotection](&c.ProtectionRules, func() (*mqlGithubBranchprotection, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("github.branch", c.__id, "protectionRules")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.(*mqlGithubBranchprotection), nil
			}
		}

		return c.protectionRules()
	})
}

func (c *mqlGithubBranch) GetRepoName() *plugin.TValue[string] {
	return &c.RepoName
}

func (c *mqlGithubBranch) GetOwner() *plugin.TValue[*mqlGithubUser] {
	return &c.Owner
}

func (c *mqlGithubBranch) GetIsDefault() *plugin.TValue[bool] {
	return &c.IsDefault
}

// mqlGithubBranchprotection for the github.branchprotection resource
type mqlGithubBranchprotection struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlGithubBranchprotectionInternal it will be used here
	Id plugin.TValue[string]
	RequiredStatusChecks plugin.TValue[interface{}]
	RequiredPullRequestReviews plugin.TValue[interface{}]
	RequiredConversationResolution plugin.TValue[interface{}]
	RequiredSignatures plugin.TValue[bool]
	RequireLinearHistory plugin.TValue[interface{}]
	EnforceAdmins plugin.TValue[interface{}]
	Restrictions plugin.TValue[interface{}]
	AllowForcePushes plugin.TValue[interface{}]
	AllowDeletions plugin.TValue[interface{}]
}

// createGithubBranchprotection creates a new instance of this resource
func createGithubBranchprotection(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlGithubBranchprotection{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	if res.__id == "" {
	res.__id, err = res.id()
		if err != nil {
			return nil, err
		}
	}

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("github.branchprotection", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlGithubBranchprotection) MqlName() string {
	return "github.branchprotection"
}

func (c *mqlGithubBranchprotection) MqlID() string {
	return c.__id
}

func (c *mqlGithubBranchprotection) GetId() *plugin.TValue[string] {
	return &c.Id
}

func (c *mqlGithubBranchprotection) GetRequiredStatusChecks() *plugin.TValue[interface{}] {
	return &c.RequiredStatusChecks
}

func (c *mqlGithubBranchprotection) GetRequiredPullRequestReviews() *plugin.TValue[interface{}] {
	return &c.RequiredPullRequestReviews
}

func (c *mqlGithubBranchprotection) GetRequiredConversationResolution() *plugin.TValue[interface{}] {
	return &c.RequiredConversationResolution
}

func (c *mqlGithubBranchprotection) GetRequiredSignatures() *plugin.TValue[bool] {
	return &c.RequiredSignatures
}

func (c *mqlGithubBranchprotection) GetRequireLinearHistory() *plugin.TValue[interface{}] {
	return &c.RequireLinearHistory
}

func (c *mqlGithubBranchprotection) GetEnforceAdmins() *plugin.TValue[interface{}] {
	return &c.EnforceAdmins
}

func (c *mqlGithubBranchprotection) GetRestrictions() *plugin.TValue[interface{}] {
	return &c.Restrictions
}

func (c *mqlGithubBranchprotection) GetAllowForcePushes() *plugin.TValue[interface{}] {
	return &c.AllowForcePushes
}

func (c *mqlGithubBranchprotection) GetAllowDeletions() *plugin.TValue[interface{}] {
	return &c.AllowDeletions
}

// mqlGithubCommit for the github.commit resource
type mqlGithubCommit struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlGithubCommitInternal it will be used here
	Owner plugin.TValue[string]
	Repository plugin.TValue[string]
	Sha plugin.TValue[string]
	Url plugin.TValue[string]
	Author plugin.TValue[*mqlGithubUser]
	Committer plugin.TValue[*mqlGithubUser]
	Commit plugin.TValue[*mqlGitCommit]
	Stats plugin.TValue[interface{}]
	AuthoredDate plugin.TValue[*time.Time]
	CommittedDate plugin.TValue[*time.Time]
}

// createGithubCommit creates a new instance of this resource
func createGithubCommit(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlGithubCommit{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	if res.__id == "" {
	res.__id, err = res.id()
		if err != nil {
			return nil, err
		}
	}

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("github.commit", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlGithubCommit) MqlName() string {
	return "github.commit"
}

func (c *mqlGithubCommit) MqlID() string {
	return c.__id
}

func (c *mqlGithubCommit) GetOwner() *plugin.TValue[string] {
	return &c.Owner
}

func (c *mqlGithubCommit) GetRepository() *plugin.TValue[string] {
	return &c.Repository
}

func (c *mqlGithubCommit) GetSha() *plugin.TValue[string] {
	return &c.Sha
}

func (c *mqlGithubCommit) GetUrl() *plugin.TValue[string] {
	return &c.Url
}

func (c *mqlGithubCommit) GetAuthor() *plugin.TValue[*mqlGithubUser] {
	return &c.Author
}

func (c *mqlGithubCommit) GetCommitter() *plugin.TValue[*mqlGithubUser] {
	return &c.Committer
}

func (c *mqlGithubCommit) GetCommit() *plugin.TValue[*mqlGitCommit] {
	return &c.Commit
}

func (c *mqlGithubCommit) GetStats() *plugin.TValue[interface{}] {
	return &c.Stats
}

func (c *mqlGithubCommit) GetAuthoredDate() *plugin.TValue[*time.Time] {
	return &c.AuthoredDate
}

func (c *mqlGithubCommit) GetCommittedDate() *plugin.TValue[*time.Time] {
	return &c.CommittedDate
}

// mqlGithubMergeRequest for the github.mergeRequest resource
type mqlGithubMergeRequest struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlGithubMergeRequestInternal it will be used here
	Id plugin.TValue[int64]
	Number plugin.TValue[int64]
	State plugin.TValue[string]
	CreatedAt plugin.TValue[*time.Time]
	Labels plugin.TValue[[]interface{}]
	Title plugin.TValue[string]
	Owner plugin.TValue[*mqlGithubUser]
	Assignees plugin.TValue[[]interface{}]
	Commits plugin.TValue[[]interface{}]
	Reviews plugin.TValue[[]interface{}]
	RepoName plugin.TValue[string]
}

// createGithubMergeRequest creates a new instance of this resource
func createGithubMergeRequest(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlGithubMergeRequest{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	if res.__id == "" {
	res.__id, err = res.id()
		if err != nil {
			return nil, err
		}
	}

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("github.mergeRequest", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlGithubMergeRequest) MqlName() string {
	return "github.mergeRequest"
}

func (c *mqlGithubMergeRequest) MqlID() string {
	return c.__id
}

func (c *mqlGithubMergeRequest) GetId() *plugin.TValue[int64] {
	return &c.Id
}

func (c *mqlGithubMergeRequest) GetNumber() *plugin.TValue[int64] {
	return &c.Number
}

func (c *mqlGithubMergeRequest) GetState() *plugin.TValue[string] {
	return &c.State
}

func (c *mqlGithubMergeRequest) GetCreatedAt() *plugin.TValue[*time.Time] {
	return &c.CreatedAt
}

func (c *mqlGithubMergeRequest) GetLabels() *plugin.TValue[[]interface{}] {
	return &c.Labels
}

func (c *mqlGithubMergeRequest) GetTitle() *plugin.TValue[string] {
	return &c.Title
}

func (c *mqlGithubMergeRequest) GetOwner() *plugin.TValue[*mqlGithubUser] {
	return &c.Owner
}

func (c *mqlGithubMergeRequest) GetAssignees() *plugin.TValue[[]interface{}] {
	return &c.Assignees
}

func (c *mqlGithubMergeRequest) GetCommits() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.Commits, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("github.mergeRequest", c.__id, "commits")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.commits()
	})
}

func (c *mqlGithubMergeRequest) GetReviews() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.Reviews, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("github.mergeRequest", c.__id, "reviews")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.reviews()
	})
}

func (c *mqlGithubMergeRequest) GetRepoName() *plugin.TValue[string] {
	return &c.RepoName
}

// mqlGithubReview for the github.review resource
type mqlGithubReview struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlGithubReviewInternal it will be used here
	Url plugin.TValue[string]
	State plugin.TValue[string]
	AuthorAssociation plugin.TValue[string]
	User plugin.TValue[*mqlGithubUser]
}

// createGithubReview creates a new instance of this resource
func createGithubReview(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlGithubReview{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	if res.__id == "" {
	res.__id, err = res.id()
		if err != nil {
			return nil, err
		}
	}

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("github.review", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlGithubReview) MqlName() string {
	return "github.review"
}

func (c *mqlGithubReview) MqlID() string {
	return c.__id
}

func (c *mqlGithubReview) GetUrl() *plugin.TValue[string] {
	return &c.Url
}

func (c *mqlGithubReview) GetState() *plugin.TValue[string] {
	return &c.State
}

func (c *mqlGithubReview) GetAuthorAssociation() *plugin.TValue[string] {
	return &c.AuthorAssociation
}

func (c *mqlGithubReview) GetUser() *plugin.TValue[*mqlGithubUser] {
	return &c.User
}

// mqlGithubInstallation for the github.installation resource
type mqlGithubInstallation struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlGithubInstallationInternal it will be used here
	Id plugin.TValue[int64]
	AppId plugin.TValue[int64]
	AppSlug plugin.TValue[string]
	CreatedAt plugin.TValue[*time.Time]
	UpdatedAt plugin.TValue[*time.Time]
}

// createGithubInstallation creates a new instance of this resource
func createGithubInstallation(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlGithubInstallation{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	if res.__id == "" {
	res.__id, err = res.id()
		if err != nil {
			return nil, err
		}
	}

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("github.installation", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlGithubInstallation) MqlName() string {
	return "github.installation"
}

func (c *mqlGithubInstallation) MqlID() string {
	return c.__id
}

func (c *mqlGithubInstallation) GetId() *plugin.TValue[int64] {
	return &c.Id
}

func (c *mqlGithubInstallation) GetAppId() *plugin.TValue[int64] {
	return &c.AppId
}

func (c *mqlGithubInstallation) GetAppSlug() *plugin.TValue[string] {
	return &c.AppSlug
}

func (c *mqlGithubInstallation) GetCreatedAt() *plugin.TValue[*time.Time] {
	return &c.CreatedAt
}

func (c *mqlGithubInstallation) GetUpdatedAt() *plugin.TValue[*time.Time] {
	return &c.UpdatedAt
}

// mqlGithubGist for the github.gist resource
type mqlGithubGist struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlGithubGistInternal it will be used here
	Id plugin.TValue[string]
	Description plugin.TValue[string]
	CreatedAt plugin.TValue[*time.Time]
	UpdatedAt plugin.TValue[*time.Time]
	Owner plugin.TValue[*mqlGithubUser]
	Public plugin.TValue[bool]
	Files plugin.TValue[[]interface{}]
}

// createGithubGist creates a new instance of this resource
func createGithubGist(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlGithubGist{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	if res.__id == "" {
	res.__id, err = res.id()
		if err != nil {
			return nil, err
		}
	}

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("github.gist", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlGithubGist) MqlName() string {
	return "github.gist"
}

func (c *mqlGithubGist) MqlID() string {
	return c.__id
}

func (c *mqlGithubGist) GetId() *plugin.TValue[string] {
	return &c.Id
}

func (c *mqlGithubGist) GetDescription() *plugin.TValue[string] {
	return &c.Description
}

func (c *mqlGithubGist) GetCreatedAt() *plugin.TValue[*time.Time] {
	return &c.CreatedAt
}

func (c *mqlGithubGist) GetUpdatedAt() *plugin.TValue[*time.Time] {
	return &c.UpdatedAt
}

func (c *mqlGithubGist) GetOwner() *plugin.TValue[*mqlGithubUser] {
	return &c.Owner
}

func (c *mqlGithubGist) GetPublic() *plugin.TValue[bool] {
	return &c.Public
}

func (c *mqlGithubGist) GetFiles() *plugin.TValue[[]interface{}] {
	return &c.Files
}

// mqlGithubGistfile for the github.gistfile resource
type mqlGithubGistfile struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlGithubGistfileInternal it will be used here
	GistId plugin.TValue[string]
	Filename plugin.TValue[string]
	Type plugin.TValue[string]
	Language plugin.TValue[string]
	RawUrl plugin.TValue[string]
	Size plugin.TValue[int64]
	Content plugin.TValue[string]
}

// createGithubGistfile creates a new instance of this resource
func createGithubGistfile(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlGithubGistfile{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	if res.__id == "" {
	res.__id, err = res.id()
		if err != nil {
			return nil, err
		}
	}

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("github.gistfile", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlGithubGistfile) MqlName() string {
	return "github.gistfile"
}

func (c *mqlGithubGistfile) MqlID() string {
	return c.__id
}

func (c *mqlGithubGistfile) GetGistId() *plugin.TValue[string] {
	return &c.GistId
}

func (c *mqlGithubGistfile) GetFilename() *plugin.TValue[string] {
	return &c.Filename
}

func (c *mqlGithubGistfile) GetType() *plugin.TValue[string] {
	return &c.Type
}

func (c *mqlGithubGistfile) GetLanguage() *plugin.TValue[string] {
	return &c.Language
}

func (c *mqlGithubGistfile) GetRawUrl() *plugin.TValue[string] {
	return &c.RawUrl
}

func (c *mqlGithubGistfile) GetSize() *plugin.TValue[int64] {
	return &c.Size
}

func (c *mqlGithubGistfile) GetContent() *plugin.TValue[string] {
	return plugin.GetOrCompute[string](&c.Content, func() (string, error) {
		return c.content()
	})
}

// mqlGithubIssue for the github.issue resource
type mqlGithubIssue struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlGithubIssueInternal it will be used here
	Id plugin.TValue[int64]
	Number plugin.TValue[int64]
	Title plugin.TValue[string]
	State plugin.TValue[string]
	Body plugin.TValue[string]
	Url plugin.TValue[string]
	CreatedAt plugin.TValue[*time.Time]
	UpdatedAt plugin.TValue[*time.Time]
	ClosedAt plugin.TValue[*time.Time]
	Assignees plugin.TValue[[]interface{}]
	ClosedBy plugin.TValue[*mqlGithubUser]
}

// createGithubIssue creates a new instance of this resource
func createGithubIssue(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlGithubIssue{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	if res.__id == "" {
	res.__id, err = res.id()
		if err != nil {
			return nil, err
		}
	}

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("github.issue", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlGithubIssue) MqlName() string {
	return "github.issue"
}

func (c *mqlGithubIssue) MqlID() string {
	return c.__id
}

func (c *mqlGithubIssue) GetId() *plugin.TValue[int64] {
	return &c.Id
}

func (c *mqlGithubIssue) GetNumber() *plugin.TValue[int64] {
	return &c.Number
}

func (c *mqlGithubIssue) GetTitle() *plugin.TValue[string] {
	return &c.Title
}

func (c *mqlGithubIssue) GetState() *plugin.TValue[string] {
	return &c.State
}

func (c *mqlGithubIssue) GetBody() *plugin.TValue[string] {
	return &c.Body
}

func (c *mqlGithubIssue) GetUrl() *plugin.TValue[string] {
	return &c.Url
}

func (c *mqlGithubIssue) GetCreatedAt() *plugin.TValue[*time.Time] {
	return &c.CreatedAt
}

func (c *mqlGithubIssue) GetUpdatedAt() *plugin.TValue[*time.Time] {
	return &c.UpdatedAt
}

func (c *mqlGithubIssue) GetClosedAt() *plugin.TValue[*time.Time] {
	return &c.ClosedAt
}

func (c *mqlGithubIssue) GetAssignees() *plugin.TValue[[]interface{}] {
	return &c.Assignees
}

func (c *mqlGithubIssue) GetClosedBy() *plugin.TValue[*mqlGithubUser] {
	return &c.ClosedBy
}
