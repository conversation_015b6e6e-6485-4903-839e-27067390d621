// Copyright (c) Mondoo, Inc.
// SPDX-License-Identifier: BUSL-1.1

// Code generated by resources. DO NOT EDIT.

package resources

import (
	"errors"
	"time"

	"go.mondoo.com/cnquery/v11/llx"
	"go.mondoo.com/cnquery/v11/providers-sdk/v1/plugin"
	"go.mondoo.com/cnquery/v11/types"
)

var resourceFactories map[string]plugin.ResourceFactory

func init() {
	resourceFactories = map[string]plugin.ResourceFactory {
		"gitlab.group": {
			Init: initGitlabGroup,
			Create: createGitlabGroup,
		},
		"gitlab.project": {
			Init: initGitlabProject,
			Create: createGitlabProject,
		},
		"gitlab.project.approvalRule": {
			// to override args, implement: initGitlabProjectApprovalRule(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createGitlabProjectApprovalRule,
		},
		"gitlab.project.approvalSetting": {
			// to override args, implement: initGitlabProjectApprovalSetting(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createGitlabProjectApprovalSetting,
		},
		"gitlab.project.protectedBranch": {
			// to override args, implement: initGitlabProjectProtectedBranch(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createGitlabProjectProtectedBranch,
		},
		"gitlab.project.member": {
			// to override args, implement: initGitlabProjectMember(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createGitlabProjectMember,
		},
		"gitlab.project.file": {
			// to override args, implement: initGitlabProjectFile(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createGitlabProjectFile,
		},
		"gitlab.project.webhook": {
			// to override args, implement: initGitlabProjectWebhook(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createGitlabProjectWebhook,
		},
	}
}

// NewResource is used by the runtime of this plugin to create new resources.
// Its arguments may be provided by users. This function is generally not
// used by initializing resources from recordings or from lists.
func NewResource(runtime *plugin.Runtime, name string, args map[string]*llx.RawData) (plugin.Resource, error) {
	f, ok := resourceFactories[name]
	if !ok {
		return nil, errors.New("cannot find resource " + name + " in this provider")
	}

	if f.Init != nil {
		cargs, res, err := f.Init(runtime, args)
		if err != nil {
			return res, err
		}

		if res != nil {
			id := name+"\x00"+res.MqlID()
			if x, ok := runtime.Resources.Get(id); ok {
				return x, nil
			}
			runtime.Resources.Set(id, res)
			return res, nil
		}

		args = cargs
	}

	res, err := f.Create(runtime, args)
	if err != nil {
		return nil, err
	}

	id := name+"\x00"+res.MqlID()
	if x, ok := runtime.Resources.Get(id); ok {
		return x, nil
	}

	runtime.Resources.Set(id, res)
	return res, nil
}

// CreateResource is used by the runtime of this plugin to create resources.
// Its arguments must be complete and pre-processed. This method is used
// for initializing resources from recordings or from lists.
func CreateResource(runtime *plugin.Runtime, name string, args map[string]*llx.RawData) (plugin.Resource, error) {
	f, ok := resourceFactories[name]
	if !ok {
		return nil, errors.New("cannot find resource " + name + " in this provider")
	}

	res, err := f.Create(runtime, args)
	if err != nil {
		return nil, err
	}

	id := name+"\x00"+res.MqlID()
	if x, ok := runtime.Resources.Get(id); ok {
		return x, nil
	}

	runtime.Resources.Set(id, res)
	return res, nil
}

var getDataFields = map[string]func(r plugin.Resource) *plugin.DataRes{
	"gitlab.group.id": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGitlabGroup).GetId()).ToDataRes(types.Int)
	},
	"gitlab.group.name": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGitlabGroup).GetName()).ToDataRes(types.String)
	},
	"gitlab.group.path": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGitlabGroup).GetPath()).ToDataRes(types.String)
	},
	"gitlab.group.createdAt": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGitlabGroup).GetCreatedAt()).ToDataRes(types.Time)
	},
	"gitlab.group.description": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGitlabGroup).GetDescription()).ToDataRes(types.String)
	},
	"gitlab.group.webURL": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGitlabGroup).GetWebURL()).ToDataRes(types.String)
	},
	"gitlab.group.visibility": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGitlabGroup).GetVisibility()).ToDataRes(types.String)
	},
	"gitlab.group.requireTwoFactorAuthentication": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGitlabGroup).GetRequireTwoFactorAuthentication()).ToDataRes(types.Bool)
	},
	"gitlab.group.preventForkingOutsideGroup": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGitlabGroup).GetPreventForkingOutsideGroup()).ToDataRes(types.Bool)
	},
	"gitlab.group.emailsDisabled": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGitlabGroup).GetEmailsDisabled()).ToDataRes(types.Bool)
	},
	"gitlab.group.mentionsDisabled": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGitlabGroup).GetMentionsDisabled()).ToDataRes(types.Bool)
	},
	"gitlab.group.projects": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGitlabGroup).GetProjects()).ToDataRes(types.Array(types.Resource("gitlab.project")))
	},
	"gitlab.project.id": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGitlabProject).GetId()).ToDataRes(types.Int)
	},
	"gitlab.project.name": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGitlabProject).GetName()).ToDataRes(types.String)
	},
	"gitlab.project.fullName": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGitlabProject).GetFullName()).ToDataRes(types.String)
	},
	"gitlab.project.path": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGitlabProject).GetPath()).ToDataRes(types.String)
	},
	"gitlab.project.createdAt": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGitlabProject).GetCreatedAt()).ToDataRes(types.Time)
	},
	"gitlab.project.description": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGitlabProject).GetDescription()).ToDataRes(types.String)
	},
	"gitlab.project.defaultBranch": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGitlabProject).GetDefaultBranch()).ToDataRes(types.String)
	},
	"gitlab.project.visibility": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGitlabProject).GetVisibility()).ToDataRes(types.String)
	},
	"gitlab.project.archived": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGitlabProject).GetArchived()).ToDataRes(types.Bool)
	},
	"gitlab.project.mirror": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGitlabProject).GetMirror()).ToDataRes(types.Bool)
	},
	"gitlab.project.webURL": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGitlabProject).GetWebURL()).ToDataRes(types.String)
	},
	"gitlab.project.emailsDisabled": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGitlabProject).GetEmailsDisabled()).ToDataRes(types.Bool)
	},
	"gitlab.project.allowMergeOnSkippedPipeline": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGitlabProject).GetAllowMergeOnSkippedPipeline()).ToDataRes(types.Bool)
	},
	"gitlab.project.onlyAllowMergeIfPipelineSucceeds": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGitlabProject).GetOnlyAllowMergeIfPipelineSucceeds()).ToDataRes(types.Bool)
	},
	"gitlab.project.onlyAllowMergeIfAllDiscussionsAreResolved": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGitlabProject).GetOnlyAllowMergeIfAllDiscussionsAreResolved()).ToDataRes(types.Bool)
	},
	"gitlab.project.issuesEnabled": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGitlabProject).GetIssuesEnabled()).ToDataRes(types.Bool)
	},
	"gitlab.project.mergeRequestsEnabled": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGitlabProject).GetMergeRequestsEnabled()).ToDataRes(types.Bool)
	},
	"gitlab.project.wikiEnabled": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGitlabProject).GetWikiEnabled()).ToDataRes(types.Bool)
	},
	"gitlab.project.snippetsEnabled": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGitlabProject).GetSnippetsEnabled()).ToDataRes(types.Bool)
	},
	"gitlab.project.containerRegistryEnabled": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGitlabProject).GetContainerRegistryEnabled()).ToDataRes(types.Bool)
	},
	"gitlab.project.serviceDeskEnabled": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGitlabProject).GetServiceDeskEnabled()).ToDataRes(types.Bool)
	},
	"gitlab.project.packagesEnabled": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGitlabProject).GetPackagesEnabled()).ToDataRes(types.Bool)
	},
	"gitlab.project.autoDevopsEnabled": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGitlabProject).GetAutoDevopsEnabled()).ToDataRes(types.Bool)
	},
	"gitlab.project.requirementsEnabled": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGitlabProject).GetRequirementsEnabled()).ToDataRes(types.Bool)
	},
	"gitlab.project.approvalRules": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGitlabProject).GetApprovalRules()).ToDataRes(types.Array(types.Resource("gitlab.project.approvalRule")))
	},
	"gitlab.project.mergeMethod": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGitlabProject).GetMergeMethod()).ToDataRes(types.String)
	},
	"gitlab.project.approvalSettings": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGitlabProject).GetApprovalSettings()).ToDataRes(types.Resource("gitlab.project.approvalSetting"))
	},
	"gitlab.project.protectedBranches": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGitlabProject).GetProtectedBranches()).ToDataRes(types.Array(types.Resource("gitlab.project.protectedBranch")))
	},
	"gitlab.project.projectMembers": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGitlabProject).GetProjectMembers()).ToDataRes(types.Array(types.Resource("gitlab.project.member")))
	},
	"gitlab.project.projectFiles": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGitlabProject).GetProjectFiles()).ToDataRes(types.Array(types.Resource("gitlab.project.file")))
	},
	"gitlab.project.webhooks": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGitlabProject).GetWebhooks()).ToDataRes(types.Array(types.Resource("gitlab.project.webhook")))
	},
	"gitlab.project.jobsEnabled": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGitlabProject).GetJobsEnabled()).ToDataRes(types.Bool)
	},
	"gitlab.project.emptyRepo": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGitlabProject).GetEmptyRepo()).ToDataRes(types.Bool)
	},
	"gitlab.project.sharedRunnersEnabled": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGitlabProject).GetSharedRunnersEnabled()).ToDataRes(types.Bool)
	},
	"gitlab.project.groupRunnersEnabled": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGitlabProject).GetGroupRunnersEnabled()).ToDataRes(types.Bool)
	},
	"gitlab.project.approvalRule.id": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGitlabProjectApprovalRule).GetId()).ToDataRes(types.Int)
	},
	"gitlab.project.approvalRule.name": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGitlabProjectApprovalRule).GetName()).ToDataRes(types.String)
	},
	"gitlab.project.approvalRule.approvalsRequired": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGitlabProjectApprovalRule).GetApprovalsRequired()).ToDataRes(types.Int)
	},
	"gitlab.project.approvalSetting.approvalsBeforeMerge": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGitlabProjectApprovalSetting).GetApprovalsBeforeMerge()).ToDataRes(types.Int)
	},
	"gitlab.project.approvalSetting.********************": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGitlabProjectApprovalSetting).Get********************()).ToDataRes(types.Bool)
	},
	"gitlab.project.approvalSetting.disableOverridingApproversPerMergeRequest": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGitlabProjectApprovalSetting).GetDisableOverridingApproversPerMergeRequest()).ToDataRes(types.Bool)
	},
	"gitlab.project.approvalSetting.mergeRequestsAuthorApproval": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGitlabProjectApprovalSetting).GetMergeRequestsAuthorApproval()).ToDataRes(types.Bool)
	},
	"gitlab.project.approvalSetting.mergeRequestsDisableCommittersApproval": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGitlabProjectApprovalSetting).GetMergeRequestsDisableCommittersApproval()).ToDataRes(types.Bool)
	},
	"gitlab.project.approvalSetting.requirePasswordToApprove": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGitlabProjectApprovalSetting).GetRequirePasswordToApprove()).ToDataRes(types.Bool)
	},
	"gitlab.project.approvalSetting.selectiveCodeOwnerRemovals": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGitlabProjectApprovalSetting).GetSelectiveCodeOwnerRemovals()).ToDataRes(types.Bool)
	},
	"gitlab.project.protectedBranch.name": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGitlabProjectProtectedBranch).GetName()).ToDataRes(types.String)
	},
	"gitlab.project.protectedBranch.allowForcePush": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGitlabProjectProtectedBranch).GetAllowForcePush()).ToDataRes(types.Bool)
	},
	"gitlab.project.protectedBranch.defaultBranch": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGitlabProjectProtectedBranch).GetDefaultBranch()).ToDataRes(types.Bool)
	},
	"gitlab.project.protectedBranch.codeOwnerApproval": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGitlabProjectProtectedBranch).GetCodeOwnerApproval()).ToDataRes(types.Bool)
	},
	"gitlab.project.member.id": func(r plugin.Resource) *plugin.DataRes {
		return (r.(***********************).GetId()).ToDataRes(types.Int)
	},
	"gitlab.project.member.name": func(r plugin.Resource) *plugin.DataRes {
		return (r.(***********************).GetName()).ToDataRes(types.String)
	},
	"gitlab.project.member.role": func(r plugin.Resource) *plugin.DataRes {
		return (r.(***********************).GetRole()).ToDataRes(types.String)
	},
	"gitlab.project.member.username": func(r plugin.Resource) *plugin.DataRes {
		return (r.(***********************).GetUsername()).ToDataRes(types.String)
	},
	"gitlab.project.member.state": func(r plugin.Resource) *plugin.DataRes {
		return (r.(***********************).GetState()).ToDataRes(types.String)
	},
	"gitlab.project.file.path": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*********************).GetPath()).ToDataRes(types.String)
	},
	"gitlab.project.file.type": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*********************).GetType()).ToDataRes(types.String)
	},
	"gitlab.project.file.name": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*********************).GetName()).ToDataRes(types.String)
	},
	"gitlab.project.file.content": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*********************).GetContent()).ToDataRes(types.String)
	},
	"gitlab.project.webhook.url": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGitlabProjectWebhook).GetUrl()).ToDataRes(types.String)
	},
	"gitlab.project.webhook.sslVerification": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlGitlabProjectWebhook).GetSslVerification()).ToDataRes(types.Bool)
	},
}

func GetData(resource plugin.Resource, field string, args map[string]*llx.RawData) *plugin.DataRes {
	f, ok := getDataFields[resource.MqlName()+"."+field]
	if !ok {
		return &plugin.DataRes{Error: "cannot find '" + field + "' in resource '" + resource.MqlName() + "'"}
	}

	return f(resource)
}

var setDataFields = map[string]func(r plugin.Resource, v *llx.RawData) bool {
	"gitlab.group.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlGitlabGroup).__id, ok = v.Value.(string)
			return
		},
	"gitlab.group.id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGitlabGroup).Id, ok = plugin.RawToTValue[int64](v.Value, v.Error)
		return
	},
	"gitlab.group.name": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGitlabGroup).Name, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"gitlab.group.path": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGitlabGroup).Path, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"gitlab.group.createdAt": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGitlabGroup).CreatedAt, ok = plugin.RawToTValue[*time.Time](v.Value, v.Error)
		return
	},
	"gitlab.group.description": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGitlabGroup).Description, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"gitlab.group.webURL": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGitlabGroup).WebURL, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"gitlab.group.visibility": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGitlabGroup).Visibility, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"gitlab.group.requireTwoFactorAuthentication": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGitlabGroup).RequireTwoFactorAuthentication, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"gitlab.group.preventForkingOutsideGroup": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGitlabGroup).PreventForkingOutsideGroup, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"gitlab.group.emailsDisabled": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGitlabGroup).EmailsDisabled, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"gitlab.group.mentionsDisabled": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGitlabGroup).MentionsDisabled, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"gitlab.group.projects": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGitlabGroup).Projects, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"gitlab.project.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlGitlabProject).__id, ok = v.Value.(string)
			return
		},
	"gitlab.project.id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGitlabProject).Id, ok = plugin.RawToTValue[int64](v.Value, v.Error)
		return
	},
	"gitlab.project.name": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGitlabProject).Name, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"gitlab.project.fullName": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGitlabProject).FullName, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"gitlab.project.path": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGitlabProject).Path, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"gitlab.project.createdAt": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGitlabProject).CreatedAt, ok = plugin.RawToTValue[*time.Time](v.Value, v.Error)
		return
	},
	"gitlab.project.description": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGitlabProject).Description, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"gitlab.project.defaultBranch": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGitlabProject).DefaultBranch, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"gitlab.project.visibility": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGitlabProject).Visibility, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"gitlab.project.archived": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGitlabProject).Archived, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"gitlab.project.mirror": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGitlabProject).Mirror, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"gitlab.project.webURL": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGitlabProject).WebURL, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"gitlab.project.emailsDisabled": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGitlabProject).EmailsDisabled, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"gitlab.project.allowMergeOnSkippedPipeline": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGitlabProject).AllowMergeOnSkippedPipeline, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"gitlab.project.onlyAllowMergeIfPipelineSucceeds": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGitlabProject).OnlyAllowMergeIfPipelineSucceeds, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"gitlab.project.onlyAllowMergeIfAllDiscussionsAreResolved": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGitlabProject).OnlyAllowMergeIfAllDiscussionsAreResolved, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"gitlab.project.issuesEnabled": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGitlabProject).IssuesEnabled, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"gitlab.project.mergeRequestsEnabled": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGitlabProject).MergeRequestsEnabled, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"gitlab.project.wikiEnabled": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGitlabProject).WikiEnabled, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"gitlab.project.snippetsEnabled": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGitlabProject).SnippetsEnabled, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"gitlab.project.containerRegistryEnabled": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGitlabProject).ContainerRegistryEnabled, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"gitlab.project.serviceDeskEnabled": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGitlabProject).ServiceDeskEnabled, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"gitlab.project.packagesEnabled": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGitlabProject).PackagesEnabled, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"gitlab.project.autoDevopsEnabled": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGitlabProject).AutoDevopsEnabled, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"gitlab.project.requirementsEnabled": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGitlabProject).RequirementsEnabled, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"gitlab.project.approvalRules": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGitlabProject).ApprovalRules, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"gitlab.project.mergeMethod": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGitlabProject).MergeMethod, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"gitlab.project.approvalSettings": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGitlabProject).ApprovalSettings, ok = plugin.RawToTValue[*mqlGitlabProjectApprovalSetting](v.Value, v.Error)
		return
	},
	"gitlab.project.protectedBranches": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGitlabProject).ProtectedBranches, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"gitlab.project.projectMembers": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGitlabProject).ProjectMembers, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"gitlab.project.projectFiles": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGitlabProject).ProjectFiles, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"gitlab.project.webhooks": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGitlabProject).Webhooks, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"gitlab.project.jobsEnabled": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGitlabProject).JobsEnabled, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"gitlab.project.emptyRepo": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGitlabProject).EmptyRepo, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"gitlab.project.sharedRunnersEnabled": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGitlabProject).SharedRunnersEnabled, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"gitlab.project.groupRunnersEnabled": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGitlabProject).GroupRunnersEnabled, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"gitlab.project.approvalRule.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlGitlabProjectApprovalRule).__id, ok = v.Value.(string)
			return
		},
	"gitlab.project.approvalRule.id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGitlabProjectApprovalRule).Id, ok = plugin.RawToTValue[int64](v.Value, v.Error)
		return
	},
	"gitlab.project.approvalRule.name": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGitlabProjectApprovalRule).Name, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"gitlab.project.approvalRule.approvalsRequired": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGitlabProjectApprovalRule).ApprovalsRequired, ok = plugin.RawToTValue[int64](v.Value, v.Error)
		return
	},
	"gitlab.project.approvalSetting.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlGitlabProjectApprovalSetting).__id, ok = v.Value.(string)
			return
		},
	"gitlab.project.approvalSetting.approvalsBeforeMerge": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGitlabProjectApprovalSetting).ApprovalsBeforeMerge, ok = plugin.RawToTValue[int64](v.Value, v.Error)
		return
	},
	"gitlab.project.approvalSetting.********************": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGitlabProjectApprovalSetting).********************, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"gitlab.project.approvalSetting.disableOverridingApproversPerMergeRequest": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGitlabProjectApprovalSetting).DisableOverridingApproversPerMergeRequest, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"gitlab.project.approvalSetting.mergeRequestsAuthorApproval": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGitlabProjectApprovalSetting).MergeRequestsAuthorApproval, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"gitlab.project.approvalSetting.mergeRequestsDisableCommittersApproval": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGitlabProjectApprovalSetting).MergeRequestsDisableCommittersApproval, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"gitlab.project.approvalSetting.requirePasswordToApprove": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGitlabProjectApprovalSetting).RequirePasswordToApprove, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"gitlab.project.approvalSetting.selectiveCodeOwnerRemovals": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGitlabProjectApprovalSetting).SelectiveCodeOwnerRemovals, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"gitlab.project.protectedBranch.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlGitlabProjectProtectedBranch).__id, ok = v.Value.(string)
			return
		},
	"gitlab.project.protectedBranch.name": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGitlabProjectProtectedBranch).Name, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"gitlab.project.protectedBranch.allowForcePush": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGitlabProjectProtectedBranch).AllowForcePush, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"gitlab.project.protectedBranch.defaultBranch": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGitlabProjectProtectedBranch).DefaultBranch, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"gitlab.project.protectedBranch.codeOwnerApproval": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGitlabProjectProtectedBranch).CodeOwnerApproval, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"gitlab.project.member.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(***********************).__id, ok = v.Value.(string)
			return
		},
	"gitlab.project.member.id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(***********************).Id, ok = plugin.RawToTValue[int64](v.Value, v.Error)
		return
	},
	"gitlab.project.member.name": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(***********************).Name, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"gitlab.project.member.role": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(***********************).Role, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"gitlab.project.member.username": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(***********************).Username, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"gitlab.project.member.state": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(***********************).State, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"gitlab.project.file.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*********************).__id, ok = v.Value.(string)
			return
		},
	"gitlab.project.file.path": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*********************).Path, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"gitlab.project.file.type": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*********************).Type, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"gitlab.project.file.name": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*********************).Name, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"gitlab.project.file.content": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*********************).Content, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"gitlab.project.webhook.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlGitlabProjectWebhook).__id, ok = v.Value.(string)
			return
		},
	"gitlab.project.webhook.url": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGitlabProjectWebhook).Url, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"gitlab.project.webhook.sslVerification": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlGitlabProjectWebhook).SslVerification, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
}

func SetData(resource plugin.Resource, field string, val *llx.RawData) error {
	f, ok := setDataFields[resource.MqlName() + "." + field]
	if !ok {
		return errors.New("[gitlab] cannot set '"+field+"' in resource '"+resource.MqlName()+"', field not found")
	}

	if ok := f(resource, val); !ok {
		return errors.New("[gitlab] cannot set '"+field+"' in resource '"+resource.MqlName()+"', type does not match")
	}
	return nil
}

func SetAllData(resource plugin.Resource, args map[string]*llx.RawData) error {
	var err error
	for k, v := range args {
		if err = SetData(resource, k, v); err != nil {
			return err
		}
	}
	return nil
}

// mqlGitlabGroup for the gitlab.group resource
type mqlGitlabGroup struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlGitlabGroupInternal it will be used here
	Id plugin.TValue[int64]
	Name plugin.TValue[string]
	Path plugin.TValue[string]
	CreatedAt plugin.TValue[*time.Time]
	Description plugin.TValue[string]
	WebURL plugin.TValue[string]
	Visibility plugin.TValue[string]
	RequireTwoFactorAuthentication plugin.TValue[bool]
	PreventForkingOutsideGroup plugin.TValue[bool]
	EmailsDisabled plugin.TValue[bool]
	MentionsDisabled plugin.TValue[bool]
	Projects plugin.TValue[[]interface{}]
}

// createGitlabGroup creates a new instance of this resource
func createGitlabGroup(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlGitlabGroup{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	if res.__id == "" {
	res.__id, err = res.id()
		if err != nil {
			return nil, err
		}
	}

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("gitlab.group", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlGitlabGroup) MqlName() string {
	return "gitlab.group"
}

func (c *mqlGitlabGroup) MqlID() string {
	return c.__id
}

func (c *mqlGitlabGroup) GetId() *plugin.TValue[int64] {
	return &c.Id
}

func (c *mqlGitlabGroup) GetName() *plugin.TValue[string] {
	return &c.Name
}

func (c *mqlGitlabGroup) GetPath() *plugin.TValue[string] {
	return &c.Path
}

func (c *mqlGitlabGroup) GetCreatedAt() *plugin.TValue[*time.Time] {
	return &c.CreatedAt
}

func (c *mqlGitlabGroup) GetDescription() *plugin.TValue[string] {
	return &c.Description
}

func (c *mqlGitlabGroup) GetWebURL() *plugin.TValue[string] {
	return &c.WebURL
}

func (c *mqlGitlabGroup) GetVisibility() *plugin.TValue[string] {
	return &c.Visibility
}

func (c *mqlGitlabGroup) GetRequireTwoFactorAuthentication() *plugin.TValue[bool] {
	return &c.RequireTwoFactorAuthentication
}

func (c *mqlGitlabGroup) GetPreventForkingOutsideGroup() *plugin.TValue[bool] {
	return &c.PreventForkingOutsideGroup
}

func (c *mqlGitlabGroup) GetEmailsDisabled() *plugin.TValue[bool] {
	return &c.EmailsDisabled
}

func (c *mqlGitlabGroup) GetMentionsDisabled() *plugin.TValue[bool] {
	return &c.MentionsDisabled
}

func (c *mqlGitlabGroup) GetProjects() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.Projects, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("gitlab.group", c.__id, "projects")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.projects()
	})
}

// mqlGitlabProject for the gitlab.project resource
type mqlGitlabProject struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlGitlabProjectInternal it will be used here
	Id plugin.TValue[int64]
	Name plugin.TValue[string]
	FullName plugin.TValue[string]
	Path plugin.TValue[string]
	CreatedAt plugin.TValue[*time.Time]
	Description plugin.TValue[string]
	DefaultBranch plugin.TValue[string]
	Visibility plugin.TValue[string]
	Archived plugin.TValue[bool]
	Mirror plugin.TValue[bool]
	WebURL plugin.TValue[string]
	EmailsDisabled plugin.TValue[bool]
	AllowMergeOnSkippedPipeline plugin.TValue[bool]
	OnlyAllowMergeIfPipelineSucceeds plugin.TValue[bool]
	OnlyAllowMergeIfAllDiscussionsAreResolved plugin.TValue[bool]
	IssuesEnabled plugin.TValue[bool]
	MergeRequestsEnabled plugin.TValue[bool]
	WikiEnabled plugin.TValue[bool]
	SnippetsEnabled plugin.TValue[bool]
	ContainerRegistryEnabled plugin.TValue[bool]
	ServiceDeskEnabled plugin.TValue[bool]
	PackagesEnabled plugin.TValue[bool]
	AutoDevopsEnabled plugin.TValue[bool]
	RequirementsEnabled plugin.TValue[bool]
	ApprovalRules plugin.TValue[[]interface{}]
	MergeMethod plugin.TValue[string]
	ApprovalSettings plugin.TValue[*mqlGitlabProjectApprovalSetting]
	ProtectedBranches plugin.TValue[[]interface{}]
	ProjectMembers plugin.TValue[[]interface{}]
	ProjectFiles plugin.TValue[[]interface{}]
	Webhooks plugin.TValue[[]interface{}]
	JobsEnabled plugin.TValue[bool]
	EmptyRepo plugin.TValue[bool]
	SharedRunnersEnabled plugin.TValue[bool]
	GroupRunnersEnabled plugin.TValue[bool]
}

// createGitlabProject creates a new instance of this resource
func createGitlabProject(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlGitlabProject{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	if res.__id == "" {
	res.__id, err = res.id()
		if err != nil {
			return nil, err
		}
	}

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("gitlab.project", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlGitlabProject) MqlName() string {
	return "gitlab.project"
}

func (c *mqlGitlabProject) MqlID() string {
	return c.__id
}

func (c *mqlGitlabProject) GetId() *plugin.TValue[int64] {
	return &c.Id
}

func (c *mqlGitlabProject) GetName() *plugin.TValue[string] {
	return &c.Name
}

func (c *mqlGitlabProject) GetFullName() *plugin.TValue[string] {
	return &c.FullName
}

func (c *mqlGitlabProject) GetPath() *plugin.TValue[string] {
	return &c.Path
}

func (c *mqlGitlabProject) GetCreatedAt() *plugin.TValue[*time.Time] {
	return &c.CreatedAt
}

func (c *mqlGitlabProject) GetDescription() *plugin.TValue[string] {
	return &c.Description
}

func (c *mqlGitlabProject) GetDefaultBranch() *plugin.TValue[string] {
	return &c.DefaultBranch
}

func (c *mqlGitlabProject) GetVisibility() *plugin.TValue[string] {
	return &c.Visibility
}

func (c *mqlGitlabProject) GetArchived() *plugin.TValue[bool] {
	return &c.Archived
}

func (c *mqlGitlabProject) GetMirror() *plugin.TValue[bool] {
	return &c.Mirror
}

func (c *mqlGitlabProject) GetWebURL() *plugin.TValue[string] {
	return &c.WebURL
}

func (c *mqlGitlabProject) GetEmailsDisabled() *plugin.TValue[bool] {
	return &c.EmailsDisabled
}

func (c *mqlGitlabProject) GetAllowMergeOnSkippedPipeline() *plugin.TValue[bool] {
	return &c.AllowMergeOnSkippedPipeline
}

func (c *mqlGitlabProject) GetOnlyAllowMergeIfPipelineSucceeds() *plugin.TValue[bool] {
	return &c.OnlyAllowMergeIfPipelineSucceeds
}

func (c *mqlGitlabProject) GetOnlyAllowMergeIfAllDiscussionsAreResolved() *plugin.TValue[bool] {
	return &c.OnlyAllowMergeIfAllDiscussionsAreResolved
}

func (c *mqlGitlabProject) GetIssuesEnabled() *plugin.TValue[bool] {
	return &c.IssuesEnabled
}

func (c *mqlGitlabProject) GetMergeRequestsEnabled() *plugin.TValue[bool] {
	return &c.MergeRequestsEnabled
}

func (c *mqlGitlabProject) GetWikiEnabled() *plugin.TValue[bool] {
	return &c.WikiEnabled
}

func (c *mqlGitlabProject) GetSnippetsEnabled() *plugin.TValue[bool] {
	return &c.SnippetsEnabled
}

func (c *mqlGitlabProject) GetContainerRegistryEnabled() *plugin.TValue[bool] {
	return &c.ContainerRegistryEnabled
}

func (c *mqlGitlabProject) GetServiceDeskEnabled() *plugin.TValue[bool] {
	return &c.ServiceDeskEnabled
}

func (c *mqlGitlabProject) GetPackagesEnabled() *plugin.TValue[bool] {
	return &c.PackagesEnabled
}

func (c *mqlGitlabProject) GetAutoDevopsEnabled() *plugin.TValue[bool] {
	return &c.AutoDevopsEnabled
}

func (c *mqlGitlabProject) GetRequirementsEnabled() *plugin.TValue[bool] {
	return &c.RequirementsEnabled
}

func (c *mqlGitlabProject) GetApprovalRules() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.ApprovalRules, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("gitlab.project", c.__id, "approvalRules")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.approvalRules()
	})
}

func (c *mqlGitlabProject) GetMergeMethod() *plugin.TValue[string] {
	return plugin.GetOrCompute[string](&c.MergeMethod, func() (string, error) {
		return c.mergeMethod()
	})
}

func (c *mqlGitlabProject) GetApprovalSettings() *plugin.TValue[*mqlGitlabProjectApprovalSetting] {
	return plugin.GetOrCompute[*mqlGitlabProjectApprovalSetting](&c.ApprovalSettings, func() (*mqlGitlabProjectApprovalSetting, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("gitlab.project", c.__id, "approvalSettings")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.(*mqlGitlabProjectApprovalSetting), nil
			}
		}

		return c.approvalSettings()
	})
}

func (c *mqlGitlabProject) GetProtectedBranches() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.ProtectedBranches, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("gitlab.project", c.__id, "protectedBranches")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.protectedBranches()
	})
}

func (c *mqlGitlabProject) GetProjectMembers() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.ProjectMembers, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("gitlab.project", c.__id, "projectMembers")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.projectMembers()
	})
}

func (c *mqlGitlabProject) GetProjectFiles() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.ProjectFiles, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("gitlab.project", c.__id, "projectFiles")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.projectFiles()
	})
}

func (c *mqlGitlabProject) GetWebhooks() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.Webhooks, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("gitlab.project", c.__id, "webhooks")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.webhooks()
	})
}

func (c *mqlGitlabProject) GetJobsEnabled() *plugin.TValue[bool] {
	return &c.JobsEnabled
}

func (c *mqlGitlabProject) GetEmptyRepo() *plugin.TValue[bool] {
	return &c.EmptyRepo
}

func (c *mqlGitlabProject) GetSharedRunnersEnabled() *plugin.TValue[bool] {
	return &c.SharedRunnersEnabled
}

func (c *mqlGitlabProject) GetGroupRunnersEnabled() *plugin.TValue[bool] {
	return &c.GroupRunnersEnabled
}

// mqlGitlabProjectApprovalRule for the gitlab.project.approvalRule resource
type mqlGitlabProjectApprovalRule struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlGitlabProjectApprovalRuleInternal it will be used here
	Id plugin.TValue[int64]
	Name plugin.TValue[string]
	ApprovalsRequired plugin.TValue[int64]
}

// createGitlabProjectApprovalRule creates a new instance of this resource
func createGitlabProjectApprovalRule(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlGitlabProjectApprovalRule{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	// to override __id implement: id() (string, error)

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("gitlab.project.approvalRule", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlGitlabProjectApprovalRule) MqlName() string {
	return "gitlab.project.approvalRule"
}

func (c *mqlGitlabProjectApprovalRule) MqlID() string {
	return c.__id
}

func (c *mqlGitlabProjectApprovalRule) GetId() *plugin.TValue[int64] {
	return &c.Id
}

func (c *mqlGitlabProjectApprovalRule) GetName() *plugin.TValue[string] {
	return &c.Name
}

func (c *mqlGitlabProjectApprovalRule) GetApprovalsRequired() *plugin.TValue[int64] {
	return &c.ApprovalsRequired
}

// mqlGitlabProjectApprovalSetting for the gitlab.project.approvalSetting resource
type mqlGitlabProjectApprovalSetting struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlGitlabProjectApprovalSettingInternal it will be used here
	ApprovalsBeforeMerge plugin.TValue[int64]
	******************** plugin.TValue[bool]
	DisableOverridingApproversPerMergeRequest plugin.TValue[bool]
	MergeRequestsAuthorApproval plugin.TValue[bool]
	MergeRequestsDisableCommittersApproval plugin.TValue[bool]
	RequirePasswordToApprove plugin.TValue[bool]
	SelectiveCodeOwnerRemovals plugin.TValue[bool]
}

// createGitlabProjectApprovalSetting creates a new instance of this resource
func createGitlabProjectApprovalSetting(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlGitlabProjectApprovalSetting{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	// to override __id implement: id() (string, error)

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("gitlab.project.approvalSetting", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlGitlabProjectApprovalSetting) MqlName() string {
	return "gitlab.project.approvalSetting"
}

func (c *mqlGitlabProjectApprovalSetting) MqlID() string {
	return c.__id
}

func (c *mqlGitlabProjectApprovalSetting) GetApprovalsBeforeMerge() *plugin.TValue[int64] {
	return &c.ApprovalsBeforeMerge
}

func (c *mqlGitlabProjectApprovalSetting) Get********************() *plugin.TValue[bool] {
	return &c.********************
}

func (c *mqlGitlabProjectApprovalSetting) GetDisableOverridingApproversPerMergeRequest() *plugin.TValue[bool] {
	return &c.DisableOverridingApproversPerMergeRequest
}

func (c *mqlGitlabProjectApprovalSetting) GetMergeRequestsAuthorApproval() *plugin.TValue[bool] {
	return &c.MergeRequestsAuthorApproval
}

func (c *mqlGitlabProjectApprovalSetting) GetMergeRequestsDisableCommittersApproval() *plugin.TValue[bool] {
	return &c.MergeRequestsDisableCommittersApproval
}

func (c *mqlGitlabProjectApprovalSetting) GetRequirePasswordToApprove() *plugin.TValue[bool] {
	return &c.RequirePasswordToApprove
}

func (c *mqlGitlabProjectApprovalSetting) GetSelectiveCodeOwnerRemovals() *plugin.TValue[bool] {
	return &c.SelectiveCodeOwnerRemovals
}

// mqlGitlabProjectProtectedBranch for the gitlab.project.protectedBranch resource
type mqlGitlabProjectProtectedBranch struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlGitlabProjectProtectedBranchInternal it will be used here
	Name plugin.TValue[string]
	AllowForcePush plugin.TValue[bool]
	DefaultBranch plugin.TValue[bool]
	CodeOwnerApproval plugin.TValue[bool]
}

// createGitlabProjectProtectedBranch creates a new instance of this resource
func createGitlabProjectProtectedBranch(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlGitlabProjectProtectedBranch{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	if res.__id == "" {
	res.__id, err = res.id()
		if err != nil {
			return nil, err
		}
	}

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("gitlab.project.protectedBranch", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlGitlabProjectProtectedBranch) MqlName() string {
	return "gitlab.project.protectedBranch"
}

func (c *mqlGitlabProjectProtectedBranch) MqlID() string {
	return c.__id
}

func (c *mqlGitlabProjectProtectedBranch) GetName() *plugin.TValue[string] {
	return &c.Name
}

func (c *mqlGitlabProjectProtectedBranch) GetAllowForcePush() *plugin.TValue[bool] {
	return &c.AllowForcePush
}

func (c *mqlGitlabProjectProtectedBranch) GetDefaultBranch() *plugin.TValue[bool] {
	return &c.DefaultBranch
}

func (c *mqlGitlabProjectProtectedBranch) GetCodeOwnerApproval() *plugin.TValue[bool] {
	return &c.CodeOwnerApproval
}

// ********************** for the gitlab.project.member resource
type ********************** struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define **********************Internal it will be used here
	Id plugin.TValue[int64]
	Name plugin.TValue[string]
	Role plugin.TValue[string]
	Username plugin.TValue[string]
	State plugin.TValue[string]
}

// createGitlabProjectMember creates a new instance of this resource
func createGitlabProjectMember(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &**********************{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	if res.__id == "" {
	res.__id, err = res.id()
		if err != nil {
			return nil, err
		}
	}

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("gitlab.project.member", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c ***********************) MqlName() string {
	return "gitlab.project.member"
}

func (c ***********************) MqlID() string {
	return c.__id
}

func (c ***********************) GetId() *plugin.TValue[int64] {
	return &c.Id
}

func (c ***********************) GetName() *plugin.TValue[string] {
	return &c.Name
}

func (c ***********************) GetRole() *plugin.TValue[string] {
	return &c.Role
}

func (c ***********************) GetUsername() *plugin.TValue[string] {
	return &c.Username
}

func (c ***********************) GetState() *plugin.TValue[string] {
	return &c.State
}

// ******************** for the gitlab.project.file resource
type ******************** struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define ********************Internal it will be used here
	Path plugin.TValue[string]
	Type plugin.TValue[string]
	Name plugin.TValue[string]
	Content plugin.TValue[string]
}

// createGitlabProjectFile creates a new instance of this resource
func createGitlabProjectFile(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &********************{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	if res.__id == "" {
	res.__id, err = res.id()
		if err != nil {
			return nil, err
		}
	}

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("gitlab.project.file", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *********************) MqlName() string {
	return "gitlab.project.file"
}

func (c *********************) MqlID() string {
	return c.__id
}

func (c *********************) GetPath() *plugin.TValue[string] {
	return &c.Path
}

func (c *********************) GetType() *plugin.TValue[string] {
	return &c.Type
}

func (c *********************) GetName() *plugin.TValue[string] {
	return &c.Name
}

func (c *********************) GetContent() *plugin.TValue[string] {
	return &c.Content
}

// mqlGitlabProjectWebhook for the gitlab.project.webhook resource
type mqlGitlabProjectWebhook struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlGitlabProjectWebhookInternal it will be used here
	Url plugin.TValue[string]
	SslVerification plugin.TValue[bool]
}

// createGitlabProjectWebhook creates a new instance of this resource
func createGitlabProjectWebhook(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlGitlabProjectWebhook{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	if res.__id == "" {
	res.__id, err = res.id()
		if err != nil {
			return nil, err
		}
	}

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("gitlab.project.webhook", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlGitlabProjectWebhook) MqlName() string {
	return "gitlab.project.webhook"
}

func (c *mqlGitlabProjectWebhook) MqlID() string {
	return c.__id
}

func (c *mqlGitlabProjectWebhook) GetUrl() *plugin.TValue[string] {
	return &c.Url
}

func (c *mqlGitlabProjectWebhook) GetSslVerification() *plugin.TValue[bool] {
	return &c.SslVerification
}
