// Copyright (c) Mondoo, Inc.
// SPDX-License-Identifier: BUSL-1.1

option provider = "go.mondoo.com/cnquery/v9/providers/gitlab"
option go_package = "go.mondoo.com/cnquery/v11/providers/gitlab/resources"

// GitLab group
gitlab.group @defaults("name visibility webURL") {
  // Group ID
  id int
  // Group name
  name string
  // Group path
  path string
  // Create date of the group
  createdAt time
  // Group description
  description string
  // URL of the group
  webURL string
  // The group's visibility level: private, internal, or public
  visibility string
  // Whether all users in this group are required to set up two-factor authentication
  requireTwoFactorAuthentication bool
  // Whether forking projects outside this group is forbidden
  preventForkingOutsideGroup bool
  // Whether group email notifications are disabled
  emailsDisabled bool
  // Whether group mentions within issues and merge requests are disabled
  mentionsDisabled bool
  // List of all projects that belong to the group
  projects() []gitlab.project
}

// GitLab project
gitlab.project @defaults("fullName visibility webURL") {
  // Project ID
  id int
  // Project name
  name string
  // The full name of the project, including the namespace
  fullName string
  // Project path
  path string
  // Create date of the project
  createdAt time
  // Project description
  description string
  // Default Git branch
  defaultBranch string
  // The project's visibility level: private, internal, or public
  visibility string
  // Whether the project is archived
  archived bool
  // Whether the project is a mirror
  mirror bool
  // URL of the project
  webURL string
  // Whether project email notifications are disabled
  emailsDisabled bool
  // Whether merging merge requests is allowed when a pipeline is skipped
  allowMergeOnSkippedPipeline bool
  // Whether merging merge requests is allowed only if the pipelines succeed
  onlyAllowMergeIfPipelineSucceeds bool
  // Whether merging merge requests is allowed only if all discussions are resolved
  onlyAllowMergeIfAllDiscussionsAreResolved bool
  // Whether the issues feature is enabled
  issuesEnabled bool
  // Whether the merge request feature is enabled
  mergeRequestsEnabled bool
  // Whether the wiki feature is enabled
  wikiEnabled bool
  // Whether the snippets feature is enabled
  snippetsEnabled bool
  // Whether the container registry feature is enabled
  containerRegistryEnabled bool
  // Whether the Service Desk feature is enabled
  serviceDeskEnabled bool
  // Whether the packages feature is enabled
  packagesEnabled bool
  // Whether the Auto DevOps feature is enabled
  autoDevopsEnabled bool
  // Whether the requirements feature is enabled
  requirementsEnabled bool
  // Approval rules for the project
  approvalRules() []gitlab.project.approvalRule
  // Merge methods for the project
  mergeMethod() string
  // Approval settings for the project
  approvalSettings() gitlab.project.approvalSetting
  // Protected branches settings for the project
  protectedBranches() []gitlab.project.protectedBranch
  // List of members in the project with their roles
  projectMembers() []gitlab.project.member
  // List of files in the project repository
  projectFiles() []gitlab.project.file
  // List of webhooks for the project
  webhooks() []gitlab.project.webhook
  // Whether CI jobs are enabled
  jobsEnabled bool
  // Whether the repo is empty
  emptyRepo bool
  // Whether the project is enabled for shared runners
  sharedRunnersEnabled bool
  // Whether the project is enabled for group runners
  groupRunnersEnabled bool
}

// GitLab project approval rule
private gitlab.project.approvalRule @defaults("id name approvalsRequired") {
  // Rule ID
  id int
  // Rule name
  name string
  // Number of approvals required
  approvalsRequired int
}

// GitLab project approval settings
private gitlab.project.approvalSetting @defaults("approvalsBeforeMerge requirePasswordToApprove") {
  // Number of approvals before merge
  approvalsBeforeMerge int
  // Whether all approvals are removed when new commits are pushed
  resetApprovalsOnPush bool
  // Whether users are prevented from overriding an approver per merge request
  disableOverridingApproversPerMergeRequest bool
  // Whether author of merge request can approve
  mergeRequestsAuthorApproval bool
  // Whether users are prevented from overriding a committer's approval for merge request
  mergeRequestsDisableCommittersApproval bool
  // Whether a password is required to approve
  requirePasswordToApprove bool
  // Whether approvals are reset from Code Owners if their files changed
  selectiveCodeOwnerRemovals bool
}

// GitLab protected branch
private gitlab.project.protectedBranch @defaults("name allowForcePush") {
  // Branch name
  name string
  // Whether force push is allowed
  allowForcePush bool
  // Whether this is the default branch
  defaultBranch bool
  // Whether code owner approval required
  codeOwnerApproval bool
}

// GitLab project member
gitlab.project.member @defaults("username role name") {
  // Member ID
  id int
  // Member name
  name string
  // Member role
  role string
  // Member username
  username string
  // Member state
  state string
}

// GitLab project file
private gitlab.project.file @defaults("path type") {
  // File path
  path string
  // File type
  type string
  // File name
  name string
  // File content
  content string
}

// GitLab project webhook
private gitlab.project.webhook @defaults("url sslVerification") {
  // Webhook URL
  url string
  // Whether SSL verification is enabled
  sslVerification bool
}
