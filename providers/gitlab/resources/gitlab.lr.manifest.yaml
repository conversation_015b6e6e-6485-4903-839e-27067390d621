# Copyright (c) Mondoo, Inc.
# SPDX-License-Identifier: BUSL-1.1

resources:
  gitlab.group:
    fields:
      createdAt:
        min_mondoo_version: 9.0.0
      description: {}
      emailsDisabled:
        min_mondoo_version: 9.0.0
      id: {}
      mentionsDisabled:
        min_mondoo_version: 9.0.0
      name: {}
      path: {}
      preventForkingOutsideGroup:
        min_mondoo_version: 9.0.0
      projects: {}
      requireTwoFactorAuthentication: {}
      visibility: {}
      webURL:
        min_mondoo_version: 9.0.0
    min_mondoo_version: 5.15.0
  gitlab.project:
    fields:
      allowMergeOnSkippedPipeline:
        min_mondoo_version: 9.0.0
      approvalRules:
        min_mondoo_version: 9.0.0
      approvalSettings:
        min_mondoo_version: 9.0.0
      archived:
        min_mondoo_version: 9.0.0
      autoDevopsEnabled:
        min_mondoo_version: 9.0.0
      containerRegistryEnabled:
        min_mondoo_version: 9.0.0
      createdAt:
        min_mondoo_version: 9.0.0
      defaultBranch:
        min_mondoo_version: 9.0.0
      description: {}
      emailsDisabled:
        min_mondoo_version: 9.0.0
      emptyRepo:
        min_mondoo_version: 9.0.0
      fullName:
        min_mondoo_version: 9.0.0
      groupRunnersEnabled:
        min_mondoo_version: 9.0.0
      id: {}
      issuesEnabled:
        min_mondoo_version: 9.0.0
      jobsEnabled:
        min_mondoo_version: 9.0.0
      mergeMethod:
        min_mondoo_version: 9.0.0
      mergeRequestsEnabled:
        min_mondoo_version: 9.0.0
      mirror:
        min_mondoo_version: 9.0.0
      name: {}
      onlyAllowMergeIfAllDiscussionsAreResolved:
        min_mondoo_version: 9.0.0
      onlyAllowMergeIfPipelineSucceeds:
        min_mondoo_version: 9.0.0
      packagesEnabled:
        min_mondoo_version: 9.0.0
      path: {}
      projectFiles:
        min_mondoo_version: 9.0.0
      projectMembers:
        min_mondoo_version: 9.0.0
      protectedBranches:
        min_mondoo_version: 9.0.0
      requirementsEnabled:
        min_mondoo_version: 9.0.0
      serviceDeskEnabled:
        min_mondoo_version: 9.0.0
      sharedRunnersEnabled:
        min_mondoo_version: 9.0.0
      snippetsEnabled:
        min_mondoo_version: 9.0.0
      visibility: {}
      webURL:
        min_mondoo_version: 9.0.0
      webhooks:
        min_mondoo_version: 9.0.0
      wikiEnabled:
        min_mondoo_version: 9.0.0
    min_mondoo_version: 5.15.0
  gitlab.project.approvalRule:
    fields:
      approvalsRequired: {}
      id: {}
      name: {}
    is_private: true
    min_mondoo_version: 9.0.0
  gitlab.project.approvalSetting:
    fields:
      approvalsBeforeMerge: {}
      disableOverridingApproversPerMergeRequest: {}
      mergeRequestsAuthorApproval: {}
      mergeRequestsDisableCommittersApproval: {}
      requirePasswordToApprove: {}
      resetApprovalsOnPush: {}
      selectiveCodeOwnerRemovals: {}
    is_private: true
    min_mondoo_version: 9.0.0
  gitlab.project.file:
    fields:
      content: {}
      name: {}
      path: {}
      type: {}
    is_private: true
    min_mondoo_version: 9.0.0
  gitlab.project.member:
    fields:
      id: {}
      name: {}
      role: {}
      state: {}
      username: {}
    min_mondoo_version: 9.0.0
  gitlab.project.protectedBranch:
    fields:
      allowForcePush: {}
      codeOwnerApproval: {}
      defaultBranch: {}
      name: {}
    is_private: true
    min_mondoo_version: 9.0.0
  gitlab.project.webhook:
    fields:
      sslVerification: {}
      url: {}
    is_private: true
    min_mondoo_version: 9.0.0
