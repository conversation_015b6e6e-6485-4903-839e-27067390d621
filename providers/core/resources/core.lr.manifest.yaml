# Copyright (c) Mondoo, Inc.
# SPDX-License-Identifier: BUSL-1.1

resources:
  asset:
    fields:
      annotations:
        min_mondoo_version: 10.8.3
      arch: {}
      build: {}
      family: {}
      fqdn: {}
      ids: {}
      kind: {}
      labels: {}
      name: {}
      platform: {}
      platformMetadata:
        min_mondoo_version: 9.0.0
      runtime: {}
      title: {}
      version: {}
    min_mondoo_version: 6.13.0
  asset.eol:
    fields:
      date: {}
      docsUrl: {}
      productUrl: {}
    min_mondoo_version: latest
  cpe:
    fields:
      edition: {}
      language: {}
      other: {}
      part: {}
      product: {}
      swEdition: {}
      targetHw: {}
      targetSw: {}
      update: {}
      uri: {}
      vendor: {}
      version: {}
    min_mondoo_version: latest
  mondoo:
    fields:
      arch:
        min_mondoo_version: latest
      build: {}
      capabilities: {}
      jobEnvironment: {}
      version: {}
    min_mondoo_version: 5.15.0
  parse:
    fields: {}
    min_mondoo_version: 5.15.0
  product:
    fields:
      name: {}
      releaseCycle: {}
      version: {}
    min_mondoo_version: 9.0.0
  product.releaseCycleInformation:
    fields:
      cycle: {}
      endOfActiveSupport: {}
      endOfExtendedSupport: {}
      endOfLife: {}
      firstReleaseDate: {}
      lastReleaseDate: {}
      latestVersion: {}
      link: {}
      name: {}
    is_private: true
    min_mondoo_version: 9.0.0
  regex:
    fields:
      creditCard: {}
      email: {}
      emoji: {}
      ipv4: {}
      ipv6: {}
      mac: {}
      semver: {}
      url: {}
      uuid: {}
    min_mondoo_version: 5.15.0
  time:
    fields:
      day: {}
      hour: {}
      minute: {}
      now: {}
      second: {}
      today: {}
      tomorrow: {}
    min_mondoo_version: 5.15.0
  uuid:
    fields:
      urn: {}
      value: {}
      variant: {}
      version: {}
    min_mondoo_version: 5.15.0
  vulnerability.exchange:
    fields:
      id: {}
      source: {}
    min_mondoo_version: 9.0.0
