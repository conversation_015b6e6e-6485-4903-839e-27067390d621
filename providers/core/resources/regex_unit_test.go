// Copyright (c) Mondoo, Inc.
// SPDX-License-Identifier: BUSL-1.1

package resources

import (
	"regexp"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

var lre = mqlRegex{}

func testRegex(t *testing.T, f func() (string, error), matches, fails []string) {
	r, err := f()
	require.NoError(t, err)

	re, err := regexp.Compile("^" + r + "$")
	require.NoError(t, err)

	for i := range matches {
		s := matches[i]
		t.Run("matches "+s, func(t *testing.T) {
			assert.True(t, re.MatchString(s))
		})
	}

	for i := range fails {
		s := fails[i]
		t.Run("fails "+s, func(t *testing.T) {
			assert.False(t, re.MatchString(s))
		})
	}
}

func TestResource_RegexEmail(t *testing.T) {
	// The following tests are copied from:
	//   https://en.wikipedia.org/wiki/Email_address
	//   Example section
	matches := []string{
		"<EMAIL>",
		"<EMAIL>",
		"<EMAIL>",
		"<EMAIL>",
		"<EMAIL>",
		"<EMAIL>",
		"<EMAIL>",
		"<EMAIL>",
		"test/<EMAIL>",
		"admin@mailserver1", // local domain name with no TLD, although ICANN highly discourages dotless email addresses
		"<EMAIL>",
		"\" \"@example.org",
		"\"john..doe\"@example.org",
		"mailhost!<EMAIL>",
		"<EMAIL>",
		"<EMAIL>",
		"jsmith@[***********]",
		"jsmith@[IPv6:2001:db8::1]",
	}

	fails := []string{
		"Abc.example.com",
		"A@b@<EMAIL>",
		"a\"b(c)d,e:f;g<h>i[j\\k]<EMAIL>",
		"just\"not\"<EMAIL>",
		"this is\"not\\<EMAIL>",
		"this\\ still\\\"not\\\\<EMAIL>",
		"<EMAIL>",
		"i_like_underscore@but_its_not_allowed_in_this_part.example.com",
		"QA[icon]CHOCOLATE[icon]@test.com",
	}

	testRegex(t, lre.email, matches, fails)
}
