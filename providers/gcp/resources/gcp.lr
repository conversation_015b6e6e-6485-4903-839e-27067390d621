// Copyright (c) Mondoo, Inc.
// SPDX-License-Identifier: BUSL-1.1

option provider = "go.mondoo.com/cnquery/v9/providers/gcp"
option go_package = "go.mondoo.com/cnquery/v11/providers/gcp/resources"

alias gcloud.organization = gcp.organization
alias gcloud.project = gcp.project
alias gcloud.resourcemanager.binding = gcp.resourcemanager.binding
alias gcp.compute = gcp.project.computeService
alias gcloud.compute = gcp.project.computeService
alias gcloud.compute.instance = gcp.project.computeService.instance
alias gcloud.compute.serviceaccount = gcp.project.computeService.serviceaccount
alias gcloud.storage = gcp.project.storageService
alias gcloud.storage.bucket = gcp.project.storageService.bucket
alias gcloud.sql = gcp.project.sqlService
alias gcp.sql = gcp.project.sqlService
alias gcloud.sql.instance = gcp.project.sqlService.instance
alias gcp.dns = gcp.project.dnsService
alias gcp.bigquery = gcp.project.bigqueryService
alias gcp.storage = gcp.project.storageService

// Google Cloud (GCP) organization
gcp.organization @defaults("name") {
  // Organization ID
  id string
  // Organization name
  name() string
  // Organization state
  state() string
  // Deprecated. Use `state` instead.
  lifecycleState() string
  // Organization IAM policy
  iamPolicy() []gcp.resourcemanager.binding
  // Access approval settings
  accessApprovalSettings() gcp.accessApprovalSettings
  // List of folders
  folders() gcp.folders
  // List of projects
  projects() gcp.projects
}

// Google Cloud (GCP) folders
private gcp.folders {
  []gcp.folder
  // Parent ID
  parentId string
  // List of the children folders only (non-recursive)
  children() []gcp.folder
}

// Google Cloud (GCP) Redis
private gcp.project.redisService {
  // Project ID
  projectId string
  // List all redis instances
  instances() []gcp.project.redisService.instance
}

// Google Cloud (GCP) Redis instance
private gcp.project.redisService.instance @defaults("name") {
  // Unique name of the resource in this scope including project and location
  name string
  // Project ID
  projectId string
  // An arbitrary and optional user-provided name for the instance
  displayName string
  // Resource labels to represent user provided metadata
  labels map[string]string
  // The zone where the instance will be provisioned
  locationId string
  // The version of Redis software
  redisVersion string
  // The CIDR range of internal addresses that are reserved for this instance
  reservedIpRange string
  // Additional IP range for node placement
  secondaryIpRange string
  // Hostname or IP address of the exposed Redis endpoint used by clients to connect to the service
  host string
  // The port number of the exposed Redis endpoint
  port int
  // The current zone where the Redis primary node is located
  currentLocationId string
  // The time the instance was created
  createTime time
  // The current state of this instance
  state string
  // Additional information about the current status of this instance, if available
  statusMessage string
  // Redis configuration parameters, according to http://redis.io/topics/config
  redisConfigs map[string]string
  // Redis memory size in GiB
  memorySizeGb int
  // The full name of the Google Compute Engine
  // [network](https://cloud.google.com/vpc/docs/vpc) to which the
  // instance is connected. If left unspecified, the `default` network
  // will be used
  AuthorizedNetwork string
  // Cloud IAM identity used by import / export operations to transfer data to/from Cloud Storage
  persistenceIamIdentity string
  // The network connect mode of the Redis instance
  connectMode string
  // Redis AUTH is enabled or not for the instance. If set to "true" AUTH is enabled on the instance
  authEnabled bool
  // The number of replica nodes
  replicaCount int
  // Info per node
  nodes []gcp.project.redisService.instance.nodeInfo
  // Hostname or IP address of the exposed readonly Redis endpoint
  readEndpoint string
  // The port number of the exposed readonly redis endpoint
  readEndpointPort int
  // The KMS key reference that the customer provides when trying to create the instance
  customerManagedKey string
  // The self service update maintenance version
  maintenanceVersion string
  // The available maintenance versions that an instance could update to
  availableMaintenanceVersions []string
}

// Google Cloud (GCP) Redis instance node information
private gcp.project.redisService.instance.nodeInfo @defaults("id zone") {
  // Project ID
  projectId string
  // Node identifying string (e.g. `node-0`, `node-1`)
  id string
  // Location of the node
  zone string
}

// Google Cloud (GCP) folder
private gcp.folder @defaults("name") {
  // Folder ID
  id string
  // Folder name
  name string
  // Creation timestamp
  created time
  // Update timestamp
  updated time
  // Parent ID
  parentId string
  // Folder state
  state string
  // List of folders
  folders() gcp.folders
  // List of projects
  projects() gcp.projects
}

// Google Cloud (GCP) projects
private gcp.projects {
  []gcp.project
  // Parent ID
  parentId string
  // List of the children projects only (non-recursive)
  children() []gcp.project
}

// Google Cloud (GCP) project
gcp.project @defaults("name") {
  // Unique, user-assigned ID of the project
  id string
  // Unique resource name
  name() string
  // Parent ID
  parentId() string
  // Deprecated. Use `id` instead.
  number() string
  // Project lifecycle state
  state() string
  // Deprecated. Use `state` instead.
  lifecycleState() string
  // Creation time
  createTime() time
  // Labels associated with this project
  labels() map[string]string
  // IAM policy
  iamPolicy() []gcp.resourcemanager.binding
  // List of available and enabled services for the project
  services() []gcp.service
  // List of recommendations
  recommendations() []gcp.recommendation
  // GCP GKE resources
  gke() gcp.project.gkeService
  // GCP Compute resources for the project
  compute() gcp.project.computeService
  // GCP pub/sub-related resources
  pubsub() gcp.project.pubsubService
  // KMS-related resources
  kms() gcp.project.kmsService
  // GCP contacts for the project
  essentialContacts() []gcp.essentialContact
  // API keys
  apiKeys() []gcp.project.apiKey
  // Logging resources
  logging() gcp.project.loggingservice
  // GCP Cloud SQL resources
  sql() gcp.project.sqlService
  // GCP IAM resources
  iam() gcp.project.iamService
  // Common instance metadata for the project
  commonInstanceMetadata() map[string]string
  // GCP Cloud DNS
  dns() gcp.project.dnsService
  // GCP BigQuery resources
  bigquery() gcp.project.bigqueryService
  // GCP Cloud Functions
  cloudFunctions() []gcp.project.cloudFunction
  // GCP Dataproc resources
  dataproc() gcp.project.dataprocService
  // GCP Cloud Run resources
  cloudRun() gcp.project.cloudRunService
  // Access approval settings
  accessApprovalSettings() gcp.accessApprovalSettings
  // GCP Storage resources
  storage() gcp.project.storageService
  // Monitoring resources
  monitoring() gcp.project.monitoringService
  // Binary Authorization resources
  binaryAuthorization() gcp.project.binaryAuthorizationControl
  // GCP Redis resources
  redis() gcp.project.redisService
}

// Google Cloud (GCP) service
gcp.service @defaults("name") {
  // Project ID
  projectId string
  // Service name
  name string
  // Service parent name
  parentName string
  // Service title
  title string
  // Service state
  state string
  // Whether the service is enabled
  enabled() bool
}

// Google Cloud (GCP) recommendation and suggested action
gcp.recommendation {
  // ID of recommendation
  id string
  // Project ID
  projectId string
  // Zone name
  zoneName string
  // Description of the recommendation
  name string
  // Recommender
  recommender string
  // The primary impact that this recommendation can have
  primaryImpact dict
  // Optional set of additional impact that this recommendation can have
  additionalImpact []dict
  // Recommended changes to resources
  content dict
  // Category of primary impact
  category string
  // Recommendation's priority
  priority string
  // Last time this recommendation was refreshed
  lastRefreshTime time
  // State and metadata of recommendation
  state dict
}

// Google Cloud (GCP) Resource Manager binding
private gcp.resourcemanager.binding {
  // Internal ID
  id string
  // Principals requesting access for a Google Cloud resource
  members []string
  // Role assigned to the list of members or principals
  role string
}

// Google Cloud (GCP) Compute Engine
private gcp.project.computeService {
  // Project ID
  projectId string
  // Whether the service is enabled
  enabled() bool
  // Google Compute Engine instances in a project
  instances() []gcp.project.computeService.instance
  // Google Compute Engine snapshots in a project
  snapshots() []gcp.project.computeService.snapshot
  // Google Compute Engine disks in a project
  disks() []gcp.project.computeService.disk
  // Google Compute Engine images in a project
  images() []gcp.project.computeService.image
  // Google Compute Engine firewalls in a project
  firewalls() []gcp.project.computeService.firewall
  // Google Compute Engine VPC network in a project
  networks() []gcp.project.computeService.network
  // Logical partition of a VPC network
  subnetworks() []gcp.project.computeService.subnetwork
  // Cloud Routers in a project
  routers() []gcp.project.computeService.router
  // Google Compute Engine machine types in a project
  machineTypes() []gcp.project.computeService.machineType
  // Project regions
  regions() []gcp.project.computeService.region
  // Project zones
  zones() []gcp.project.computeService.zone
  // List of backend services
  backendServices() []gcp.project.computeService.backendService
  // List of IP addresses
  addresses() []gcp.project.computeService.address
  // List of forwarding rules
  forwardingRules() []gcp.project.computeService.forwardingRule
}

// Google Cloud (GCP) Compute address
private gcp.project.computeService.address @defaults("name address addressType") {
  // Unique identifier
  id string
  // Static IP address
  address string
  // Address type
  addressType string
  // Creation timestamp
  created time
  // Address description
  description string
  // IP version
  ipVersion string
  // Endpoint type
  ipv6EndpointType string
  // Address name
  name string
  // URL of the network in which to reserve the address
  networkUrl string
  // Network in which to reserve the address
  network() gcp.project.computeService.network
  // Network tier used for configuring this address
  networkTier string
  // Prefix length if the resource represents an IP range
  prefixLength int
  // Address purpose
  purpose string
  // Region URL
  regionUrl string
  // Address status
  status string
  // URL of the subnetwork in which to reserve the address
  subnetworkUrl string
  // Subnetwork in which to reserve the address
  subnetwork() gcp.project.computeService.subnetwork
  // URLs of the resources that are using this address
  resourceUrls []string
}

// Google Cloud (GCP) Compute forwarding rules
private gcp.project.computeService.forwardingRule {
  // Unique identifier
  id string
  // IP address for which this forwarding rule accepts traffic
  ipAddress string
  // IP protocol to which this rule applies
  ipProtocol string
  // Whether to use all ports for forwarding traffic
  allPorts bool
  // Whether to allow access to the load balancer from all regions
  allowGlobalAccess bool
  // Backend service to which the forwarding rule sends traffic
  backendService string
  // Creation timestamp
  created time
  // Optional resource description
  description string
  // IP version that this forwarding rule uses
  ipVersion string
  // Whether this load balancer can be used as a collector for packet mirroring
  isMirroringCollector bool
  // User-defined labels
  labels map[string]string
  // Forwarding rule type
  loadBalancingScheme string
  // Opaque filter criteria used by the load balancer to restrict routing configuration to a limited set of xDS-compliant clients
  metadataFilters []dict
  // Forwarding rule name
  name string
  // URL of the network used for internal load balancing
  networkUrl string
  // Network used for internal load balancing
  network() gcp.project.computeService.network
  // Network tier used for configuring this load balancer
  networkTier string
  // Whether the forwarding rule should try to auto-generate a DNS zone
  noAutomateDnsZone bool
  // Port range to forward
  portRange string
  // Ports to forward
  ports []string
  // Region URL
  regionUrl string
  // Service Directory resources with which to register this forwarding rule
  serviceDirectoryRegistrations []dict
  // Optional prefix to the service name for this forwarding rule
  serviceLabel string
  // Internal fully qualified service name for this forwarding rule
  serviceName string
  // URL of the subnetwork to which the load balanced IP belongs
  subnetworkUrl string
  // Subnetwork to which the load balanced IP belongs
  subnetwork() gcp.project.computeService.subnetwork
  // URL of the target resource to receive the matched traffic
  targetUrl string
}

// Google Cloud (GCP) Compute region
private gcp.project.computeService.region @defaults("name") {
  // Unique identifier
  id string
  // Name of the resource
  name string
  // Resource description
  description string
  // Status of the region
  status string
  // Creation timestamp
  created time
  // Quotas assigned to this region
  quotas map[string]float
  // Deprecation status
  deprecated dict
}

// Google Cloud (GCP) Compute zone
private gcp.project.computeService.zone @defaults("name") {
  // Unique identifier
  id string
  // Name of the resource
  name string
  // Resource description
  description string
  // Status of the zone
  status string
  // Creation timestamp
  created time
}

// Google Cloud (GCP) machine type
private gcp.project.computeService.machineType @defaults("name") {
  // Unique identifier
  id string
  // Project ID
  projectId string
  // Name of the resource
  name string
  // Resource Description
  description string
  // Number of virtual CPUs that are available to the instance
  guestCpus int
  // Whether the machine has a shared CPU
  isSharedCpu bool
  // Maximum persistent disks allowed
  maximumPersistentDisks int
  // Maximum total persistent disks size (GB) allowed
  maximumPersistentDisksSizeGb int
  // Physical memory available to the instance (MB)
  memoryMb int
  // Creation timestamp
  created time
  // The zone where the machine type resides
  zone gcp.project.computeService.zone
}

// Google Cloud (GCP) Compute instances
private gcp.project.computeService.instance @defaults("name") {
  // Unique identifier for the instance
  id string
  // Project ID
  projectId string
  // User-friendly name for this instance
  name string
  // User-friendly name for this instance
  description string
  // Confidential instance configuration
  confidentialInstanceConfig dict
  // Whether the instance is allowed to send and receive packets with non-matching destination or source IPs
  canIpForward bool
  // CPU platform used by this instance
  cpuPlatform string
  // Creation timestamp
  created time
  // Whether the instance is protected against deletion
  deletionProtection bool
  // Whether the instance has display enabled
  enableDisplay bool
  // Attached list of accelerator cards
  guestAccelerators []dict
  // Instance fingerprint
  fingerprint string
  // Hostname of the instance
  hostname string
  // KeyRevocationActionType of the instance
  keyRevocationActionType string
  // User-provided labels
  labels map[string]string
  // Last start timestamp
  lastStartTimestamp time
  // Last stop timestamp
  lastStopTimestamp time
  // Last suspended timestamp
  lastSuspendedTimestamp time
  // Instance metadata
  metadata map[string]string
  // Minimum CPU platform for the VM instance
  minCpuPlatform string
  // Network configurations for the instance
  networkInterfaces []dict
  // Private IPv6 google access type for the VM
  privateIpv6GoogleAccess string
  // Reservations from which this instance can consume
  reservationAffinity dict
  // Resource policies applied to this instance
  resourcePolicies []string
  // Resource status for physical host
  physicalHostResourceStatus string
  // Scheduling options
  scheduling dict
  // Whether Shielded Instance integrity monitoring is enabled
  enableIntegrityMonitoring bool
  // Whether Shielded Instance secure boot is enabled
  enableSecureBoot bool
  // Whether Shielded Instance vTPM is enabled
  enableVtpm bool
  // Whether VM has been restricted from starting because Compute Engine has detected suspicious activity
  startRestricted bool
  // Instance status
  status string
  // Human-readable explanation of the status
  statusMessage string
  // Source machine image
  sourceMachineImage string
  // Tags associated with this instance
  tags []string
  // Network performance configuration
  totalEgressBandwidthTier string
  // Service accounts authorized for this instance
  serviceAccounts []gcp.project.computeService.serviceaccount
  // Disks associated with the instance
  disks []gcp.project.computeService.attachedDisk
  // Machine type
  machineType() gcp.project.computeService.machineType
  // Instance zone
  zone gcp.project.computeService.zone
}

// Google Cloud (GCP) Compute service account
private gcp.project.computeService.serviceaccount @defaults("email") {
  // Service account email address
  email string
  // Service account scopes
  scopes []string
}

// Google Cloud (GCP) Compute persistent disk
private gcp.project.computeService.disk @defaults("name") {
  // Unique identifier for the resource
  id string
  // User-friendly name for this disk
  name string
  // The architecture of the disk
  architecture string
  // Optional description
  description string
  // Features to enable on the guest operating
  guestOsFeatures []string
  // Labels to apply to this disk
  labels map[string]string
  // Last attach timestamp
  lastAttachTimestamp time
  // Last detach timestamp
  lastDetachTimestamp time
  // Publicly visible licenses
  licenses []string
  // An opaque location hint
  locationHint string
  // Physical block size of the persistent disk
  physicalBlockSizeBytes int
  // How many IOPS to provision for the disk
  provisionedIops int
  // Size, in GB, of the persistent disk
  sizeGb int
  // The status of disk creation
  status string
  // Disk zone
  zone gcp.project.computeService.zone
  // Creation timestamp
  created time
  // Disk encryption key
  diskEncryptionKey dict
}

// Google Cloud (GCP) Compute attached disk
private gcp.project.computeService.attachedDisk {
  // Attached Disk ID
  id string
  // Project ID
  projectId string
  // Architecture of the attached disk
  architecture string
  // Whether the disk will be auto-deleted
  autoDelete bool
  // Whether this is a boot disk
  boot bool
  // Unique device name
  deviceName string
  // Size of the disk in GB
  diskSizeGb int
  // Whether to force attach the regional disk
  forceAttach bool
  // Features to enable on the guest operating
  guestOsFeatures []string
  // Index to this disk
  index int
  // Disk interface
  interface string
  // Publicly visible licenses
  licenses []string
  // Mode in which to the disk is attached
  mode string
  // Attached Persistent Disk resource
  source() gcp.project.computeService.disk
  // Disk Type
  type string
}

// Google Cloud (GCP) Compute persistent disk snapshot
private gcp.project.computeService.snapshot @defaults("name") {
  // Unique identifier
  id string
  // Name of the resource
  name string
  // Optional description
  description string
  // Architecture of the snapshot
  architecture string
  // Whether the snapshot was automatically created
  autoCreated bool
  // Snapshot chain
  chainName string
  // Size in bytes of the snapshot at creation time
  creationSizeBytes int
  // Size of the source disk, specified in GB
  diskSizeGb int
  // Number of bytes downloaded to restore a snapshot to a disk
  downloadBytes int
  // Size of the storage used by the snapshot
  storageBytes int
  // An indicator whether storageBytes is in a stable state or in storage reallocation
  storageBytesStatus string
  // Snapshot type
  snapshotType string
  // Public visible licenses
  licenses []string
  // Snapshot Labels
  labels map[string]string
  // Creation timestamp
  created time
  // The status of the snapshot
  status string
}

// Google Cloud (GCP) Compute
private gcp.project.computeService.image @defaults("id name") {
  // Unique identifier
  id string
  // Project ID
  projectId string
  // Name of the resource
  name string
  // Optional description
  description string
  // Architecture of the snapshot
  architecture string
  // Size of the image tar.gz archive stored in Google Cloud Storage (in bytes)
  archiveSizeBytes int
  // Size of the image when restored onto a persistent disk (in GB)
  diskSizeGb int
  // The name of the image family to which this image belongs
  family string
  // Public visible licenses
  licenses []string
  // Snapshot Labels
  labels map[string]string
  // Creation timestamp
  created time
  // The status of the image
  status string
}

// Google Cloud (GCP) Compute firewall
private gcp.project.computeService.firewall @defaults("name") {
  // Unique identifier
  id string
  // Project ID
  projectId string
  // User-provided name
  name string
  // An optional description of this resource
  description string
  // Priority for this rule
  priority int
  //  Direction of traffic
  direction string
  // Whether the firewall rule is disabled
  disabled bool
  // Source ranges
  sourceRanges []string
  // Source service accounts
  sourceServiceAccounts []string
  // Source tags
  sourceTags []string
  // Range of destination IP addresses for which the rule applies to traffic
  destinationRanges []string
  // List of service accounts
  targetServiceAccounts []string
  // Creation timestamp
  created time
  // List of ALLOW rules specified by this firewall
  allowed []dict
  // List of DENY rules specified by this firewall
  denied []dict
}

// Google Cloud (GCP) Compute VPC network resource
private gcp.project.computeService.network @defaults("name") {
  // Unique identifier
  id string
  // Project ID
  projectId string
  // Name of the resource
  name string
  // An optional description of this resource
  description string
  // If not set, indicates a legacy network
  autoCreateSubnetworks bool
  // Whether ULA internal IPv6 is enabled on this network
  enableUlaInternalIpv6 bool
  // Gateway address for default routing
  gatewayIPv4 string
  // Maximum transmission unit size in bytes
  mtu int
  // Network firewall policy enforcement order
  networkFirewallPolicyEnforcementOrder string
  // Creation timestamp
  created time
  // Network peerings for the resource
  peerings []dict
  // The network-wide routing mode to use
  routingMode string
  // Network mode: legacy, custom or auto
  mode string
  // List of URLs for the subnetwork in the network
  subnetworkUrls []string
  // Subnetworks in the network
  subnetworks() []gcp.project.computeService.subnetwork
}

// Google Cloud (GCP) Compute VPC network partitioning
private gcp.project.computeService.subnetwork @defaults("name") {
  // Unique identifier
  id string
  // Project ID
  projectId string
  // Name of the resource
  name string
  // An optional description of this resource
  description string
  // Whether flow logging is enabled for the subnetwork
  enableFlowLogs bool
  // External IPv6 address range
  externalIpv6Prefix string
  // Fingerprint of this resource
  fingerprint string
  // Gateway address for default routes
  gatewayAddress string
  // Internal IPv6 address range
  internalIpv6Prefix string
  // Range of internal addresses
  ipCidrRange string
  // Access type of IPv6 address
  ipv6AccessType string
  // Range of internal IPv6 addresses
  ipv6CidrRange string
  // VPC flow logging configuration
  logConfig gcp.project.computeService.subnetwork.logConfig
  // Whether VMs in this subnet can access Google services without assigned external IP addresses
  privateIpGoogleAccess bool
  // VMs in this subnet that can access Google services without assigned external IPv6 addresses
  privateIpv6GoogleAccess string
  // Purpose of the resource
  purpose string
  // Region
  region() gcp.project.computeService.region
  // Region URL
  regionUrl string
  // Role of subnetwork
  role string
  // Stack type for the subnet
  stackType string
  // State of the subnetwork
  state string
  // Creation timestamp
  created time
}

// Google Cloud (GCP) Compute VPC network partitioning log configuration
private gcp.project.computeService.subnetwork.logConfig @defaults("enable") {
  // Internal ID
  id string
  // Toggles the aggregation interval for collecting flow logs
  aggregationInterval string
  // Whether to enable flow logging for this subnetwork
  enable bool
  // Which VPC flow logs are exported to Cloud Logging
  filterExpression string
  // Sampling rate of VPC flow logs within the subnetwork (1.0 means all collected logs are reported and 0.0 means no logs are reported.)
  flowSampling float
  // Whether all, none, or a subset of metadata are added to the reported VPC flow logs
  metadata string
  // Metadata fields to be added to the reported VPC flow logs
  metadataFields []string
}

// Google Cloud (GCP) Compute cloud router
private gcp.project.computeService.router @defaults("name")  {
  // Unique identifier
  id string
  // Name of the resource
  name string
  // An optional description of this resource
  description string
  // BGP information
  bgp dict
  // BGP routing stack configuration to establish BGP peering
  bgpPeers []dict
  // Whether a router is dedicated for use with encrypted VLAN attachments
  encryptedInterconnectRouter bool
  // NAT services created in this router
  nats []dict
  // Creation timestamp
  created time
}

// Google Cloud (GCP) Compute backend service
private gcp.project.computeService.backendService @defaults("name") {
  // Unique identifier
  id string
  // Lifetime of cookies in seconds
  affinityCookieTtlSec int
  // List of backends that serve this backend service
  backends []gcp.project.computeService.backendService.backend
  // Cloud CDN configuration
  cdnPolicy gcp.project.computeService.backendService.cdnPolicy
  // Circuit breakers
  circuitBreakers dict
  // Compress text responses using Brotli or gzip compression, based on the client's Accept-Encoding header
  compressionMode string
  // Connection draining configuration
  connectionDraining dict
  // Connection tracking configuration
  connectionTrackingPolicy dict
  // Consistent hash-based load balancing used to provide soft session affinity based on HTTP headers, cookies or other properties
  consistentHash dict
  // Creation timestamp
  created time
  // Headers that the load balancer adds to proxied requests
  customRequestHeaders []string
  // Headers that the load balancer adds to proxied responses
  customResponseHeaders []string
  // Backend service description
  description string
  // Resource URL for the edge security policy associated with this backend service
  edgeSecurityPolicy string
  // Whether to enable Cloud CDN
  enableCDN bool
  // Failover policy
  failoverPolicy dict
  // List of URLs to the health checks
  healthChecks []string
  // Identity-aware proxy configuration
  iap dict
  // Load balancer type
  loadBalancingScheme string
  // List of locality load balancing policies to be used in order of preference
  localityLbPolicies []dict
  // Load balancing algorithm used within the scope of the locality
  localityLbPolicy string
  // Log configuration
  logConfig dict
  // Default maximum duration (timeout) for streams to this service
  maxStreamDuration time
  // Backend service name
  name string
  // URL to the network to which this backend service belongs
  networkUrl string
  // Named port on a backend instance group representing the port for communication to the backend VMs in that group
  portName string
  // Protocol used for communication
  protocol string
  // Region URL
  regionUrl string
  // Security policy URL
  securityPolicyUrl string
  // Security settings
  securitySettings dict
  // Service binding URLs
  serviceBindingUrls []string
  // Session affinity type
  sessionAffinity string
  // Backend service timeout in settings
  timeoutSec int
}

// Google Cloud (GCP) Compute backend service backend
private gcp.project.computeService.backendService.backend @defaults("description") {
  // Internal ID
  id string
  // How to determine whether the backend of a load balancer can handle additional traffic or is fully loaded
  balancingMode string
  // Multiplier applied to the backend's target capacity of its balancing mode
  capacityScaler float
  // Backend description
  description string
  // Whether this is a failover backend
  failover bool
  // Fully-qualified URL of an instance group or network endpoint group determining what types of backends a load balancer supports
  groupUrl string
  // Maximum number of simultaneous connections
  maxConnections int
  // Maximum number of simultaneous connections per endpoint
  maxConnectionsPerEndpoint int
  // Maximum number of simultaneous connections per instance
  maxConnectionsPerInstance int
  // Maximum number of HTTP requests per second
  maxRate int
  // Maximum number for requests per second per endpoint
  maxRatePerEndpoint float
  // Maximum number for requests per second per instance
  maxRatePerInstance float
  // Target capacity for the utilization balancing mode
  maxUtilization float
}

// Google Cloud (GCP) Compute backend service CDN policy
private gcp.project.computeService.backendService.cdnPolicy {
  // Internal ID
  id string
  // Bypass the cache when the specified request headers are matched
  bypassCacheOnRequestHeaders []dict
  // Cache key policy
  cacheKeyPolicy dict
  // Cache mode for all responses from this backend
  cacheMode string
  // Client maximum TTL
  clientTtl int
  // Default TTL for cached content
  defaultTtl int
  // Maximum allowed TTL for cached content
  maxTtl int
  // Whether negative caching allows per-status code TTLs to be set in order to apply fine-grained caching for common errors or redirects
  negativeCaching bool
  // Negative caching policy
  negativeCachingPolicy []dict
  // Whether Cloud CDN combines multiple concurrent cache fill requests into a small number of requests to the origin
  requestCoalescing bool
  // Serve existing content from the cache when revalidating content with the origin
  serveWhileStale int
  // Maximum number of seconds the response to a signed URL request is considered fresh
  signedUrlCacheMaxAgeSec int
  // Names of the keys for signing request URLs
  signedUrlKeyNames []string
}

// Google Cloud (GCP) Storage
private gcp.project.storageService {
  // Project ID
  projectId string
  // List all buckets
  buckets() []gcp.project.storageService.bucket
}

// Google Cloud (GCP) Storage bucket
private gcp.project.storageService.bucket @defaults("id") {
  // Bucket ID
  id string
  // Project ID
  projectId string
  // Bucket name
  name string
  // User-defined labels
  labels map[string]string
  // Bucket location
  location string
  // Bucket location type
  locationType string
  // Project number
  projectNumber string
  // Default storage class
  storageClass string
  // Creation timestamp
  created time
  // Update timestamp
  updated time
  // IAM policy
  iamPolicy() []gcp.resourcemanager.binding
  // IAM configuration
  iamConfiguration dict
  // Retention policy
  retentionPolicy dict
  // Encryption
  encryption dict
  // Lifecycle configuration
  lifecycle []gcp.project.storageService.bucket.lifecycleRule
}

// Google Cloud bucket's lifecycle configuration
private gcp.project.storageService.bucket.lifecycleRule {
  // The action to take
  action gcp.project.storageService.bucket.lifecycleRuleAction
  // The condition(s) under which the action will be taken
  condition gcp.project.storageService.bucket.lifecycleRuleCondition
}

// A lifecycle management rule, which is made of an action to take and
// the condition(s) under which the action will be taken
private gcp.project.storageService.bucket.lifecycleRuleAction @defaults("type storageClass") {
  // Target storage class. Required iff the type of the action is SetStorageClass
  storageClass string
  // Type of the action. Currently, only Delete, SetStorageClass, and
  // AbortIncompleteMultipartUpload are supported
  type string
}

// The condition(s) under which the action will be taken
private gcp.project.storageService.bucket.lifecycleRuleCondition @defaults("age numNewerVersions") {
  // Age of an object (in days). This condition is satisfied when an object
  // reaches the specified age
  age int
  // CreatedBefore: A date in RFC 3339 format with only the date part (for
  // instance, "2013-01-15"). This condition is satisfied when an object is
  // created before midnight of the specified date in UTC
  createdBefore string
  // CustomTimeBefore: A date in RFC 3339 format with only the date part (for
  // instance, "2013-01-15"). This condition is satisfied when the custom time on
  // an object is before this date in UTC
  customTimeBefore string
  // DaysSinceCustomTime: Number of days elapsed since the user-specified
  // timestamp set on an object. The condition is satisfied if the days elapsed
  // is at least this number. If no custom timestamp is specified on an object,
  // the condition does not apply
  daysSinceCustomTime int
  // DaysSinceNoncurrentTime: Number of days elapsed since the noncurrent
  // timestamp of an object. The condition is satisfied if the days elapsed is at
  // least this number. This condition is relevant only for versioned objects.
  // The value of the field must be a nonnegative integer. If it's zero, the
  // object version will become eligible for Lifecycle action as soon as it
  // becomes noncurrent
  daysSinceNoncurrentTime int
  // IsLive: Relevant only for versioned objects. If the value is true, this
  // condition matches live objects; if the value is false, it matches archived
  // objects
  isLive bool
  // MatchesPattern: A regular expression that satisfies the RE2 syntax. This
  // condition is satisfied when the name of the object matches the RE2 pattern
  matchesPattern string
  // MatchesPrefix: List of object name prefixes. This condition will be
  // satisfied when at least one of the prefixes exactly matches the beginning of
  // the object name
  matchesPrefix []string
  // MatchesStorageClass: Objects having any of the storage classes specified by
  // this condition will be matched
  matchesStorageClass []string
  // MatchesSuffix: List of object name suffixes. This condition will be
  // satisfied when at least one of the suffixes exactly matches the end of the
  // object name
  matchesSuffix []string
  // NoncurrentTimeBefore: A date in RFC 3339 format with only the date part (for
  // instance, "2013-01-15"). This condition is satisfied when the noncurrent
  // time on an object is before this date in UTC. This condition is relevant
  // only for versioned objects
  noncurrentTimeBefore string
  // NumNewerVersions: Relevant only for versioned objects. If the value is N,
  // this condition is satisfied when there are at least N versions (including
  // the live version) newer than this version of the object
  numNewerVersions int
}

// Google Cloud (GCP) SQL resources
private gcp.project.sqlService {
  // Project ID
  projectId string
  // List of Cloud SQL instances in the current project
  instances() []gcp.project.sqlService.instance
}

// Google Cloud (GCP) SQL instance
private gcp.project.sqlService.instance @defaults("name") {
  // Project ID
  projectId string
  // All maintenance versions applicable on the instance
  availableMaintenanceVersions []string
  // Backend type
  backendType string
  // Connection name of the instance used in connection strings
  connectionName string
  // Creation timestamp
  created time
  // Deprecated
  currentDiskSize int
  // Current database version running on the instance
  databaseInstalledVersion string
  // Database engine type and version
  databaseVersion string
  // Disk encryption configuration
  diskEncryptionConfiguration dict
  // Disk encryption status
  diskEncryptionStatus dict
  // Name and status of the failover replica
  failoverReplica dict
  // Compute Engine zone that the instance is currently serviced from
  gceZone string
  // Instance type
  instanceType string
  // Assigned IP addresses
  ipAddresses []gcp.project.sqlService.instance.ipMapping
  // Current software version on the instance
  maintenanceVersion string
  // Name of the instance that acts as primary in the replica
  masterInstanceName string
  // Maximum disk size in bytes
  maxDiskSize int
  // Instance name
  name string
  // This is deprecated; use projectId instead.
  project string
  // Region
  region string
  // Replicas
  replicaNames []string
  // Settings
  settings gcp.project.sqlService.instance.settings
  // Service account email address
  serviceAccountEmailAddress string
  // Instance state
  state string
  // List of the databases in the current SQL instance
  databases() []gcp.project.sqlService.instance.database
}

// Google Cloud (GCP) SQL instance database
private gcp.project.sqlService.instance.database @defaults("name") {
  // Project ID
  projectId string
  // Character set value
  charset string
  // Collation
  collation string
  // Name of the Cloud SQL instance
  instance string
  // Name of the database
  name string
  // SQL Server database details
  sqlserverDatabaseDetails dict
}

// Google Cloud (GCP) SQL instance IP mapping
private gcp.project.sqlService.instance.ipMapping @defaults("ipAddress") {
  // Internal ID
  id string
  // Assigned IP address
  ipAddress string
  // Due time for this IP to retire
  timeToRetire time
  // Type of this IP address
  type string
}

// Google Cloud (GCP) SQL instance settings
private gcp.project.sqlService.instance.settings {
  // Project ID
  projectId string
  // Instance name
  instanceName string
  // When the instance is activated
  activationPolicy string
  // Entra ID (formerly Active Directory) configuration (relevant only for Cloud SQL for SQL Server)
  activeDirectoryConfig dict
  // Availability type
  availabilityType string
  // Daily backup configuration for the instance
  backupConfiguration gcp.project.sqlService.instance.settings.backupconfiguration
  // Name of the server collation
  collation string
  // Whether connections must use Cloud SQL connectors
  connectorEnforcement string
  // Whether database flags for crash-safe replication are enabled
  crashSafeReplicationEnabled bool
  // Size of the data disk, in GB
  dataDiskSizeGb int
  // Type of the data disk
  dataDiskType string
  // Database flags passed to the instance at startup
  databaseFlags map[string]string
  // Whether replication is enabled
  databaseReplicationEnabled bool
  // Whether to protect against accidental instance deletion
  deletionProtectionEnabled bool
  // Deny maintenance periods
  denyMaintenancePeriods []gcp.project.sqlService.instance.settings.denyMaintenancePeriod
  // Insights configuration
  insightsConfig dict
  // IP management settings
  ipConfiguration gcp.project.sqlService.instance.settings.ipConfiguration
  // Location preference settings
  locationPreference dict
  // Maintenance window
  maintenanceWindow gcp.project.sqlService.instance.settings.maintenanceWindow
  // Local user password validation policy
  passwordValidationPolicy gcp.project.sqlService.instance.settings.passwordValidationPolicy
  // Pricing plan
  pricingPlan string
  // Replication type
  replicationType string
  // Instance settings version
  settingsVersion int
  // SQL-server-specific audit configuration
  sqlServerAuditConfig dict
  // Whether to increase storage size automatically
  storageAutoResize bool
  // Maximum size to which storage capacity can be automatically increased
  storageAutoResizeLimit int
  // Service tier for this instance
  tier string
  // Server timezone
  timeZone string
  // User-provided labels
  userLabels map[string]string
}

// Google Cloud (GCP) SQL instance settings backup configuration
private gcp.project.sqlService.instance.settings.backupconfiguration {
  // Internal ID
  id string
  // Backup retention settings
  backupRetentionSettings dict
  // Whether binary log is enabled
  binaryLogEnabled bool
  // Whether this configuration is enabled
  enabled bool
  // Location of the backup
  location string
  // Whether point-in-time recovery is enabled
  pointInTimeRecoveryEnabled bool
  // Start time for the daily backup configuration (in UTC timezone, in the 24 hour format)
  startTime string
  // Number of days of transaction logs retained for point-in-time restore
  transactionLogRetentionDays int
}

// Google Cloud (GCP) SQL instance settings deny maintenance period
private gcp.project.sqlService.instance.settings.denyMaintenancePeriod @defaults("startDate endDate") {
  // Internal ID
  id string
  // Deny maintenance period end date
  endDate string
  // Deny maintenance period start date
  startDate string
  // Time in UTC when the deny maintenance period starts and ends
  time string
}

// Google Cloud (GCP) SQL instance settings IP configuration
private gcp.project.sqlService.instance.settings.ipConfiguration {
  // Internal ID
  id string
  // Name of the allocated IP range for the private IP Cloud SQL instance
  allocatedIpRange string
  // List of external networks that are allowed to connect to the instance using the IP
  authorizedNetworks []dict
  // Whether the instance is assigned a public IP address
  ipv4Enabled bool
  // Resource link for the VPC network from which the private IPs can access the Cloud SQL instance
  privateNetwork string
  // Whether SSL connections over IP are enforced
  requireSsl bool
  // Specifies how SSL/TLS is enforced in database connections.
  sslMode string
  // Whether the service uses an internal direct path instead of the private IP address inside of the Virtual Private Cloud (Controls connectivity to private IP instances from Google services, such as BigQuery)
  enablePrivatePathForGoogleCloudServices bool
}

// Google Cloud (GCP) SQL instance settings maintenance window
private gcp.project.sqlService.instance.settings.maintenanceWindow @defaults("day hour") {
  // Internal ID
  id string
  // Day of week (1-7, 1 is Monday)
  day int
  // Hour of day (0 to 23)
  hour int
  // Maintenance time setting: canary (earlier) or stable (later)
  updateTrack string
}

// Google Cloud (GCP) SQL instance settings password validation policy
private gcp.project.sqlService.instance.settings.passwordValidationPolicy @defaults("enabledPasswordPolicy") {
  // Internal ID
  id string
  // Password complexity
  complexity string
  // Whether username is forbidden as a part of the password
  disallowUsernameSubstring bool
  // Whether the password policy is enabled
  enabledPasswordPolicy bool
  // Minimum number of characters required in passwords
  minLength int
  // Minimum interval after which the password can be changed
  passwordChangeInterval string
  // Number of previous passwords that cannot be reused
  reuseInterval int
}

// Google Cloud (GCP) BigQuery resources
private gcp.project.bigqueryService @defaults("projectId") {
  // Project ID
  projectId string
  // List of BigQuery datasets
  datasets() []gcp.project.bigqueryService.dataset
}

// Google Cloud (GCP) BigQuery dataset
private gcp.project.bigqueryService.dataset @defaults("id name") {
  // Dataset ID
  id string
  // Project ID
  projectId string
  // User-friendly name for this dataset
  name string
  // User-friendly description of this dataset
  description string
  // Geo location of the dataset
  location string
  // User-provided labels
  labels map[string]string
  // Creation timestamp
  created time
  // Modified timestamp
  modified time
  // Tags associated with this dataset
  tags map[string]string
  // Cloud KMS encryption key that will be used to protect BigQuery table
  kmsName string
  // Access permissions
  access []gcp.project.bigqueryService.dataset.accessEntry
  // Returns tables in the dataset
  tables() []gcp.project.bigqueryService.table
  // Returns models in the dataset
  models() []gcp.project.bigqueryService.model
  // Returns routines in the dataset
  routines() []gcp.project.bigqueryService.routine
}

// Google Cloud (GCP) BigQuery dataset access entry
private gcp.project.bigqueryService.dataset.accessEntry @defaults("role entity entityType") {
  // Internal ID
  id string
  // Dataset ID
  datasetId string
  // Role of the entity
  role string
  // Type of the entity
  entityType string
  // Entity (individual or group) granted access
  entity string
  // View granted access (entityType must be ViewEntity)
  viewRef dict
  // Routine granted access (only UDF currently supported)
  routineRef dict
  // Resources within a dataset granted access
  datasetRef dict
}

// Google Cloud (GCP) BigQuery table
private gcp.project.bigqueryService.table @defaults("id") {
  // Table ID
  id string
  // Project ID
  projectId string
  // Dataset ID
  datasetId string
  // The user-friendly name for the table
  name string
  // Location of the table
  location string
  // User-friendly description of the table
  description string
  // User-provided labels
  labels map[string]string
  // Whether the view query uses Legacy SQL
  useLegacySQL bool
  // Whether queries that reference this table must specify a partition filter
  requirePartitionFilter bool
  // Creation timestamp
  created time
  // Modified timestamp
  modified time
  // Size of the table in bytes
  numBytes int
  // Number of bytes in the table considered "long-term storage" for reduced billing purposes
  numLongTermBytes int
  // Number of rows of data in this table
  numRows int
  // Table Type
  type string
  // Time when this table expires
  expirationTime time
  // Cloud KMS encryption key that is used to protect BigQuery table
  kmsName string
  // Snapshot creation time
  snapshotTime time
  // Query to use for a logical view
  viewQuery string
  // Data clustering configuration
  clusteringFields dict
  // Information about table stored outside of BigQuery.
  externalDataConfig dict
  // Information for materialized views
  materializedView dict
  // Integer-range-based partitioning on a table
  rangePartitioning dict
  // Time-based date partitioning on a table
  timePartitioning dict
  // Table schema
  schema []dict
}

// Google Cloud (GCP) BigQuery ML model
private gcp.project.bigqueryService.model @defaults("id") {
  // Model ID
  id string
  // Dataset ID
  datasetId string
  // Project ID
  projectId string
  // User-friendly name of the model
  name string
  // Geographic location
  location string
  // User-friendly description of the model
  description string
  // User-provided labels
  labels map[string]string
  // Creation timestamp
  created time
  // Modified timestamp
  modified time
  // Type of the mode
  type string
  // Expiration time of the model
  expirationTime time
  // Cloud KMS encryption key that is used to protect BigQuery model
  kmsName string
}

// Google Cloud (GCP) BigQuery routine
private gcp.project.bigqueryService.routine @defaults("id") {
  // Routine ID
  id string
  // Dataset ID
  datasetId string
  // Project ID
  projectId string
  // Language of the routine, such as SQL or JAVASCRIPT
  language string
  // User-friendly description of the routine
  description string
  // Creation timestamp
  created time
  // Modified timestamp
  modified time
  // Type of routine
  type string
}

// Google Cloud (GCP) DNS
private gcp.project.dnsService {
  // Project ID
  projectId string
  // Cloud DNS managed zone in project
  managedZones() []gcp.project.dnsService.managedzone
  // Cloud DNS rules in project
  policies() []gcp.project.dnsService.policy
}

// Google Cloud (GCP) DNS managed zone (a resource that represents a DNS zone hosted by the Cloud DNS service)
private gcp.project.dnsService.managedzone @defaults("name") {
  // Managed zone ID
  id string
  // Project ID
  projectId string
  // User-friendly name of the resource
  name string
  // User-friendly description of the resource
  description string
  // DNSSEC configuration
  dnssecConfig dict
  // DNS name of this managed zone
  dnsName string
  // Optionally specifies the name server set for this managed zone
  nameServerSet string
  // Delegated to these virtual name servers
  nameServers []string
  // Zone's visibility
  visibility string
  // Creation timestamp
  created time
  // Cloud DNS record set in the zone
  recordSets() []gcp.project.dnsService.recordset
}

// Google Cloud (GCP) DNS record set
private gcp.project.dnsService.recordset @defaults("name") {
  // Project ID
  projectId string
  // User-friendly name of the resource
  name string
  // Rrdatas: As defined in RFC 1035 (section 5) and RFC 1034 (section 3.6.1)
  rrdatas []string
  // SignatureRrdatas: As defined in RFC 4034
  signatureRrdatas []string
  // Number of seconds that this resource record set can be cached by resolvers
  ttl int
  // The identifier of a supported record type
  type string
}

// Google Cloud (GCP) DNS rules applied to one or more Virtual Private Cloud resources
private gcp.project.dnsService.policy @defaults("name") {
  // Project ID
  projectId string
  // Managed Zone ID
  id string
  // User-friendly name of the resource
  name string
  // User-friendly description of the resource
  description string
  // Whether DNS queries sent by VMs or applications over VPN connections are allowed
  enableInboundForwarding bool
  // Whether logging is enabled
  enableLogging bool
  // List of network names specifying networks to which this policy is applied
  networkNames []string
  // List of networks to which this policy is applied
  networks() []gcp.project.computeService.network
}

// Google Kubernetes Engine (GKE)
private gcp.project.gkeService {
  // Project ID
  projectId string
  // List of GKE clusters in the current project
  clusters() []gcp.project.gkeService.cluster
}

// Google Kubernetes Engine (GKE) cluster
private gcp.project.gkeService.cluster @defaults("name description zone status currentMasterVersion") {
  // Project ID
  projectId string
  // Unique identifier for the cluster
  id string
  // The name of the cluster
  name string
  // Optional description for the cluster
  description string
  // The logging service the cluster should use to write logs
  loggingService string
  // The monitoring service the cluster should use to write metrics
  monitoringService string
  // The name of the Google Compute Engine network to which the cluster is connected
  network string
  // The IP address range of the container pods in this cluster
  clusterIpv4Cidr string
  // The name of the Google Compute Engine subnetwork to which the cluster is connected
  subnetwork string
  // The list of node pools for the cluster
  nodePools []gcp.project.gkeService.cluster.nodepool
  // The list of Google Compute Engine zones in which the cluster's nodes should be located
  locations []string
  // Whether Kubernetes alpha features are enabled
  enableKubernetesAlpha bool
  // Whether Autopilot is enabled for the cluster
  autopilotEnabled bool
  // Deprecated; use location instead
  zone string
  // Name of the Google Compute Engine zone/region in which the cluster exists
  location string
  // The IP address of the cluster's master endpoint
  endpoint string
  // The initial Kubernetes version for this cluster
  initialClusterVersion string
  // The current software version of the master endpoint
  currentMasterVersion string
  // The current status of this cluster
  status string
  // The resource labels for the cluster to use to annotate any related Google Compute Engine resources
  resourceLabels map[string]string
  // Creation time
  created time
  // The time the cluster will be automatically deleted in
  expirationTime time
  // Configurations for the various addons available to run in the cluster
  addonsConfig gcp.project.gkeService.cluster.addonsConfig
  // Configuration for the use of Kubernetes Service Accounts in GCP IAM policies
  workloadIdentityConfig dict
  // Configuration for cluster IP allocation
  ipAllocationPolicy gcp.project.gkeService.cluster.ipAllocationPolicy
  // Configuration for cluster networking
  networkConfig gcp.project.gkeService.cluster.networkConfig
  // Binary authorization configuration
  binaryAuthorization dict
  // Legacy ABAC authorization configuration
  legacyAbac dict
  // Authentication information for accessing the master endpoint
  masterAuth dict
  // Master authorized networks configuration
  masterAuthorizedNetworksConfig dict
  // Private cluster configuration
  privateClusterConfig dict
  // Etcd encryption configuration
  databaseEncryption dict
  // Configuration for Shielded Nodes feature
  shieldedNodesConfig dict
  // Configuration for the fine-grained cost management feature
  costManagementConfig dict
  // Configuration of Confidential Nodes
  confidentialNodesConfig dict
  // Configuration for Identity Service component
  identityServiceConfig dict
  // Configuration options for the NetworkPolicy feature
  networkPolicyConfig dict
  // The release channel that the cluster is subscribed to
  releaseChannel string
}

// Google Kubernetes Engine (GKE) cluster addons config
private gcp.project.gkeService.cluster.addonsConfig {
  // Internal ID
  id string
  // Configuration for the HTTP (L7) load balancing controller addon
  httpLoadBalancing dict
  // Configuration for the horizontal pod autoscaling feature
  horizontalPodAutoscaling dict
  // Configuration for the Kubernetes Dashboard
  kubernetesDashboard dict
  // Configuration for NetworkPolicy
  networkPolicyConfig dict
  // Configuration for the Cloud Run addon
  cloudRunConfig dict
  // Configuration for NodeLocalDNS, a DNS cache running on cluster nodes
  dnsCacheConfig dict
  // Configuration for the ConfigConnector addon
  configConnectorConfig dict
  // Configuration for the Compute Engine Persistent Disk CSI driver
  gcePersistentDiskCsiDriverConfig dict
  // Configuration for the GCP Filestore CSI driver
  gcpFilestoreCsiDriverConfig dict
  // Configuration for the backup for GKE agent addon
  gkeBackupAgentConfig dict
  // Configuration for the Cloud Storage Fuse CSI driver
  gcsFuseCsiDriverConfig dict
  // Configuration for the StatefulHA add-on.
  statefulHaConfig dict
}

// Google Kubernetes Engine (GKE) cluster IP allocation policy
private gcp.project.gkeService.cluster.ipAllocationPolicy {
  // Internal ID
  id string
  // Whether alias IPs are used for pod IPs in the cluster
  useIpAliases bool
  // Whether a new subnetwork is created automatically for the cluster
  createSubnetwork bool
  // Custom subnetwork name to be used if createSubnetwork is true
  subnetworkName string
  // Name of the secondary range to be used for the cluster CIDR block
  clusterSecondaryRangeName string
  // Name of the secondary range to be used for the services CIDR block
  servicesSecondaryRangeName string
  // IP address range for the cluster pod IPs
  clusterIpv4CidrBlock string
  // IP address range of the instance IPs in this cluster
  nodeIpv4CidrBlock string
  // IP address range of the services IPs in this cluster
  servicesIpv4CidrBlock string
  // IP address range of the Cloud TPUs in this cluster
  tpuIpv4CidrBlock string
  // Whether routes will be used for pod IPs in this cluster
  useRoutes bool
  // IP stack type
  stackType string
  // IPv6 access type
  ipv6AccessType string
}

// Google Kubernetes Engine (GKE) cluster network config
private gcp.project.gkeService.cluster.networkConfig @defaults("networkPath") {
  // Internal ID
  id string
  // Relative path of the network to which the cluster is connected
  networkPath string
  // Network to which the cluster is connected
  network() gcp.project.computeService.network
  // Relative path of the subnetwork to which the cluster is connected
  subnetworkPath string
  // Subnetwork to which the cluster is connected
  subnetwork() gcp.project.computeService.subnetwork
  // Whether intra-node visibility is enabled for this cluster
  enableIntraNodeVisibility bool
  // Whether the cluster disables default in-node sNAT rules
  defaultSnatStatus dict
  // Whether L4ILB subsetting is enabled for this cluster
  enableL4IlbSubsetting bool
  // Desired datapath provider for this cluster
  datapathProvider string
  // Desired state of IPv6 connectivity to Google Services
  privateIpv6GoogleAccess string
  // Cluster DNS configuration
  dnsConfig dict
  // Configuration specifying whether services with externalIPs field are blocked
  serviceExternalIpsConfig dict
  // Whether multi-networking is enabled for this cluster.
  enableMultiNetworking bool
  // Whether FQDN Network Policy is enabled on this cluster.
  enableFqdnNetworkPolicy bool
  // Whether CiliumClusterwideNetworkPolicy is enabled on this cluster.
  enableCiliumClusterwideNetworkPolicy bool
}

// Google Kubernetes Engine (GKE) cluster node pool
private gcp.project.gkeService.cluster.nodepool @defaults("name") {
  // Internal ID
  id string
  // The name of the node pool
  name string
  // The node configuration of the pool
  config gcp.project.gkeService.cluster.nodepool.config
  // The initial node count for the pool
  initialNodeCount int
  // The list of Google Compute Engine zones in which the NodePool's nodes should be located
  locations []string
  // Networking configuration for this node pool
  networkConfig gcp.project.gkeService.cluster.nodepool.networkConfig
  // The Kubernetes version
  version string
  // The resource URLs of the managed instance groups associated with this node pool
  instanceGroupUrls []string
  // The current status of this node pool
  status string
  // Node management configuration
  management dict
  // Autoscaler configuration for this NodePool
  autoscaling gcp.project.gkeService.cluster.nodepool.autoscaling
}

private gcp.project.gkeService.cluster.nodepool.autoscaling @defaults( "enabled" ) {
  // Is autoscaling enabled for this node pool.
  enabled bool
  // Minimum number of nodes for one location in the node pool. Must be greater
  // than or equal to 0 and less than or equal to max_node_count.
  minNodeCount int
  // Maximum number of nodes for one location in the node pool. Must be >=
  // min_node_count. There has to be enough quota to scale up the cluster.
  maxNodeCount int
  // Can this node pool be deleted automatically.
  autoprovisioned bool
  // Minimum number of nodes in the node pool. Must be greater than or equal
  // to 0 and less than or equal to total_max_node_count.
  // The total_*_node_count fields are mutually exclusive with the *_node_count
  // fields.
  totalMinNodeCount int
  // Maximum number of nodes in the node pool. Must be greater than or equal to
  // total_min_node_count. There has to be enough quota to scale up the cluster.
  // The total_*_node_count fields are mutually exclusive with the *_node_count
  // fields.
  totalMaxNodeCount int
}

// Google Kubernetes Engine (GKE) node pool-Level network configuration
private gcp.project.gkeService.cluster.nodepool.networkConfig @defaults("podRange podIpv4CidrBlock") {
  // Internal ID
  id string
  // The ID of the secondary range for pod IPs
  podRange string
  // The IP address range for pod IPs in this node pool
  podIpv4CidrBlock string
  // Network performance tier configuration
  performanceConfig gcp.project.gkeService.cluster.nodepool.networkConfig.performanceConfig
}

// Google Kubernetes Engine (GKE) node pool network performance configuration
private gcp.project.gkeService.cluster.nodepool.networkConfig.performanceConfig @defaults("totalEgressBandwidthTier") {
  // Internal ID
  id string
  // Specifies the total network bandwidth tier for the node pool
  totalEgressBandwidthTier string
}

// Google Kubernetes Engine (GKE) node pool configuration
private gcp.project.gkeService.cluster.nodepool.config @defaults("machineType diskSizeGb") {
  // Internal ID
  id string
  // Project ID
  projectId string
  // The name of a Google Compute Engine machine type
  machineType string
  // Size of the disk attached to each node, specified in GB
  diskSizeGb int
  // The set of Google API scopes to be made available on all of the node VMs under the "default" service account
  oauthScopes []string
  // Email of the Google Cloud Platform Service Account to be used by the node VMs
  serviceAccountEmail string
  // Google Cloud Platform Service Account to be used by the node VMs
  serviceAccount() gcp.project.iamService.serviceAccount
  // The metadata key/value pairs assigned to instances in the cluster
  metadata map[string]string
  // The image type to use for this node
  imageType string
  // The map of Kubernetes labels to be applied to each node
  labels map[string]string
  // The number of local SSD disks to be attached to the node
  localSsdCount int
  // The list of instance tags applied to all nodes
  tags []string
  // Whether the nodes are created as preemptible VM instances.
  preemptible bool
  // A list of hardware accelerators to attach to each node
  accelerators []gcp.project.gkeService.cluster.nodepool.config.accelerator
  // Type of the disk attached to each node
  diskType string
  // Minimum CPU platform to be used by this instance
  minCpuPlatform string
  // The workload metadata mode for this node
  workloadMetadataMode string
  // List of Kubernetes taints to be applied to each node
  taints []gcp.project.gkeService.cluster.nodepool.config.nodeTaint
  // Sandbox configuration for this node
  sandboxConfig gcp.project.gkeService.cluster.nodepool.config.sandboxConfig
  // Shielded instance configuration
  shieldedInstanceConfig gcp.project.gkeService.cluster.nodepool.config.shieldedInstanceConfig
  // Parameters that can be configured on Linux nodes
  linuxNodeConfig gcp.project.gkeService.cluster.nodepool.config.linuxNodeConfig
  // Node kubelet configs
  kubeletConfig gcp.project.gkeService.cluster.nodepool.config.kubeletConfig
  // The Customer Managed Encryption Key used to encrypt the boot disk attached to each node
  bootDiskKmsKey string
  // Google Container File System (image streaming) configuration
  gcfsConfig gcp.project.gkeService.cluster.nodepool.config.gcfsConfig
  // Advanced features for the Compute Engine VM
  advancedMachineFeatures gcp.project.gkeService.cluster.nodepool.config.advancedMachineFeatures
  // gVNIC configuration
  gvnicConfig gcp.project.gkeService.cluster.nodepool.config.gvnicConfig
  // Whether Spot VM is enabled (a rebrand of the existing preemptible flag)
  spot bool
  // Confidential nodes configuration
  confidentialNodes gcp.project.gkeService.cluster.nodepool.config.confidentialNodes
}

// Google Kubernetes Engine (GKE) node pool hardware accelerators configuration
private gcp.project.gkeService.cluster.nodepool.config.accelerator @defaults("type count") {
  // Internal ID
  id string
  // The number of the accelerator cards exposed to an instance
  count int
  // The accelerator type resource name
  type string
  // Size of partitions to create on the GPU
  gpuPartitionSize string
  // The configuration for GPU sharing
  gpuSharingConfig gcp.project.gkeService.cluster.nodepool.config.accelerator.gpuSharingConfig
}

// Google Kubernetes Engine (GKE) GPU sharing configuration
private gcp.project.gkeService.cluster.nodepool.config.accelerator.gpuSharingConfig @defaults("strategy") {
  // Internal ID
  id string
  // The max number of containers that can share a GPU
  maxSharedClientsPerGpu int
  // The GPU sharing strategy
  strategy string
}

// Google Kubernetes Engine (GKE) Kubernetes node taint
private gcp.project.gkeService.cluster.nodepool.config.nodeTaint @defaults("key value effect") {
  // Internal ID
  id string
  // Key for the taint
  key string
  // Value for the taint
  value string
  // Effect for the taint
  effect string
}

// Google Kubernetes Engine (GKE) node pool sandbox configuration
private gcp.project.gkeService.cluster.nodepool.config.sandboxConfig @defaults("type") {
  // Internal ID
  id string
  // Type of the sandbox to use for this node
  type string
}

// Google Kubernetes Engine (GKE) node pool shielded instance configuration
private gcp.project.gkeService.cluster.nodepool.config.shieldedInstanceConfig @defaults("enableSecureBoot enableIntegrityMonitoring") {
  // Internal ID
  id string
  // Whether the instance has Secure Boot enabled
  enableSecureBoot bool
  // Whether the instance has integrity monitoring enabled
  enableIntegrityMonitoring bool
}

// Google Kubernetes Engine (GKE) node pool parameters that can be configured on Linux nodes
private gcp.project.gkeService.cluster.nodepool.config.linuxNodeConfig @defaults("sysctls") {
  // Internal ID
  id string
  // The Linux kernel parameters to apply to the nodes and all pods running on them
  sysctls map[string]string
}

// Google Kubernetes Engine (GKE) Node Pool kubelet configuration
private gcp.project.gkeService.cluster.nodepool.config.kubeletConfig @defaults("cpuManagerPolicy podPidsLimit") {
  // Internal ID
  id string
  // Control the CPU management policy on the node
  cpuManagerPolicy string
  // Set the CPU CFS quota period value 'cpu.cfs_period_us'
  cpuCfsQuotaPeriod string
  // Set the Pod PID limits
  podPidsLimit int
}

// Google Kubernetes Engine (GKE) node pool GCFS configuration
private gcp.project.gkeService.cluster.nodepool.config.gcfsConfig @defaults("enabled") {
  // Internal ID
  id string
  // Whether to use GCFS
  enabled bool
}

// Google Kubernetes Engine (GKE) node pool advanced machine features configuration
private gcp.project.gkeService.cluster.nodepool.config.advancedMachineFeatures @defaults("threadsPerCore") {
  // Internal ID
  id string
  // Number of threads per physical core (if unset, assumes the maximum number of threads supported per core by the underlying processor)
  threadsPerCore int
}

// Google Kubernetes Engine (GKE) node pool gVNIC configuration
private gcp.project.gkeService.cluster.nodepool.config.gvnicConfig @defaults("enabled") {
  // Internal ID
  id string
  // Whether to use gVNIC
  enabled bool
}

// Google Kubernetes Engine (GKE) node pool confidential nodes configuration
private gcp.project.gkeService.cluster.nodepool.config.confidentialNodes @defaults("enabled") {
  // Internal ID
  id string
  // Whether to use confidential nodes
  enabled bool
}

// Google Cloud (GCP) Pub/Sub resources
private gcp.project.pubsubService {
  // Project ID
  projectId string
  // List of topics in the current project
  topics() []gcp.project.pubsubService.topic
  // List of subscriptions in the current project
  subscriptions() []gcp.project.pubsubService.subscription
  // List of snapshots in the current project
  snapshots() []gcp.project.pubsubService.snapshot
}

// Google Cloud (GCP) Pub/Sub topic
private gcp.project.pubsubService.topic @defaults("name") {
  // Project ID
  projectId string
  // Topic name
  name string
  // Topic configuration
  config() gcp.project.pubsubService.topic.config
}

// Google Cloud (GCP) Pub/Sub topic configuration
private gcp.project.pubsubService.topic.config @defaults("kmsKeyName messageStoragePolicy") {
  // Project ID
  projectId string
  // Topic name
  topicName string
  // Labels associated with this topic
  labels map[string]string
  // Cloud KMS key used to protect access to messages published to the topic
  kmsKeyName string
  // Message storage policy
  messageStoragePolicy gcp.project.pubsubService.topic.config.messagestoragepolicy
}

// Google Cloud (GCP) Pub/Sub topic message storage policy
private gcp.project.pubsubService.topic.config.messagestoragepolicy @defaults("allowedPersistenceRegions"){
  // Parent configuration ID
  configId string
  // List of GCP regions where messages published to the topic can persist in storage
  allowedPersistenceRegions []string
}

// Google Cloud (GCP) Pub/Sub subscription
private gcp.project.pubsubService.subscription @defaults("name") {
  // Project ID
  projectId string
  // Subscription name
  name string
  // Subscription configuration
  config() gcp.project.pubsubService.subscription.config
}

// Google Cloud (GCP) Pub/Sub subscription configuration
private gcp.project.pubsubService.subscription.config @defaults("topic.name ackDeadline expirationPolicy") {
  // Project ID
  projectId string
  // Subscription name
  subscriptionName string
  // Topic to which the subscription points
  topic gcp.project.pubsubService.topic
  // Configuration for subscriptions that operate in push mode
  pushConfig gcp.project.pubsubService.subscription.config.pushconfig
  // Default maximum time a subscriber can take to acknowledge a message after receiving it
  ackDeadline time
  // Whether to retain acknowledged messages
  retainAckedMessages bool
  // How long to retain messages in the backlog after they're published
  retentionDuration time
  // Conditions for a subscription's expiration
  expirationPolicy time
  // The labels associated with this subscription
  labels map[string]string
}

// GCP Pub/Sub configuration for subscriptions that operate in push mode
private gcp.project.pubsubService.subscription.config.pushconfig @defaults("attributes") {
  // Parent configuration ID
  configId string
  // URL of the endpoint to which to push messages
  endpoint string
  // Endpoint configuration attributes
  attributes map[string]string
}

// Google Cloud (GCP) Pub/Sub snapshot
private gcp.project.pubsubService.snapshot @defaults("name") {
  // Project ID
  projectId string
  // Subscription name
  name string
  // The topic for which the snapshot is
  topic gcp.project.pubsubService.topic
  // When the snapshot expires
  expiration time
}
// GCP KMS resources
private gcp.project.kmsService {
  // Project ID
  projectId string
  // Available locations for the service
  locations() []string
  // List of keyrings in the current project
  keyrings() []gcp.project.kmsService.keyring
}

// Google Cloud (GCP) KMS keyring
private gcp.project.kmsService.keyring @defaults("name") {
  // Project ID
  projectId string
  // Full resource path
  resourcePath string
  // Keyring name
  name string
  // Time created
  created time
  // Keyring location
  location string
  // List of cryptokeys in the current keyring
  cryptokeys() []gcp.project.kmsService.keyring.cryptokey
}

// Google Cloud (GCP) KMS crypto key
private gcp.project.kmsService.keyring.cryptokey @defaults("name purpose") {
  // Full resource path
  resourcePath string
  // Crypto key name
  name string
  // Primary version for encrypt to use for this crypto key
  primary gcp.project.kmsService.keyring.cryptokey.version
  // Crypto key purpose
  purpose string
  // Creation timestamp
  created time
  // Time at which KMS will create a new version of this key and mark it as primary
  nextRotation time
  // Rotation period
  rotationPeriod time
  // Template describing the settings for new crypto key versions
  versionTemplate dict
  // User-defined labels
  labels map[string]string
  // Whether this key can contain imported versions only
  importOnly bool
  // Period of time that versions of this key spend in DESTROY_SCHEDULED state before being destroyed
  destroyScheduledDuration time
  // Resource name of the backend environment where the key material for all crypto key versions reside
  cryptoKeyBackend string
  // List of cryptokey versions
  versions() []gcp.project.kmsService.keyring.cryptokey.version
  // Crypto key IAM policy
  iamPolicy() []gcp.resourcemanager.binding
}

// Google Cloud (GCP) KMS crypto key version
private gcp.project.kmsService.keyring.cryptokey.version @defaults("name state") {
  // Full resource path
  resourcePath string
  // Crypto key version name
  name string
  // Crypto key version's current state
  state string
  // Protection level describing how crypto operations perform with this crypto key version
  protectionLevel string
  // Algorithm that the crypto key version supports
  algorithm string
  // Statement generated and signed by HSM at key creation time
  attestation gcp.project.kmsService.keyring.cryptokey.version.attestation
  // Time created
  created time
  // Time generated
  generated time
  // Time destroyed
  destroyed time
  // Destroy event timestamp
  destroyEventTime time
  // Name of the import job used in the most recent import of the crypto key version
  importJob string
  // Time at which this crypto key version's key material was imported
  importTime time
  // The root cause of an import failure
  importFailureReason string
  // Additional fields for configuring external protection level
  externalProtectionLevelOptions gcp.project.kmsService.keyring.cryptokey.version.externalProtectionLevelOptions
  // Whether the crypto key version is eligible for reimport
  reimportEligible bool
}

// GCP KMS crypto key version attestation
private gcp.project.kmsService.keyring.cryptokey.version.attestation {
  // Crypto key version name
  cryptoKeyVersionName string
  // Format of the attestation data
  format string
  // Certificate chains needed to validate the attestation
  certificateChains gcp.project.kmsService.keyring.cryptokey.version.attestation.certificatechains
}

// Google Cloud (GCP) KMS crypto key version attestation certificate chains
private gcp.project.kmsService.keyring.cryptokey.version.attestation.certificatechains {
  // Crypto key version name
  cryptoKeyVersionName string
  // Cavium certificate chain corresponding to the attestation
  caviumCerts []string
  // Google card certificate chain corresponding to the attestation
  googleCardCerts []string
  // Google partition certificate chain corresponding to the attestation
  googlePartitionCerts []string
}

// Google Cloud (GCP) KMS crypto key version external protection level options
private gcp.project.kmsService.keyring.cryptokey.version.externalProtectionLevelOptions {
  // Crypto key version name
  cryptoKeyVersionName string
  // URI for an external resource that the crypto key version represents
  externalKeyUri string
  // Path to the external key material on the EKM when using EKM connection
  ekmConnectionKeyPath string
}

// Google Cloud (GCP) contact
private gcp.essentialContact @defaults("email notificationCategories") {
  // Full resource path
  resourcePath string
  // Email address to send notifications to
  email string
  // Preferred language for notifications, as a ISO 639-1 language code
  languageTag string
  // Categories of notifications that the contact will receive communication for
  notificationCategories []string
  // Last time the validation state was updated
  validated time
  // Validity of the contact
  validationState string
}

// Google Cloud (GCP) project API key
private gcp.project.apiKey @defaults("name") {
  // The ID of the key
  id string
  // Project ID
  projectId string
  // Human-readable display name of this key
  name string
  // Full resource path
  resourcePath string
  // Annotations
  annotations map[string]string
  // Creation timestamp
  created time
  // Deletion timestamp
  deleted time
  // Encrypted and signed value held by this key
  keyString string
  // API key restrictions
  restrictions gcp.project.apiKey.restrictions
  // Update timestamp
  updated time
}

// Google Cloud (GCP) project API key restrictions
private gcp.project.apiKey.restrictions {
  // Parent resource path
  parentResourcePath string
  // The Android apps that are allowed to use the key
  androidKeyRestrictions dict
  // A restriction for a specific service and optionally one or more specific methods
  apiTargets []dict
  // The HTTP referrers that are allowed to use the key
  browserKeyRestrictions dict
  // The iOS apps that are allowed to use the key
  iosKeyRestrictions dict
  // The IP addresses that are allowed to use the key
  serverKeyRestrictions dict
}

// Google Cloud (GCP) Logging resources
private gcp.project.loggingservice {
  // Project ID
  projectId string
  // List of logging buckets
  buckets() []gcp.project.loggingservice.bucket
  // List of metrics
  metrics() []gcp.project.loggingservice.metric
  // List of log sinks
  sinks() []gcp.project.loggingservice.sink
}

// Google Cloud (GCP) Logging bucket
private gcp.project.loggingservice.bucket @defaults("name") {
  // Project ID
  projectId string
  // CMEK settings of the log bucket
  cmekSettings dict
  // Creation timestamp
  created time
  // Description of the bucket
  description string
  // List of indexed fields and related configuration data
  indexConfigs []gcp.project.loggingservice.bucket.indexConfig
  // Bucket lifecycle state
  lifecycleState string
  // Whether the bucket is locked
  locked bool
  // Bucket name
  name string
  // Log entry field paths that are denied access in this bucket
  restrictedFields []string
  // Amount of time for which logs will be retained by default, after which they're' automatically deleted
  retentionDays int
  // Last update timestamp of the bucket
  updated time
}

// Google Cloud (GCP) Logging bucket index config
private gcp.project.loggingservice.bucket.indexConfig @defaults("id") {
  // Internal ID
  id string
  // Creation timestamp
  created time
  // Log entry field path to index
  fieldPath string
  // Type of data in this index
  type string
}

// Google Cloud (GCP) Logging metric
private gcp.project.loggingservice.metric @defaults("description filter") {
  // Metric ID
  id string
  // Project ID
  projectId string
  // Metric description
  description string
  // Advanced log filter
  filter string
  // Alert policies for this metric
  alertPolicies() []gcp.project.monitoringService.alertPolicy
}

// GCP Logging sink
private gcp.project.loggingservice.sink @defaults("destination") {
  // Sink ID
  id string
  // Project ID
  projectId string
  // Export destination
  destination string
  // Storage bucket to which the sink exports (only set for sinks with a destination storage bucket)
  storageBucket() gcp.project.storageService.bucket
  // Optional advanced logs filter
  filter string
  // When exporting logs, logging adopts this identity for authorization
  writerIdentity string
  // Whether to allow the sink to export log entries from the organization or folder, plus (recursively) from any contained folders, billings accounts, or projects
  includeChildren bool
}

// Google Cloud (GCP) IAM resources
private gcp.project.iamService {
  // Project ID
  projectId string
  // List of service accounts
  serviceAccounts() []gcp.project.iamService.serviceAccount
}

// Google Cloud (GCP) service account
private gcp.project.iamService.serviceAccount @defaults("displayName name") {
  // Project ID
  projectId string
  // Service account name
  name string
  // Unique, stable, numeric ID for the service account
  uniqueId string
  // Email address of the service account
  email string
  // User-specified, human-readable name for the service account
  displayName string
  // Service account description
  description string
  // OAuth 2.0 client ID
  oauth2ClientId string
  // Whether the service account is disabled
  disabled bool
  // Service account keys
  keys() []gcp.project.iamService.serviceAccount.key
}

// Google Cloud (GCP) service account keys
private gcp.project.iamService.serviceAccount.key @defaults("name") {
  // Service account key name
  name string
  // Algorithm (and possibly key size) of the key
  keyAlgorithm string
  // Key can be used after this timestamp
  validAfterTime time
  // Key can be used before this timestamp
  validBeforeTime time
  // Key origin
  keyOrigin string
  // Key type
  keyType string
  // Whether the key is disabled
  disabled bool
}
// GCP cloud function
private gcp.project.cloudFunction @defaults("name") {
  // Project ID
  projectId string
  // Cloud function name
  name string
  // Cloud function description
  description string
  // Location of the archive with the function's source code
  sourceArchiveUrl string
  // Repository reference for the function's source code
  sourceRepository dict
  // Location of the upload with the function's source code
  sourceUploadUrl string
  // HTTPS endpoint of source that can be triggered via URL
  httpsTrigger dict
  // Source that fires events in response to a condition in another service
  eventTrigger dict
  // Status of the function deployment
  status string
  // Name of the function (as defined in source code) that is executed
  entryPoint string
  // Runtime in which to run the function
  runtime string
  // Function execution timeout
  timeout time
  // Amount of memory in MB available for a function
  availableMemoryMb int
  // Email of the function's service account
  serviceAccountEmail string
  // Update timestamp
  updated time
  // Version identifier of the cloud function
  versionId int
  // Labels associated with this cloud function
  labels map[string]string
  // Environment variables that are available during function execution
  envVars map[string]string
  // Build environment variables that are available during build time
  buildEnvVars map[string]string
  // VPC network that this cloud function can connect to
  network string
  // Maximum number of function instances that may coexist at a given time
  maxInstances int
  // Lower bound for the number of function instances that may coexist at a given time
  minInstances int
  // VPC network connector that this cloud function can connect to
  vpcConnector string
  // Egress settings for the connector controlling what traffic is diverted
  egressSettings string
  // Ingress settings for the function controlling what traffic can reach
  ingressSettings string
  // Resource name of a KMS crypto key used to encrypt/decrypt function resources
  kmsKeyName string
  // Name of the Cloud Build custom WorkerPool that should be used to build the function
  buildWorkerPool string
  // Cloud Build ID of the latest successful deployment of the function
  buildId string
  // Cloud Build name of the function deployment
  buildName string
  // Secret environment variables
  secretEnvVars map[string]dict
  // Secret volumes
  secretVolumes []dict
  // User-managed repository created in Artifact Registry
  dockerRepository string
  // Docker registry to use for this deployment
  dockerRegistry string
}
// Google Cloud (GCP) Dataproc resources
private gcp.project.dataprocService {
  // Project ID
  projectId string
  // Whether the DataProc service is enabled in the project or not
  enabled bool
  // List of available regions
  regions() []string
  // List of Dataproc clusters in the current project
  clusters() []gcp.project.dataprocService.cluster
}

// Google Cloud (GCP) Dataproc cluster
private gcp.project.dataprocService.cluster @defaults("name") {
  // Project ID
  projectId string
  // Cluster name
  name string
  // Cluster UUID
  uuid string
  // Cluster configuration
  config gcp.project.dataprocService.cluster.config
  // Labels associated with the cluster
  labels map[string]string
  // Contains cluster daemon metrics such as HDF and YARN stats
  metrics dict
  // Cluster status
  status gcp.project.dataprocService.cluster.status
  // Previous cluster status
  statusHistory []gcp.project.dataprocService.cluster.status
  // Virtual cluster config used when creating a Dataproc cluster that does not directly control the underlying compute resources
  virtualClusterConfig gcp.project.dataprocService.cluster.virtualClusterConfig
}

// Google Cloud (GCP) Dataproc cluster config
private gcp.project.dataprocService.cluster.config {
  // Parent resource path
  parentResourcePath string
  // Autoscaling configuration for the policy associated with the cluster
  autoscaling dict
  // Cloud Storage bucket used to stage job dependencies, config files, and job driver console output
  configBucket string
  // Dataproc metrics configuration
  metrics dict
  // Encryption configuration
  encryption dict
  // Port/endpoint configuration
  endpoint dict
  // Shared Compute Engine configuration
  gceCluster gcp.project.dataprocService.cluster.config.gceCluster
  // Kubernetes Engine config for Dataproc clusters deployed to Kubernetes
  gkeCluster gcp.project.dataprocService.cluster.config.gkeCluster
  // Commands to execute on each node after config is completed
  initializationActions []dict
  // Lifecycle configuration
  lifecycle gcp.project.dataprocService.cluster.config.lifecycle
  // Compute Engine config for the cluster's master instance
  master gcp.project.dataprocService.cluster.config.instance
  // Metastore configuration
  metastore dict
  // Compute Engine configuration for the cluster's secondary worker instances
  secondaryWorker gcp.project.dataprocService.cluster.config.instance
  // Security configuration
  security dict
  // Cluster software configuration
  software dict
  // Cloud Storage bucket used to store ephemeral cluster and jobs data
  tempBucket string
  // Compute Engine configuration for the cluster's worker instances
  worker gcp.project.dataprocService.cluster.config.instance
}

// Google Cloud (GCP) Dataproc cluster endpoint config
private gcp.project.dataprocService.cluster.config.gceCluster {
  // Internal ID
  id string
  // Project ID
  projectId string
  // Confidential instance configuration
  confidentialInstance dict
  // Whether the cluster has only internal IP addresses
  internalIpOnly bool
  // Compute Engine metadata entries
  metadata map[string]string
  // Compute Engine network to be used for machine communications
  networkUri string
  // Node group affinity for sole-tenant clusters
  nodeGroupAffinity dict
  // Type of IPv6 access for the cluster
  privateIpv6GoogleAccess string
  // Reservation affinity for consuming zonal reservations
  reservationAffinity gcp.project.dataprocService.cluster.config.gceCluster.reservationAffinity
  // Email of the service account used by the Dataproc cluster VM instances
  serviceAccountEmail string
  // Service account used by the Dataproc cluster VM instances
  serviceAccount() gcp.project.iamService.serviceAccount
  // URIs of service account scopes to be included in Compute Engine instances
  serviceAccountScopes []string
  // Shielded instance config for clusters using Compute Engine Shielded VMs
  shieldedInstanceConfig gcp.project.dataprocService.cluster.config.gceCluster.shieldedInstanceConfig
  // Compute Engine subnetwork to use for machine communications
  subnetworkUri string
  // Compute Engine tags
  tags []string
  // Zone where the Compute Engine cluster is located
  zoneUri string
}

// Google Cloud (GCP) Dataproc cluster GCE cluster reservation affinity config
private gcp.project.dataprocService.cluster.config.gceCluster.reservationAffinity {
  // Internal ID
  id string
  // Type of reservation to consume
  consumeReservationType string
  // Corresponds to the label key of the reservation resource
  key string
  // Corresponds to the label values of the reservation resource
  values []string
}

// Google Cloud (GCP) Dataproc cluster GCE cluster shielded instance config
private gcp.project.dataprocService.cluster.config.gceCluster.shieldedInstanceConfig {
  // Internal ID
  id string
  // Whether the instances have integrity monitoring enabled
  enableIntegrityMonitoring bool
  // Whether the instances have Secure Boot enabled
  enableSecureBoot bool
  // Whether the instances have the vTPM enabled
  enableVtpm bool
}

// Google Cloud (GCP) Dataproc cluster GKE cluster config
private gcp.project.dataprocService.cluster.config.gkeCluster {
  // Internal ID
  id string
  // Target GKE cluster
  gkeClusterTarget string
  // GKE node pools where workloads are scheduled
  nodePoolTarget []dict
}

// Google Cloud (GCP) Dataproc cluster lifecycle config
private gcp.project.dataprocService.cluster.config.lifecycle {
  // Internal ID
  id string
  // Time when the cluster will be auto-deleted
  autoDeleteTime string
  // Lifetime duration of the cluster
  autoDeleteTtl string
  // Duration to keep the cluster alive while idling
  idleDeleteTtl string
  // Time when the cluster will be auto-resumed
  idleStartTime string
}

// Google Cloud (GCP) Dataproc cluster instance config
private gcp.project.dataprocService.cluster.config.instance {
  // Internal ID
  id string
  // Compute Engine accelerators
  accelerators []dict
  // Disk options
  diskConfig gcp.project.dataprocService.cluster.config.instance.diskConfig
  // Compute Engine imager resource used for cluster instances
  imageUri string
  // List of instance names
  instanceNames []string
  // List of references to Compute Engine instances
  instanceReferences []dict
  // Whether the instance group contains preemptible instances
  isPreemptible bool
  // Compute Engine machine type used for cluster instances
  machineTypeUri string
  // Config for Compute Engine Instance Group Manager that manages this group
  managedGroupConfig dict
  // Minimum CPU platform for the instance group
  minCpuPlatform string
  // Number of VM instances in the instance group
  numInstances int
  // The preemptibility of the instance group
  preemptibility string
}

// Google Cloud (GCP) Dataproc cluster instance disk config
private gcp.project.dataprocService.cluster.config.instance.diskConfig {
  // Internal ID
  id string
  // Size in GB of the boot disk
  bootDiskSizeGb int
  // Type of the boot disk
  bootDiskType string
  // Interface type of local SSDs
  localSsdInterface string
  // Number of attached SSDs
  numLocalSsds int
}

// Google Cloud (GCP) Dataproc cluster status
private gcp.project.dataprocService.cluster.status @defaults("state") {
  // Internal ID
  id string
  // Details of the cluster's state
  detail string
  // Cluster's state
  state string
  // Started timestamp
  started time
  // Additional state information that includes status reported by the agent
  substate string
}

// Google Cloud (GCP) Dataproc cluster virtual cluster config
private gcp.project.dataprocService.cluster.virtualClusterConfig {
  // Parent resource path
  parentResourcePath string
  // Auxiliary services configuration
  auxiliaryServices dict
  // Kubernetes cluster configuration
  kubernetesCluster dict
  // Cloud Storage bucket used to stage job dependencies, config files, and job driver console output
  stagingBucket string
}
// Google Cloud (GCP) Run resources
private gcp.project.cloudRunService {
  // Project ID
  projectId string
  // List of available regions
  regions() []string
  // List of operations
  operations() []gcp.project.cloudRunService.operation
  // List of services
  services() []gcp.project.cloudRunService.service
  // List of jobs
  jobs() []gcp.project.cloudRunService.job
}

// Google Cloud (GCP) Run operation
private gcp.project.cloudRunService.operation @defaults("name") {
  // Project ID
  projectId string
  // Operation name
  name string
  // Whether the operation is completed
  done bool
}

// Google Cloud (GCP) Run service
private gcp.project.cloudRunService.service @defaults("name") {
  // Service identifier
  id string
  // Project ID
  projectId string
  // Region
  region string
  // Service name
  name string
  // Service description
  description string
  // Number that monotonically increases every time the user modifies the desired state
  generation int
  // User-provided labels
  labels map[string]string
  // Unstructured key-value map that may be set by external tools to store an arbitrary metadata
  annotations map[string]string
  // Creation timestamp
  created time
  // Update timestamp
  updated time
  // Deletion timestamp
  deleted time
  // Timestamp after which a deleted service will be permanently deleted
  expired time
  // Email address of the authenticated creator
  creator string
  // Email address of the last authenticated modifier
  lastModifier string
  // Ingress settings
  ingress string
  // Launch stage
  launchStage string
  // Template used to create revisions for the service
  template gcp.project.cloudRunService.service.revisionTemplate
  // Specifies how to distribute traffic over a collection of revisions belonging to the service
  traffic []dict
  // Generation of this service currently serving traffic
  observedGeneration int
  // Conditions of this service, containing its readiness status and detailed error information in case it did not reach a serving state
  terminalCondition gcp.project.cloudRunService.condition
  // Conditions of all other associated sub-resources
  conditions []gcp.project.cloudRunService.condition
  // Name of the latest revision that is serving traffic
  latestReadyRevision string
  // Name of the last created revision
  latestCreatedRevision string
  // Detailed status information for corresponding traffic targets
  trafficStatuses []dict
  // Main URI in which this service is serving traffic
  uri string
  // Whether the service is currently being acted upon by the system to bring it into the desired state
  reconciling bool
}

// Google Cloud (GCP) Run service revision template
private gcp.project.cloudRunService.service.revisionTemplate @defaults("name") {
  // Internal ID
  id string
  // Project ID
  projectId string
  // Revision name
  name string
  // User-provided labels
  labels map[string]string
  // Unstructured key-value map that may be set by external tools to store an arbitrary metadata
  annotations map[string]string
  // Scaling settings
  scaling dict
  // VPC access configuration
  vpcAccess dict
  // Maximum allowed time for an instance to respond to a request
  timeout time
  // Email address of the IAM service account associated with the revision of the service
  serviceAccountEmail string
  // IAM service account associated with the revision of the service
  serviceAccount() gcp.project.iamService.serviceAccount
  // Containers for this revision
  containers  []gcp.project.cloudRunService.container
  // List of volumes to make available to containers
  volumes []dict
  // Sandbox environment to host the revision
  executionEnvironment string
  // Reference to a customer-managed encryption key to use to encrypt this container image
  encryptionKey string
  // Maximum number of requests that each serving instance can receive
  maxInstanceRequestConcurrency int
}

// Google Cloud (GCP) Run service revision template container
private gcp.project.cloudRunService.container @defaults("name image") {
  // Internal ID
  id string
  // Container name
  name string
  // URL of the container image in Google Container Registry or Google Artifact Registry
  image string
  // Entrypoint array
  command []string
  // Arguments to the entrypoint
  args []string
  // Environment variables
  env []dict
  // Compute resource requirements by the container
  resources dict
  // List of ports to expose from the container
  // @afiune Fix issue int32 not parse as dict
  ports []dict
  // Volumes to mount into the container's file system
  volumeMounts []dict
  // Container's working directory
  workingDir string
  // Periodic probe of container liveness
  livenessProbe gcp.project.cloudRunService.container.probe
  // Startup probe of application within the container
  startupProbe gcp.project.cloudRunService.container.probe
}

// Google Cloud (GCP) Run service revision template container probe
private gcp.project.cloudRunService.container.probe {
  // Internal ID
  id string
  // Number of seconds after the container has started before the probe is initiated
  initialDelaySeconds int
  // Number of seconds after which the probe times out
  timeoutSeconds int
  // Number of seconds indicating how often to perform the probe
  periodSeconds int
  // Minimum consecutive successes for the probe to be considered failed
  failureThreshold int
  // HTTP GET probe configuration
  httpGet dict
  // TCP socket probe configuration
  tcpSocket dict
}

// Google Cloud (GCP) Run condition
private gcp.project.cloudRunService.condition @defaults("type state message") {
  // Internal ID
  id string
  // Status of the reconciliation process
  type string
  // Condition state
  state string
  // Human-readable message indicating details about the current status
  message string
  // Last time the condition transitioned from one status to another
  lastTransitionTime time
  // How to interpret failures of this condition
  severity string
}

// Google Cloud (GCP) Run job
private gcp.project.cloudRunService.job {
  // Job identifier
  id string
  // Project ID
  projectId string
  // Region
  region string
  // Job name
  name string
  // Number that monotonically increases every time the user modifies the desired state
  generation int
  // User-defined labels
  labels map[string]string
  // Unstructured key-value map that may be set by external tools to store an arbitrary metadata
  annotations map[string]string
  // Creation timestamp
  created time
  // Update timestamp
  updated time
  // Deletion timestamp
  deleted time
  // Timestamp after which a deleted service will be permanently deleted
  expired time
  // Email address of the authenticated creator
  creator string
  // Email address of the last authenticated modifier
  lastModifier string
  // Arbitrary identifier for the API client
  client string
  // Arbitrary version identifier for the API client
  clientVersion string
  // Launch stage
  launchStage string
  // Template used to create executions for this job
  template gcp.project.cloudRunService.job.executionTemplate
  // Generation of this service currently serving traffic
  observedGeneration int
  // Conditions of this service, containing its readiness status and detailed error information in case it did not reach a serving state
  terminalCondition gcp.project.cloudRunService.condition
  // Conditions of all other associated sub-resources
  conditions []gcp.project.cloudRunService.condition
  // Number of executions created for this job
  executionCount int
  // Whether the service is currently being acted upon by the system to bring it into the desired state
  reconciling bool
}

// Google Cloud (GCP) Run job execution template
private gcp.project.cloudRunService.job.executionTemplate {
  // Internal ID
  id string
  // User-defined labels
  labels map[string]string
  // Unstructured key-value map that may be set by external tools to store an arbitrary metadata
  annotations map[string]string
  // Specifies the maximum desired number of tasks the execution should run at a given time
  parallelism int
  // Specifies the desired number of tasks the execution should run
  taskCount int
  // Describes the task that will be create when executing an execution
  template gcp.project.cloudRunService.job.executionTemplate.taskTemplate
}

// Google Cloud (GCP) Run job execution template task template
private gcp.project.cloudRunService.job.executionTemplate.taskTemplate {
  // Internal ID
  id string
  // Project ID
  projectId string
  // VPC access configuration
  vpcAccess dict
  // Maximum allowed time for an instance to respond to a request
  timeout time
  // Email address of the IAM service account associated with the revision of the service
  serviceAccountEmail string
  // IAM service account associated with the revision of the service
  serviceAccount() gcp.project.iamService.serviceAccount
  // Containers for this revision
  containers  []gcp.project.cloudRunService.container
  // List of volumes to make available to containers
  volumes []dict
  // Sandbox environment to host the revision
  executionEnvironment string
  // Reference to a customer-managed encryption key to use to encrypt this container image
  encryptionKey string
  // Number of retries allowed per task
  maxRetries int
}

// Google Cloud (GCP) access approval settings
private gcp.accessApprovalSettings {
  // Resource path
  resourcePath string
  // List of email addresses to which notifications relating to approval requests should be sent
  notificationEmails []string
  // List of Google Cloud services for which the given resource has access approval enrolled
  enrolledServices []dict
  // Whether at least one service is enrolled for access approval in one or more ancestors of the project or folder (unset for organizations since organizations do not have ancestors)
  enrolledAncestor bool
  // Asymmetric crypto key version to use for signing approval requests
  activeKeyVersion string
  // Whether an ancestor of this project or folder has set active key version (unset for organizations since organizations do not have ancestors)
  ancestorHasActiveKeyVersion bool
  // Whether there is some configuration issue with the active key version configured at this level of the resource hierarchy
  invalidKeyVersion bool
}

// Google Cloud (GCP) monitoring resources
private gcp.project.monitoringService {
  // Project ID
  projectId string
  // List of alert policies
  alertPolicies() []gcp.project.monitoringService.alertPolicy
}

// Google Cloud (GCP) monitoring alert policy
private gcp.project.monitoringService.alertPolicy {
  // Project ID
  projectId string
  // Alert policy name
  name string
  // Display name
  displayName string
  // Documentation included with notifications and incidents related to this policy
  documentation dict
  // User-defined labels
  labels map[string]string
  // List of conditions for the policy
  conditions []dict
  // How to combine the results of multiple conditions to determine if an incident should be opened
  combiner string
  // Whether the policy is enabled
  enabled bool
  // Description of how the alert policy is invalid
  validity dict
  // Notification channel URLs to which notifications should be sent when incidents are opened or closed
  notificationChannelUrls []string
  // Creation timestamp
  created time
  // Email address of the user who created the alert policy
  createdBy string
  // Update timestamp
  updated time
  // Email address of the user who last updated the alert policy
  updatedBy string
  // Configuration for notification channels notifications
  alertStrategy dict
}

private gcp.project.binaryAuthorizationControl {
  // The policy for container image binary authorization
  policy gcp.project.binaryAuthorizationControl.policy
}

private gcp.project.binaryAuthorizationControl.policy {
  // The resource name
  name string
  // Controls the evaluation of a Google-maintained global admission policy for common system-level images
  globalPolicyEvaluationMode string
  // Admission policy allowlisting
  admissionWhitelistPatterns []string
  // Per-cluster admission rules
  clusterAdmissionRules map[string]gcp.project.binaryAuthorizationControl.admissionRule
  // Per-kubernetes-namespace admission rules
  kubernetesNamespaceAdmissionRules map[string]gcp.project.binaryAuthorizationControl.admissionRule
  // Per-kubernetes-service-account admission rules
  kubernetesServiceAccountAdmissionRules map[string]gcp.project.binaryAuthorizationControl.admissionRule
  // Per-istio-service-identity admission rules
  istioServiceIdentityAdmissionRules map[string]gcp.project.binaryAuthorizationControl.admissionRule
  // Default admission rule for a cluster without a per-cluster, per-kubernetes-service-account, or per-istio-service-identity admission rule
  defaultAdmissionRule gcp.project.binaryAuthorizationControl.admissionRule
  // Time when the policy was last updated
  updated time
}

private gcp.project.binaryAuthorizationControl.admissionRule {
  // How this admission rule will be evaluated
  evaluationMode string
  // The action when a pod creation is denied by the admission rule
  enforcementMode string
  // The resource names of the attestors that must attest to a container image
  requireAttestationsBy []string
}
