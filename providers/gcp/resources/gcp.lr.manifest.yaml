# Copyright (c) Mondoo, Inc.
# SPDX-License-Identifier: BUSL-1.1

resources:
  gcp.accessApprovalSettings:
    fields:
      activeKeyVersion: {}
      ancestorHasActiveKeyVersion: {}
      enrolledAncestor: {}
      enrolledServices: {}
      invalidKeyVersion: {}
      notificationEmails: {}
      resourcePath: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Access Approval documentation
      url: https://cloud.google.com/assured-workloads/access-approval/docs
  gcp.essentialContact:
    fields:
      email: {}
      languageTag: {}
      notificationCategories: {}
      resourcePath: {}
      validated: {}
      validationState: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Managing contacts for notifications
      url: https://cloud.google.com/resource-manager/docs/managing-notification-contacts
  gcp.folder:
    fields:
      created: {}
      folders: {}
      id: {}
      name: {}
      parentId: {}
      projects: {}
      state: {}
      updated: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Creating and managing Folders
      url: https://cloud.google.com/resource-manager/docs/creating-managing-folders
  gcp.folders:
    fields:
      children: {}
      list: {}
      parentId: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Creating and managing Folders
      url: https://cloud.google.com/resource-manager/docs/creating-managing-folders
  gcp.organization:
    fields:
      accessApprovalSettings: {}
      folders: {}
      iamPolicy: {}
      id: {}
      lifecycleState: {}
      name: {}
      projects: {}
      state: {}
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Resource hierarchy
      url: https://cloud.google.com/resource-manager/docs/cloud-platform-resource-hierarchy
  gcp.project:
    fields:
      accessApprovalSettings: {}
      apiKeys: {}
      bigquery: {}
      binaryAuthorization: {}
      cloudFunctions: {}
      cloudRun: {}
      commonInstanceMetadata: {}
      compute: {}
      createTime: {}
      dataproc: {}
      dns: {}
      essentialContacts: {}
      gke: {}
      iam: {}
      iamPolicy: {}
      id: {}
      kms: {}
      labels: {}
      lifecycleState: {}
      logging: {}
      monitoring: {}
      name: {}
      number: {}
      parentId: {}
      pubsub: {}
      recommendations: {}
      redis:
        min_mondoo_version: 9.0.0
      services: {}
      sql: {}
      state: {}
      storage: {}
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Creating and managing projects
      url: https://cloud.google.com/resource-manager/docs/creating-managing-projects
  gcp.project.apiKey:
    fields:
      annotations: {}
      created: {}
      deleted: {}
      id: {}
      keyString: {}
      name: {}
      projectId: {}
      resourcePath: {}
      restrictions: {}
      updated: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: API Keys Overview
      url: https://cloud.google.com/api-keys/docs/overview
  gcp.project.apiKey.restrictions:
    fields:
      androidKeyRestrictions: {}
      apiTargets: {}
      browserKeyRestrictions: {}
      iosKeyRestrictions: {}
      parentResourcePath: {}
      serverKeyRestrictions: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Adding restrictions to API keys
      url: https://cloud.google.com/api-keys/docs/add-restrictions-api-keys
  gcp.project.bigqueryService:
    fields:
      datasets: {}
      projectId: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: BigQuery documentation
      url: https://cloud.google.com/bigquery/docs
  gcp.project.bigqueryService.dataset:
    fields:
      access: {}
      created: {}
      description: {}
      id: {}
      kmsName: {}
      labels: {}
      location: {}
      models: {}
      modified: {}
      name: {}
      projectId: {}
      routines: {}
      tables: {}
      tags: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Introduction to datasets
      url: https://cloud.google.com/bigquery/docs/datasets-intro
  gcp.project.bigqueryService.dataset.accessEntry:
    fields:
      datasetId: {}
      datasetRef: {}
      entity: {}
      entityType: {}
      id: {}
      role: {}
      routineRef: {}
      viewRef: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Control access to resources
      url: https://cloud.google.com/bigquery/docs/control-access-to-resources-iam
  gcp.project.bigqueryService.model:
    fields:
      created: {}
      datasetId: {}
      description: {}
      expirationTime: {}
      id: {}
      kmsName: {}
      labels: {}
      location: {}
      modified: {}
      name: {}
      projectId: {}
      type: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Introduction to BigQuery ML
      url: https://cloud.google.com/bigquery/docs/bqml-introduction
  gcp.project.bigqueryService.routine:
    fields:
      created: {}
      datasetId: {}
      description: {}
      id: {}
      language: {}
      modified: {}
      projectId: {}
      type: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Manage routines
      url: https://cloud.google.com/bigquery/docs/routines
  gcp.project.bigqueryService.table:
    fields:
      clusteringFields: {}
      created: {}
      datasetId: {}
      description: {}
      expirationTime: {}
      externalDataConfig: {}
      id: {}
      kmsName: {}
      labels: {}
      location: {}
      materializedView: {}
      modified: {}
      name: {}
      numBytes: {}
      numLongTermBytes: {}
      numRows: {}
      projectId: {}
      rangePartitioning: {}
      requirePartitionFilter: {}
      schema: {}
      snapshotTime: {}
      timePartitioning: {}
      type: {}
      useLegacySQL: {}
      viewQuery: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Introduction to tables
      url: https://cloud.google.com/bigquery/docs/tables-intro
    platform:
      name:
      - gcp
  gcp.project.binaryAuthorizationControl:
    fields:
      policy: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
  gcp.project.binaryAuthorizationControl.admissionRule:
    fields:
      enforcementMode: {}
      evaluationMode: {}
      requireAttestationsBy: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
  gcp.project.binaryAuthorizationControl.policy:
    fields:
      admissionWhitelistPatterns: {}
      clusterAdmissionRules: {}
      defaultAdmissionRule: {}
      globalPolicyEvaluationMode: {}
      istioServiceIdentityAdmissionRules: {}
      kubernetesNamespaceAdmissionRules: {}
      kubernetesServiceAccountAdmissionRules: {}
      name: {}
      updated: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
  gcp.project.cloudFunction:
    fields:
      availableMemoryMb: {}
      buildEnvVars: {}
      buildId: {}
      buildName: {}
      buildWorkerPool: {}
      description: {}
      dockerRegistry: {}
      dockerRepository: {}
      egressSettings: {}
      entryPoint: {}
      envVars: {}
      eventTrigger: {}
      httpsTrigger: {}
      ingressSettings: {}
      kmsKeyName: {}
      labels: {}
      maxInstances: {}
      minInstances: {}
      name: {}
      network: {}
      projectId: {}
      runtime: {}
      secretEnvVars: {}
      secretVolumes: {}
      serviceAccountEmail: {}
      sourceArchiveUrl: {}
      sourceRepository: {}
      sourceUploadUrl: {}
      status: {}
      timeout: {}
      updated: {}
      versionId: {}
      vpcConnector: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Cloud Functions
      url: https://cloud.google.com/functions
  gcp.project.cloudRunService:
    fields:
      jobs: {}
      operations: {}
      projectId: {}
      regions: {}
      services: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: What is Cloud Run
      url: https://cloud.google.com/run/docs/overview/what-is-cloud-run
  gcp.project.cloudRunService.condition:
    fields:
      id: {}
      lastTransitionTime: {}
      message: {}
      severity: {}
      state: {}
      type: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Condition
      url: https://cloud.google.com/run/docs/reference/rest/v1/Condition
  gcp.project.cloudRunService.container:
    fields:
      args: {}
      command: {}
      env: {}
      id: {}
      image: {}
      livenessProbe: {}
      name: {}
      ports: {}
      resources: {}
      startupProbe: {}
      volumeMounts: {}
      workingDir: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Container
      url: https://cloud.google.com/run/docs/reference/rest/v1/Container
  gcp.project.cloudRunService.container.probe:
    fields:
      failureThreshold: {}
      httpGet: {}
      id: {}
      initialDelaySeconds: {}
      periodSeconds: {}
      tcpSocket: {}
      timeoutSeconds: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Container
      url: https://cloud.google.com/run/docs/reference/rest/v1/Container
  gcp.project.cloudRunService.job:
    fields:
      annotations: {}
      client: {}
      clientVersion: {}
      conditions: {}
      created: {}
      creator: {}
      deleted: {}
      executionCount: {}
      expired: {}
      generation: {}
      id: {}
      labels: {}
      lastModifier: {}
      launchStage: {}
      name: {}
      observedGeneration: {}
      projectId: {}
      reconciling: {}
      region: {}
      template: {}
      terminalCondition: {}
      updated: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Cloud Run Jobs
      url: https://cloud.google.com/run/docs/overview/what-is-cloud-run#jobs
  gcp.project.cloudRunService.job.executionTemplate:
    fields:
      annotations: {}
      id: {}
      labels: {}
      parallelism: {}
      taskCount: {}
      template: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Create jobs
      url: https://cloud.google.com/run/docs/create-jobs
  gcp.project.cloudRunService.job.executionTemplate.taskTemplate:
    fields:
      containers: {}
      encryptionKey: {}
      executionEnvironment: {}
      id: {}
      maxRetries: {}
      projectId: {}
      serviceAccount: {}
      serviceAccountEmail: {}
      timeout: {}
      volumes: {}
      vpcAccess: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Create jobs
      url: https://cloud.google.com/run/docs/create-jobs
  gcp.project.cloudRunService.operation:
    fields:
      done: {}
      name: {}
      projectId: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
  gcp.project.cloudRunService.service:
    fields:
      annotations: {}
      conditions: {}
      created: {}
      creator: {}
      deleted: {}
      description: {}
      expired: {}
      generation: {}
      id: {}
      ingress: {}
      labels: {}
      lastModifier: {}
      latestCreatedRevision: {}
      latestReadyRevision: {}
      launchStage: {}
      name: {}
      observedGeneration: {}
      projectId: {}
      reconciling: {}
      region: {}
      template: {}
      terminalCondition: {}
      traffic: {}
      trafficStatuses: {}
      updated: {}
      uri: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Cloud Run services
      url: https://cloud.google.com/run/docs/resource-model#services
  gcp.project.cloudRunService.service.revisionTemplate:
    fields:
      annotations: {}
      containers: {}
      encryptionKey: {}
      executionEnvironment: {}
      id: {}
      labels: {}
      maxInstanceRequestConcurrency: {}
      name: {}
      projectId: {}
      scaling: {}
      serviceAccount: {}
      serviceAccountEmail: {}
      timeout: {}
      volumes: {}
      vpcAccess: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Rollbacks, gradual rollouts, and traffic migration
      url: https://cloud.google.com/run/docs/rollouts-rollbacks-traffic-migration
  gcp.project.computeService:
    fields:
      addresses: {}
      backendServices: {}
      disks: {}
      enabled: {}
      firewalls: {}
      forwardingRules: {}
      images: {}
      instances: {}
      machineTypes: {}
      networks: {}
      projectId: {}
      regions: {}
      routers: {}
      snapshots: {}
      subnetworks: {}
      zones: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Compute Engine documentation
      url: https://cloud.google.com/compute/docs
  gcp.project.computeService.address:
    fields:
      address: {}
      addressType: {}
      created: {}
      description: {}
      id: {}
      ipVersion: {}
      ipv6EndpointType: {}
      name: {}
      network: {}
      networkTier: {}
      networkUrl: {}
      prefixLength: {}
      purpose: {}
      regionUrl: {}
      resourceUrls: {}
      status: {}
      subnetwork: {}
      subnetworkUrl: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: IP addresses
      url: https://cloud.google.com/compute/docs/ip-addresses
  gcp.project.computeService.attachedDisk:
    fields:
      architecture: {}
      autoDelete: {}
      boot: {}
      deviceName: {}
      diskSizeGb: {}
      forceAttach: {}
      guestOsFeatures: {}
      id: {}
      index: {}
      interface: {}
      licenses: {}
      mode: {}
      projectId: {}
      source: {}
      type: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: About Persistent Disk
      url: https://cloud.google.com/compute/docs/disks/persistent-disks
  gcp.project.computeService.backendService:
    fields:
      affinityCookieTtlSec: {}
      backends: {}
      cdnPolicy: {}
      circuitBreakers: {}
      compressionMode: {}
      connectionDraining: {}
      connectionTrackingPolicy: {}
      consistentHash: {}
      created: {}
      customRequestHeaders: {}
      customResponseHeaders: {}
      description: {}
      edgeSecurityPolicy: {}
      enableCDN: {}
      failoverPolicy: {}
      healthChecks: {}
      iap: {}
      id: {}
      loadBalancingScheme: {}
      localityLbPolicies: {}
      localityLbPolicy: {}
      logConfig: {}
      maxStreamDuration: {}
      name: {}
      networkUrl: {}
      portName: {}
      protocol: {}
      regionUrl: {}
      securityPolicyUrl: {}
      securitySettings: {}
      serviceBindingUrls: {}
      sessionAffinity: {}
      timeoutSec: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Backend services overview
      url: https://cloud.google.com/load-balancing/docs/backend-service
  gcp.project.computeService.backendService.backend:
    fields:
      balancingMode: {}
      capacityScaler: {}
      description: {}
      failover: {}
      groupUrl: {}
      id: {}
      maxConnections: {}
      maxConnectionsPerEndpoint: {}
      maxConnectionsPerInstance: {}
      maxRate: {}
      maxRatePerEndpoint: {}
      maxRatePerInstance: {}
      maxUtilization: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Backend services overview
      url: https://cloud.google.com/load-balancing/docs/backend-service
  gcp.project.computeService.backendService.cdnPolicy:
    fields:
      bypassCacheOnRequestHeaders: {}
      cacheKeyPolicy: {}
      cacheMode: {}
      clientTtl: {}
      defaultTtl: {}
      id: {}
      maxTtl: {}
      negativeCaching: {}
      negativeCachingPolicy: {}
      requestCoalescing: {}
      serveWhileStale: {}
      signedUrlCacheMaxAgeSec: {}
      signedUrlKeyNames: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Backend services overview
      url: https://cloud.google.com/load-balancing/docs/backend-service
  gcp.project.computeService.disk:
    fields:
      architecture: {}
      created: {}
      description: {}
      diskEncryptionKey: {}
      guestOsFeatures: {}
      id: {}
      labels: {}
      lastAttachTimestamp: {}
      lastDetachTimestamp: {}
      licenses: {}
      locationHint: {}
      name: {}
      physicalBlockSizeBytes: {}
      provisionedIops: {}
      sizeGb: {}
      status: {}
      zone: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Storage options
      url: https://cloud.google.com/compute/docs/disks
  gcp.project.computeService.firewall:
    fields:
      allowed: {}
      created: {}
      denied: {}
      description: {}
      destinationRanges: {}
      direction: {}
      disabled: {}
      id: {}
      name: {}
      priority: {}
      projectId: {}
      sourceRanges: {}
      sourceServiceAccounts: {}
      sourceTags: {}
      targetServiceAccounts: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: VPC firewall rules
      url: https://cloud.google.com/firewall/docs/firewalls
  gcp.project.computeService.forwardingRule:
    fields:
      allPorts: {}
      allowGlobalAccess: {}
      backendService: {}
      created: {}
      description: {}
      id: {}
      ipAddress: {}
      ipProtocol: {}
      ipVersion: {}
      isMirroringCollector: {}
      labels: {}
      loadBalancingScheme: {}
      metadataFilters: {}
      name: {}
      network: {}
      networkTier: {}
      networkUrl: {}
      noAutomateDnsZone: {}
      portRange: {}
      ports: {}
      regionUrl: {}
      serviceDirectoryRegistrations: {}
      serviceLabel: {}
      serviceName: {}
      subnetwork: {}
      subnetworkUrl: {}
      targetUrl: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Forwarding rules overview
      url: https://cloud.google.com/load-balancing/docs/forwarding-rule-concepts
  gcp.project.computeService.image:
    fields:
      architecture: {}
      archiveSizeBytes: {}
      created: {}
      description: {}
      diskSizeGb: {}
      family: {}
      id: {}
      labels: {}
      licenses: {}
      name: {}
      projectId: {}
      status: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: OS images
      url: https://cloud.google.com/compute/docs/images
  gcp.project.computeService.instance:
    fields:
      canIpForward: {}
      confidentialInstanceConfig: {}
      cpuPlatform: {}
      created: {}
      deletionProtection: {}
      description: {}
      disks: {}
      enableDisplay: {}
      enableIntegrityMonitoring: {}
      enableSecureBoot: {}
      enableVtpm: {}
      fingerprint: {}
      guestAccelerators: {}
      hostname: {}
      id: {}
      keyRevocationActionType: {}
      labels: {}
      lastStartTimestamp: {}
      lastStopTimestamp: {}
      lastSuspendedTimestamp: {}
      machineType: {}
      metadata: {}
      minCpuPlatform: {}
      name: {}
      networkInterfaces: {}
      physicalHostResourceStatus: {}
      privateIpv6GoogleAccess: {}
      projectId: {}
      reservationAffinity: {}
      resourcePolicies: {}
      scheduling: {}
      serviceAccounts: {}
      sourceMachineImage: {}
      startRestricted: {}
      status: {}
      statusMessage: {}
      tags: {}
      totalEgressBandwidthTier: {}
      zone: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Virtual machine instances
      url: https://cloud.google.com/compute/docs/instances
  gcp.project.computeService.machineType:
    fields:
      created: {}
      description: {}
      guestCpus: {}
      id: {}
      isSharedCpu: {}
      maximumPersistentDisks: {}
      maximumPersistentDisksSizeGb: {}
      memoryMb: {}
      name: {}
      projectId: {}
      zone: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Machine families resource and comparison guide
      url: https://cloud.google.com/compute/docs/machine-resource
  gcp.project.computeService.network:
    fields:
      autoCreateSubnetworks: {}
      created: {}
      description: {}
      enableUlaInternalIpv6: {}
      gatewayIPv4: {}
      id: {}
      mode: {}
      mtu: {}
      name: {}
      networkFirewallPolicyEnforcementOrder: {}
      peerings: {}
      projectId: {}
      routingMode: {}
      subnetworkUrls: {}
      subnetworks: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Create and manage VPC networks
      url: https://cloud.google.com/vpc/docs/create-modify-vpc-networks
  gcp.project.computeService.region:
    fields:
      created: {}
      deprecated: {}
      description: {}
      id: {}
      name: {}
      quotas: {}
      status: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Regions and zones
      url: https://cloud.google.com/compute/docs/regions-zones
  gcp.project.computeService.router:
    fields:
      bgp: {}
      bgpPeers: {}
      created: {}
      description: {}
      encryptedInterconnectRouter: {}
      id: {}
      name: {}
      nats: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Cloud Router overview
      url: https://cloud.google.com/network-connectivity/docs/router/concepts/overview
  gcp.project.computeService.serviceaccount:
    fields:
      email: {}
      scopes: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Service accounts
      url: https://cloud.google.com/compute/docs/access/service-accounts
  gcp.project.computeService.snapshot:
    fields:
      architecture: {}
      autoCreated: {}
      chainName: {}
      created: {}
      creationSizeBytes: {}
      description: {}
      diskSizeGb: {}
      downloadBytes: {}
      id: {}
      labels: {}
      licenses: {}
      name: {}
      snapshotType: {}
      status: {}
      storageBytes: {}
      storageBytesStatus: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: About archive and standard disk snapshots
      url: https://cloud.google.com/compute/docs/disks/snapshots
  gcp.project.computeService.subnetwork:
    fields:
      created: {}
      description: {}
      enableFlowLogs: {}
      externalIpv6Prefix: {}
      fingerprint: {}
      gatewayAddress: {}
      id: {}
      internalIpv6Prefix: {}
      ipCidrRange: {}
      ipv6AccessType: {}
      ipv6CidrRange: {}
      logConfig: {}
      name: {}
      privateIpGoogleAccess: {}
      privateIpv6GoogleAccess: {}
      projectId: {}
      purpose: {}
      region: {}
      regionUrl: {}
      role: {}
      stackType: {}
      state: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Create and manage VPC networks
      url: https://cloud.google.com/vpc/docs/create-modify-vpc-networks
  gcp.project.computeService.subnetwork.logConfig:
    fields:
      aggregationInterval: {}
      enable: {}
      filterExpression: {}
      flowSampling: {}
      id: {}
      metadata: {}
      metadataFields: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Create and manage VPC networks
      url: https://cloud.google.com/vpc/docs/create-modify-vpc-networks
  gcp.project.computeService.zone:
    fields:
      created: {}
      description: {}
      id: {}
      name: {}
      status: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Regions and zones
      url: https://cloud.google.com/compute/docs/regions-zones
  gcp.project.dataprocService:
    fields:
      clusters: {}
      enabled: {}
      projectId: {}
      regions: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Dataproc documentation
      url: https://cloud.google.com/dataproc/docs#docs
  gcp.project.dataprocService.cluster:
    fields:
      config: {}
      labels: {}
      metrics: {}
      name: {}
      projectId: {}
      status: {}
      statusHistory: {}
      uuid: {}
      virtualClusterConfig: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Create a cluster
      url: https://cloud.google.com/dataproc/docs/guides/create-cluster
  gcp.project.dataprocService.cluster.config:
    fields:
      autoscaling: {}
      configBucket: {}
      encryption: {}
      endpoint: {}
      gceCluster: {}
      gkeCluster: {}
      initializationActions: {}
      lifecycle: {}
      master: {}
      metastore: {}
      metrics: {}
      parentResourcePath: {}
      secondaryWorker: {}
      security: {}
      software: {}
      tempBucket: {}
      worker: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Create a cluster
      url: https://cloud.google.com/dataproc/docs/guides/create-cluster
  gcp.project.dataprocService.cluster.config.gceCluster:
    fields:
      confidentialInstance: {}
      id: {}
      internalIpOnly: {}
      metadata: {}
      networkUri: {}
      nodeGroupAffinity: {}
      privateIpv6GoogleAccess: {}
      projectId: {}
      reservationAffinity: {}
      serviceAccount: {}
      serviceAccountEmail: {}
      serviceAccountScopes: {}
      shieldedInstanceConfig: {}
      subnetworkUri: {}
      tags: {}
      zoneUri: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Create a cluster
      url: https://cloud.google.com/dataproc/docs/guides/create-cluster
  gcp.project.dataprocService.cluster.config.gceCluster.reservationAffinity:
    fields:
      consumeReservationType: {}
      id: {}
      key: {}
      values: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Create a cluster
      url: https://cloud.google.com/dataproc/docs/guides/create-cluster
  gcp.project.dataprocService.cluster.config.gceCluster.shieldedInstanceConfig:
    fields:
      enableIntegrityMonitoring: {}
      enableSecureBoot: {}
      enableVtpm: {}
      id: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Create a cluster
      url: https://cloud.google.com/dataproc/docs/guides/create-cluster
  gcp.project.dataprocService.cluster.config.gkeCluster:
    fields:
      gkeClusterTarget: {}
      id: {}
      nodePoolTarget: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: GKE cluster architecture
      url: https://cloud.google.com/kubernetes-engine/docs/concepts/cluster-architecture
  gcp.project.dataprocService.cluster.config.instance:
    fields:
      accelerators: {}
      diskConfig: {}
      id: {}
      imageUri: {}
      instanceNames: {}
      instanceReferences: {}
      isPreemptible: {}
      machineTypeUri: {}
      managedGroupConfig: {}
      minCpuPlatform: {}
      numInstances: {}
      preemptibility: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Create a cluster
      url: https://cloud.google.com/dataproc/docs/guides/create-cluster
  gcp.project.dataprocService.cluster.config.instance.diskConfig:
    fields:
      bootDiskSizeGb: {}
      bootDiskType: {}
      id: {}
      localSsdInterface: {}
      numLocalSsds: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
  gcp.project.dataprocService.cluster.config.lifecycle:
    fields:
      autoDeleteTime: {}
      autoDeleteTtl: {}
      id: {}
      idleDeleteTtl: {}
      idleStartTime: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Create a cluster
      url: https://cloud.google.com/dataproc/docs/guides/create-cluster
  gcp.project.dataprocService.cluster.status:
    fields:
      detail: {}
      id: {}
      started: {}
      state: {}
      substate: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Create a cluster
      url: https://cloud.google.com/dataproc/docs/guides/create-cluster
  gcp.project.dataprocService.cluster.virtualClusterConfig:
    fields:
      auxiliaryServices: {}
      kubernetesCluster: {}
      parentResourcePath: {}
      stagingBucket: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Create a cluster
      url: https://cloud.google.com/dataproc/docs/guides/create-cluster
  gcp.project.dnsService:
    fields:
      managedZones: {}
      policies: {}
      projectId: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Cloud DNS overview
      url: https://cloud.google.com/dns/docs/overview/
  gcp.project.dnsService.managedzone:
    fields:
      created: {}
      description: {}
      dnsName: {}
      dnssecConfig: {}
      id: {}
      name: {}
      nameServerSet: {}
      nameServers: {}
      projectId: {}
      recordSets: {}
      visibility: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: DNS zones overview
      url: https://cloud.google.com/dns/docs/zones
  gcp.project.dnsService.policy:
    fields:
      description: {}
      enableInboundForwarding: {}
      enableLogging: {}
      id: {}
      name: {}
      networkNames: {}
      networks: {}
      projectId: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: DNS policies overview
      url: https://cloud.google.com/dns/docs/policies-overview
  gcp.project.dnsService.recordset:
    fields:
      name: {}
      projectId: {}
      rrdatas: {}
      signatureRrdatas: {}
      ttl: {}
      type: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: DNS records overview
      url: https://cloud.google.com/dns/docs/records-overview
  gcp.project.gkeService:
    fields:
      clusters: {}
      projectId: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: GKE overview
      url: https://cloud.google.com/kubernetes-engine/docs/concepts/kubernetes-engine-overview
  gcp.project.gkeService.cluster:
    fields:
      addonsConfig: {}
      autopilotEnabled: {}
      binaryAuthorization: {}
      clusterIpv4Cidr: {}
      confidentialNodesConfig: {}
      costManagementConfig: {}
      created: {}
      currentMasterVersion: {}
      databaseEncryption:
        min_mondoo_version: 8.3.0
      description: {}
      enableKubernetesAlpha: {}
      endpoint: {}
      expirationTime: {}
      id: {}
      identityServiceConfig: {}
      initialClusterVersion: {}
      ipAllocationPolicy: {}
      legacyAbac: {}
      location: {}
      locations: {}
      loggingService: {}
      masterAuth: {}
      masterAuthorizedNetworksConfig: {}
      monitoringService: {}
      name: {}
      network: {}
      networkConfig: {}
      networkPolicyConfig: {}
      nodePools: {}
      privateClusterConfig: {}
      projectId: {}
      releaseChannel: {}
      resourceLabels: {}
      shieldedNodesConfig: {}
      status: {}
      subnetwork: {}
      workloadIdentityConfig: {}
      zone: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: About cluster configuration choices
      url: https://cloud.google.com/kubernetes-engine/docs/concepts/types-of-clusters
  gcp.project.gkeService.cluster.addonsConfig:
    fields:
      cloudRunConfig: {}
      configConnectorConfig: {}
      dnsCacheConfig: {}
      gcePersistentDiskCsiDriverConfig: {}
      gcpFilestoreCsiDriverConfig: {}
      gcsFuseCsiDriverConfig: {}
      gkeBackupAgentConfig: {}
      horizontalPodAutoscaling: {}
      httpLoadBalancing: {}
      id: {}
      kubernetesDashboard: {}
      networkPolicyConfig: {}
      statefulHaConfig: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
  gcp.project.gkeService.cluster.ipAllocationPolicy:
    fields:
      clusterIpv4CidrBlock: {}
      clusterSecondaryRangeName: {}
      createSubnetwork: {}
      id: {}
      ipv6AccessType: {}
      nodeIpv4CidrBlock: {}
      servicesIpv4CidrBlock: {}
      servicesSecondaryRangeName: {}
      stackType: {}
      subnetworkName: {}
      tpuIpv4CidrBlock: {}
      useIpAliases: {}
      useRoutes: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Plan the required IP address allotment
      url: https://cloud.google.com/kubernetes-engine/docs/best-practices/networking#plan-ip-allotment
  gcp.project.gkeService.cluster.networkConfig:
    fields:
      datapathProvider: {}
      defaultSnatStatus: {}
      dnsConfig: {}
      enableCiliumClusterwideNetworkPolicy: {}
      enableFqdnNetworkPolicy: {}
      enableIntraNodeVisibility: {}
      enableL4IlbSubsetting: {}
      enableMultiNetworking: {}
      id: {}
      network: {}
      networkPath: {}
      privateIpv6GoogleAccess: {}
      serviceExternalIpsConfig: {}
      subnetwork: {}
      subnetworkPath: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Best practices for GKE networking
      url: https://cloud.google.com/kubernetes-engine/docs/best-practices/networking
  gcp.project.gkeService.cluster.nodepool:
    fields:
      autoscaling:
        min_mondoo_version: 9.0.0
      config: {}
      id: {}
      initialNodeCount: {}
      instanceGroupUrls: {}
      locations: {}
      management: {}
      name: {}
      networkConfig: {}
      status: {}
      version: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: About node pools
      url: https://cloud.google.com/kubernetes-engine/docs/concepts/node-pools
  gcp.project.gkeService.cluster.nodepool.autoscaling:
    fields:
      autoprovisioned: {}
      enabled: {}
      maxNodeCount: {}
      minNodeCount: {}
      totalMaxNodeCount: {}
      totalMinNodeCount: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - gcp
  gcp.project.gkeService.cluster.nodepool.config:
    fields:
      accelerators: {}
      advancedMachineFeatures: {}
      bootDiskKmsKey: {}
      confidentialNodes: {}
      diskSizeGb: {}
      diskType: {}
      gcfsConfig: {}
      gvnicConfig: {}
      id: {}
      imageType: {}
      kubeletConfig: {}
      labels: {}
      linuxNodeConfig: {}
      localSsdCount: {}
      machineType: {}
      metadata: {}
      minCpuPlatform: {}
      oauthScopes: {}
      preemptible: {}
      projectId: {}
      sandboxConfig: {}
      serviceAccount: {}
      serviceAccountEmail: {}
      shieldedInstanceConfig: {}
      spot: {}
      tags: {}
      taints: {}
      workloadMetadataMode: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: About node pools
      url: https://cloud.google.com/kubernetes-engine/docs/concepts/node-pools
  gcp.project.gkeService.cluster.nodepool.config.accelerator:
    fields:
      count: {}
      gpuPartitionSize: {}
      gpuSharingConfig: {}
      id: {}
      type: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Run GPUs in GKE Standard node pools
      url: https://cloud.google.com/kubernetes-engine/docs/how-to/gpus
  gcp.project.gkeService.cluster.nodepool.config.accelerator.gpuSharingConfig:
    fields:
      id: {}
      maxSharedClientsPerGpu: {}
      strategy: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Run GPUs in GKE Standard node pools
      url: https://cloud.google.com/kubernetes-engine/docs/how-to/gpus
  gcp.project.gkeService.cluster.nodepool.config.advancedMachineFeatures:
    fields:
      id: {}
      threadsPerCore: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Add and manage node pools
      url: https://cloud.google.com/kubernetes-engine/docs/how-to/node-pools
  gcp.project.gkeService.cluster.nodepool.config.confidentialNodes:
    fields:
      enabled: {}
      id: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Add and manage node pools
      url: https://cloud.google.com/kubernetes-engine/docs/how-to/node-pools
  gcp.project.gkeService.cluster.nodepool.config.gcfsConfig:
    fields:
      enabled: {}
      id: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Add and manage node pools
      url: https://cloud.google.com/kubernetes-engine/docs/how-to/node-pools
  gcp.project.gkeService.cluster.nodepool.config.gvnicConfig:
    fields:
      enabled: {}
      id: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Add and manage node pools
      url: https://cloud.google.com/kubernetes-engine/docs/how-to/node-pools
  gcp.project.gkeService.cluster.nodepool.config.kubeletConfig:
    fields:
      cpuCfsQuotaPeriod: {}
      cpuManagerPolicy: {}
      id: {}
      podPidsLimit: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Add and manage node pools
      url: https://cloud.google.com/kubernetes-engine/docs/how-to/node-pools
  gcp.project.gkeService.cluster.nodepool.config.linuxNodeConfig:
    fields:
      id: {}
      sysctls: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Add and manage node pools
      url: https://cloud.google.com/kubernetes-engine/docs/how-to/node-pools
  gcp.project.gkeService.cluster.nodepool.config.nodeTaint:
    fields:
      effect: {}
      id: {}
      key: {}
      value: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
  gcp.project.gkeService.cluster.nodepool.config.sandboxConfig:
    fields:
      id: {}
      type: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Add and manage node pools
      url: https://cloud.google.com/kubernetes-engine/docs/how-to/node-pools
  gcp.project.gkeService.cluster.nodepool.config.shieldedInstanceConfig:
    fields:
      enableIntegrityMonitoring: {}
      enableSecureBoot: {}
      id: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Add and manage node pools
      url: https://cloud.google.com/kubernetes-engine/docs/how-to/node-pools
  gcp.project.gkeService.cluster.nodepool.networkConfig:
    fields:
      id: {}
      performanceConfig: {}
      podIpv4CidrBlock: {}
      podRange: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Add and manage node pools
      url: https://cloud.google.com/kubernetes-engine/docs/how-to/node-pools
  gcp.project.gkeService.cluster.nodepool.networkConfig.performanceConfig:
    fields:
      id: {}
      totalEgressBandwidthTier: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Add and manage node pools
      url: https://cloud.google.com/kubernetes-engine/docs/how-to/node-pools
  gcp.project.iamService:
    fields:
      projectId: {}
      serviceAccounts: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: IAM overview
      url: https://cloud.google.com/iam/docs/overview
  gcp.project.iamService.serviceAccount:
    fields:
      description: {}
      disabled: {}
      displayName: {}
      email: {}
      keys: {}
      name: {}
      oauth2ClientId: {}
      projectId: {}
      uniqueId: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Identities for workloads
      url: https://cloud.google.com/iam/docs/workload-identities
  gcp.project.iamService.serviceAccount.key:
    fields:
      disabled: {}
      keyAlgorithm: {}
      keyOrigin: {}
      keyType: {}
      name: {}
      validAfterTime: {}
      validBeforeTime: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Create and delete service account keys
      url: https://cloud.google.com/iam/docs/keys-create-delete
  gcp.project.kmsService:
    fields:
      keyrings: {}
      locations: {}
      projectId: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Cloud KMS resources
      url: https://cloud.google.com/kms/docs/resource-hierarchy
  gcp.project.kmsService.keyring:
    fields:
      created: {}
      cryptokeys: {}
      location: {}
      name: {}
      projectId: {}
      resourcePath: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Create a key ring
      url: https://cloud.google.com/kms/docs/create-key-ring
  gcp.project.kmsService.keyring.cryptokey:
    fields:
      created: {}
      cryptoKeyBackend: {}
      destroyScheduledDuration: {}
      iamPolicy: {}
      importOnly: {}
      labels: {}
      name: {}
      nextRotation: {}
      primary: {}
      purpose: {}
      resourcePath: {}
      rotationPeriod: {}
      versionTemplate: {}
      versions: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Create a key ring
      url: https://cloud.google.com/kms/docs/create-key-ring
  gcp.project.kmsService.keyring.cryptokey.version:
    fields:
      algorithm: {}
      attestation: {}
      created: {}
      destroyEventTime: {}
      destroyed: {}
      externalProtectionLevelOptions: {}
      generated: {}
      importFailureReason: {}
      importJob: {}
      importTime: {}
      name: {}
      protectionLevel: {}
      reimportEligible: {}
      resourcePath: {}
      state: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Create a key ring
      url: https://cloud.google.com/kms/docs/create-key-ring
  gcp.project.kmsService.keyring.cryptokey.version.attestation:
    fields:
      certificateChains: {}
      cryptoKeyVersionName: {}
      format: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Create a key ring
      url: https://cloud.google.com/kms/docs/create-key-ring
  gcp.project.kmsService.keyring.cryptokey.version.attestation.certificatechains:
    fields:
      caviumCerts: {}
      cryptoKeyVersionName: {}
      googleCardCerts: {}
      googlePartitionCerts: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Create a key ring
      url: https://cloud.google.com/kms/docs/create-key-ring
  gcp.project.kmsService.keyring.cryptokey.version.externalProtectionLevelOptions:
    fields:
      cryptoKeyVersionName: {}
      ekmConnectionKeyPath: {}
      externalKeyUri: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Create a key ring
      url: https://cloud.google.com/kms/docs/create-key-ring
  gcp.project.loggingservice:
    fields:
      buckets: {}
      metrics: {}
      projectId: {}
      sinks: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Cloud Logging overview
      url: https://cloud.google.com/logging/docs/overview
  gcp.project.loggingservice.bucket:
    fields:
      cmekSettings: {}
      created: {}
      description: {}
      indexConfigs: {}
      lifecycleState: {}
      locked: {}
      name: {}
      projectId: {}
      restrictedFields: {}
      retentionDays: {}
      updated: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Cloud Logging overview
      url: https://cloud.google.com/logging/docs/overview
  gcp.project.loggingservice.bucket.indexConfig:
    fields:
      created: {}
      fieldPath: {}
      id: {}
      type: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Cloud Logging overview
      url: https://cloud.google.com/logging/docs/overview
  gcp.project.loggingservice.metric:
    fields:
      alertPolicies: {}
      description: {}
      filter: {}
      id: {}
      projectId: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Cloud Logging overview
      url: https://cloud.google.com/logging/docs/overview
  gcp.project.loggingservice.sink:
    fields:
      destination: {}
      filter: {}
      id: {}
      includeChildren: {}
      projectId: {}
      storageBucket: {}
      writerIdentity: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Cloud Logging overview
      url: https://cloud.google.com/logging/docs/overview
  gcp.project.monitoringService:
    fields:
      alertPolicies: {}
      projectId: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Cloud Monitoring overview
      url: https://cloud.google.com/monitoring/docs/monitoring-overview
  gcp.project.monitoringService.alertPolicy:
    fields:
      alertStrategy: {}
      combiner: {}
      conditions: {}
      created: {}
      createdBy: {}
      displayName: {}
      documentation: {}
      enabled: {}
      labels: {}
      name: {}
      notificationChannelUrls: {}
      projectId: {}
      updated: {}
      updatedBy: {}
      validity: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Alerting overview
      url: https://cloud.google.com/monitoring/alerts
  gcp.project.pubsubService:
    fields:
      projectId: {}
      snapshots: {}
      subscriptions: {}
      topics: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Alerting overview
      url: https://cloud.google.com/monitoring/alerts
  gcp.project.pubsubService.snapshot:
    fields:
      expiration: {}
      name: {}
      projectId: {}
      topic: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Overview of the Pub/Sub service
      url: https://cloud.google.com/pubsub/docs/pubsub-basics
  gcp.project.pubsubService.subscription:
    fields:
      config: {}
      name: {}
      projectId: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Overview of the Pub/Sub service
      url: https://cloud.google.com/pubsub/docs/pubsub-basics
  gcp.project.pubsubService.subscription.config:
    fields:
      ackDeadline: {}
      expirationPolicy: {}
      labels: {}
      projectId: {}
      pushConfig: {}
      retainAckedMessages: {}
      retentionDuration: {}
      subscriptionName: {}
      topic: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Subscription overview
      url: https://cloud.google.com/pubsub/docs/subscription-overview
  gcp.project.pubsubService.subscription.config.pushconfig:
    fields:
      attributes: {}
      configId: {}
      endpoint: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Push subscriptions
      url: https://cloud.google.com/pubsub/docs/push
  gcp.project.pubsubService.topic:
    fields:
      config: {}
      name: {}
      projectId: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Monitor topics within Pub/Sub
      url: https://cloud.google.com/pubsub/docs/monitor-topic
  gcp.project.pubsubService.topic.config:
    fields:
      kmsKeyName: {}
      labels: {}
      messageStoragePolicy: {}
      projectId: {}
      topicName: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Monitor topics within Pub/Sub
      url: https://cloud.google.com/pubsub/docs/monitor-topic
  gcp.project.pubsubService.topic.config.messagestoragepolicy:
    fields:
      allowedPersistenceRegions: {}
      configId: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Monitor topics within Pub/Sub
      url: https://cloud.google.com/pubsub/docs/monitor-topic
  gcp.project.redisService:
    fields:
      instances: {}
      projectId: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - gcp
  gcp.project.redisService.instance:
    fields:
      AuthorizedNetwork: {}
      Labels: {}
      authEnabled: {}
      availableMaintenanceVersions: {}
      connectMode: {}
      createTime: {}
      currentLocationId: {}
      customerManagedKey: {}
      displayName: {}
      host: {}
      labels: {}
      locationId: {}
      maintenanceVersion: {}
      memorySizeGb: {}
      name: {}
      nodes: {}
      persistenceIamIdentity: {}
      port: {}
      projectId: {}
      readEndpoint: {}
      readEndpointPort: {}
      redisConfigs: {}
      redisVersion: {}
      replicaCount: {}
      reservedIpRange: {}
      secondaryIpRange: {}
      state: {}
      statusMessage: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - gcp
  gcp.project.redisService.instance.nodeInfo:
    fields:
      id: {}
      projectId: {}
      zone: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - gcp
  gcp.project.sqlService:
    fields:
      instances: {}
      projectId: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Cloud SQL overview
      url: https://cloud.google.com/sql/docs/introduction
  gcp.project.sqlService.instance:
    fields:
      availableMaintenanceVersions: {}
      backendType: {}
      connectionName: {}
      created: {}
      currentDiskSize: {}
      databaseInstalledVersion: {}
      databaseVersion: {}
      databases: {}
      diskEncryptionConfiguration: {}
      diskEncryptionStatus: {}
      failoverReplica: {}
      gceZone: {}
      instanceType: {}
      ipAddresses: {}
      maintenanceVersion: {}
      masterInstanceName: {}
      maxDiskSize: {}
      name: {}
      project: {}
      projectId: {}
      region: {}
      replicaNames: {}
      serviceAccountEmailAddress: {}
      settings: {}
      state: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Cloud SQL overview
      url: https://cloud.google.com/sql/docs/introduction
  gcp.project.sqlService.instance.database:
    fields:
      charset: {}
      collation: {}
      instance: {}
      name: {}
      projectId: {}
      sqlserverDatabaseDetails: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Cloud SQL overview
      url: https://cloud.google.com/sql/docs/introduction
  gcp.project.sqlService.instance.ipMapping:
    fields:
      id: {}
      ipAddress: {}
      timeToRetire: {}
      type: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Cloud SQL overview
      url: https://cloud.google.com/sql/docs/introduction
  gcp.project.sqlService.instance.settings:
    fields:
      activationPolicy: {}
      activeDirectoryConfig: {}
      availabilityType: {}
      backupConfiguration: {}
      collation: {}
      connectorEnforcement: {}
      crashSafeReplicationEnabled: {}
      dataDiskSizeGb: {}
      dataDiskType: {}
      databaseFlags: {}
      databaseReplicationEnabled: {}
      deletionProtectionEnabled: {}
      denyMaintenancePeriods: {}
      insightsConfig: {}
      instanceName: {}
      ipConfiguration: {}
      locationPreference: {}
      maintenanceWindow: {}
      passwordValidationPolicy: {}
      pricingPlan: {}
      projectId: {}
      replicationType: {}
      settingsVersion: {}
      sqlServerAuditConfig: {}
      storageAutoResize: {}
      storageAutoResizeLimit: {}
      tier: {}
      timeZone: {}
      userLabels: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Cloud SQL overview
      url: https://cloud.google.com/sql/docs/introduction
  gcp.project.sqlService.instance.settings.backupconfiguration:
    fields:
      backupRetentionSettings: {}
      binaryLogEnabled: {}
      enabled: {}
      id: {}
      location: {}
      pointInTimeRecoveryEnabled: {}
      startTime: {}
      transactionLogRetentionDays: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Cloud SQL overview
      url: https://cloud.google.com/sql/docs/introduction
  gcp.project.sqlService.instance.settings.denyMaintenancePeriod:
    fields:
      endDate: {}
      id: {}
      startDate: {}
      time: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Cloud SQL overview
      url: https://cloud.google.com/sql/docs/introduction
  gcp.project.sqlService.instance.settings.ipConfiguration:
    fields:
      allocatedIpRange: {}
      authorizedNetworks: {}
      enablePrivatePathForGoogleCloudServices: {}
      id: {}
      ipv4Enabled: {}
      privateNetwork: {}
      requireSsl: {}
      sslMode: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
  gcp.project.sqlService.instance.settings.maintenanceWindow:
    fields:
      day: {}
      hour: {}
      id: {}
      updateTrack: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Cloud SQL overview
      url: https://cloud.google.com/sql/docs/introduction
  gcp.project.sqlService.instance.settings.passwordValidationPolicy:
    fields:
      complexity: {}
      disallowUsernameSubstring: {}
      enabledPasswordPolicy: {}
      id: {}
      minLength: {}
      passwordChangeInterval: {}
      reuseInterval: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Cloud SQL overview
      url: https://cloud.google.com/sql/docs/introduction
  gcp.project.storageService:
    fields:
      buckets: {}
      projectId: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Product overview of Cloud Storage
      url: https://cloud.google.com/storage/docs/introduction
  gcp.project.storageService.bucket:
    fields:
      created: {}
      encryption:
        min_mondoo_version: 9.0.0
      iamConfiguration: {}
      iamPolicy: {}
      id: {}
      labels: {}
      lifecycle:
        min_mondoo_version: 9.0.0
      location: {}
      locationType: {}
      name: {}
      projectId: {}
      projectNumber: {}
      retentionPolicy: {}
      storageClass: {}
      updated: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: About Cloud Storage buckets
      url: https://cloud.google.com/storage/docs/buckets
  gcp.project.storageService.bucket.lifecycleRule:
    fields:
      action: {}
      condition: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - gcp
  gcp.project.storageService.bucket.lifecycleRuleAction:
    fields:
      storageClass: {}
      type: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - gcp
  gcp.project.storageService.bucket.lifecycleRuleCondition:
    fields:
      age: {}
      createdBefore: {}
      customTimeBefore: {}
      daysSinceCustomTime: {}
      daysSinceNoncurrentTime: {}
      isLive: {}
      matchesPattern: {}
      matchesPrefix: {}
      matchesStorageClass: {}
      matchesSuffix: {}
      noncurrentTimeBefore: {}
      numNewerVersions: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - gcp
  gcp.projects:
    fields:
      children: {}
      list: {}
      parentId: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Creating and managing projects
      url: https://cloud.google.com/resource-manager/docs/creating-managing-projects
  gcp.recommendation:
    fields:
      additionalImpact: {}
      category: {}
      content: {}
      id: {}
      lastRefreshTime: {}
      name: {}
      primaryImpact: {}
      priority: {}
      projectId: {}
      recommender: {}
      state: {}
      zoneName: {}
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Recommendations
      url: https://cloud.google.com/recommender/docs/key-concepts
  gcp.resourcemanager.binding:
    fields:
      id: {}
      members: {}
      role: {}
    is_private: true
    min_mondoo_version: latest
    platform:
      name:
      - gcp
    refs:
    - title: Creating and managing projects
      url: https://cloud.google.com/resource-manager/docs/creating-managing-projects
  gcp.service:
    fields:
      enabled: {}
      name: {}
      parentName: {}
      projectId: {}
      state: {}
      title: {}
    min_mondoo_version: latest
    platform:
      name:
      - gcp
