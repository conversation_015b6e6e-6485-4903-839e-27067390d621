# Copyright (c) Mondoo, Inc.
# SPDX-License-Identifier: BUSL-1.1

packs:
  - uid: mondoo-googleworkspace-incident-response
    name: Google Workspace Incident Response Pack
    version: 1.1.0
    license: BUSL-1.1
    authors:
      - name: <PERSON><PERSON><PERSON>, <PERSON>
        email: <EMAIL>
    tags:
      mondoo.com/platform: google-workspace,saas
      mondoo.com/category: security
    docs:
      desc: |
        ### Overview

        The Google Workspace Incident Response query pack retrieves configuration data about your Google Workspace configuration during a security incident.

        ### Prerequisites

        1. Create/Select a GCP project
        2. Navigate to the [Google API Console](https://console.cloud.google.com/apis/dashboard). 
        3. Select "Enable APIs and Services" and enable the following APIs:
          - Admin SDK API 
          - Cloud Identity API 
          - Google Calendar API
          - Google Drive API
          - Gmail API
          - Google People API
        4. Create a service account for [Google Workspace](https://support.google.com/a/answer/7378726?product_name=UnuFlow&hl=en&visit_id=638041387835615758-**********&rd=1&src=supportwidget0&hl=en)
        5. Create credentials for the service account and download the json file
        6. Enter the following scopes in Security -> Access and data controls -> API controls, and select [Domain-wide Delegation](https://developers.google.com/workspace/guides/create-credentials#delegate_domain-wide_authority_to_your_service_account) 

          - https://www.googleapis.com/auth/admin.chrome.printers.readonly
          - https://www.googleapis.com/auth/admin.directory.customer.readonly
          - https://www.googleapis.com/auth/admin.directory.device.chromeos.readonly
          - https://www.googleapis.com/auth/admin.directory.device.mobile.readonly
          - https://www.googleapis.com/auth/admin.directory.domain.readonly
          - https://www.googleapis.com/auth/admin.directory.group.member.readonly
          - https://www.googleapis.com/auth/admin.directory.group.readonly
          - https://www.googleapis.com/auth/admin.directory.orgunit.readonly
          - https://www.googleapis.com/auth/admin.directory.resource.calendar.readonly
          - https://www.googleapis.com/auth/admin.directory.rolemanagement.readonly
          - https://www.googleapis.com/auth/admin.directory.user.alias.readonly
          - https://www.googleapis.com/auth/admin.directory.user.readonly
          - https://www.googleapis.com/auth/admin.directory.userschema.readonly
          - https://www.googleapis.com/auth/admin.reports.audit.readonly
          - https://www.googleapis.com/auth/admin.reports.usage.readonly
          - https://www.googleapis.com/auth/admin.directory.user.security
          - https://www.googleapis.com/auth/cloud-identity.groups.readonly

        ### Run query pack

        To run this query pack against a Google Workspace customer:

        ```bash
        export GOOGLEWORKSPACE_CREDENTIALS=$PWD/my-project-123456-1234ea722b12.json
        cnquery scan google-workspace --customer-id <CUSTOMERID> --impersonated-user-email <EMAIL>
        ```
    filters:
      - asset.platform == "googleworkspace" || asset.platform == "google-workspace"
    queries:
      - uid: mondoo-googleworkspace-incident-response-domain
        title: Google Workspace domains
        mql: googleworkspace.domains { domainName isPrimary verified }
      - uid: mondoo-googleworkspace-incident-response-user-mfa-status
        title: Google Workspace users' MFA status
        mql: googleworkspace.users { primaryEmail isEnforcedIn2Sv }
      - uid: mondoo-googleworkspace-incident-response-super-admins
        title: Google Workspace super admins
        mql: googleworkspace.report.users.where( security["isSuperAdmin"] == true) { userEmail  }
      - uid: mondoo-googleworkspace-incident-response-super-admins-without-2FA-enrolled
        title: Google Workspace super admins who are not enrolled in 2FA
        mql: googleworkspace.users.where(isEnrolledIn2Sv != true && isAdmin == true) {primaryEmail isEnrolledIn2Sv isAdmin}
      - uid: mondoo-googleworkspace-incident-response-users-without-2FA-enrolled
        title: Google Workspace user accounts that are not enrolled in 2FA
        mql: googleworkspace.users.where(isEnrolledIn2Sv != true) {primaryEmail isEnrolledIn2Sv isAdmin}
      - uid: mondoo-googleworkspace-incident-response-super-admins-without-hardware-based-2fa
        title: Super admin accounts that do not use hardware-based security keys
        mql: googleworkspace.report.users.where(security["isSuperAdmin"] == true && security["numSecurityKeys"] <= 0 ) {account['adminSetName'] security['numSecurityKeys']}
      - uid: mondoo-googleworkspace-incident-response-config-drift-recovery-email
        title: Primary and recovery email accounts of all Google Workspace users
        mql: googleworkspace.users {primaryEmail recoveryEmail}
