# Copyright (c) Mondoo, Inc.
# SPDX-License-Identifier: BUSL-1.1

packs:
  - uid: mondoo-incident-response-github-org
    name: GitHub Organization Incident Response Pack
    version: 1.1.0
    license: BUSL-1.1
    authors:
      - name: <PERSON><PERSON><PERSON>, Inc
        email: <EMAIL>
    tags:
      mondoo.com/platform: github,saas
      mondoo.com/category: security
    docs:
      desc: |
        ### Overview

        The GitHub Organization Incident Response Pack by Mondoo query pack retrieves configuration data about GitHub organizations and the repositories within them for investigation during a security incident.

        ### Prerequisites

        To run this query pack, you will need a [GitHub personal access token](https://docs.github.com/en/authentication/keeping-your-account-and-data-secure/creating-a-personal-access-token) with read access to the GitHub organization you are scanning.

        ### Run query pack

        To run this query pack against a GitHub Organization:

        ```bash
        export GITHUB_TOKEN=<your personal access token>
        cnquery scan github org <org_name> -f mondoo-github-org-incident-response.mql.yaml
        ```
    filters:
      - asset.platform == "github-org"
    queries:
      - uid: mondoo-incident-response-github-org-name
        title: GitHub Organization Name
        mql: |
          github.organization.name
      - uid: mondoo-incident-response-github-org-login
        title: GitHub Organization Login
        mql: |
          github.organization.login
      - uid: mondoo-incident-response-github-org-description
        title: GitHub Organization description
        mql: |
          github.organization.description
      - uid: mondoo-incident-response-github-org-mfa-status
        title: GitHub Organization MFA status
        docs:
          desc: |
            This query retrieves whether multi-factor authentication is required for users of the organization. A null value means the API token used to query the information doesn't have sufficient permissions in the organization. The API token must have owner permissions in the organization to access this data.
        mql: |
          github.organization.twoFactorRequirementEnabled
      - uid: mondoo-incident-response-github-org-owners
        title: GitHub Organization Owners
        docs:
          desc: |
            This query retrieves all GitHub organization owners.
        mql: |
          github.organization.owners.length
          github.organization {
            owners {
              name
              email
              login
            }
          }
      - uid: mondoo-incident-response-github-org-members
        title: GitHub Organization Members
        docs:
          desc: |
            This query retrieves all of the members of the GitHub organization.
        mql: |
          github.organization.members.length
          github.organization {
            members {
              name
              login
              email
            }
          }
      - uid: mondoo-incident-response-github-org-teams
        title: GitHub Organization Teams
        docs:
          desc: |
            This query retrieves all GitHub organization teams.
        mql: |
          github.organization {
            teams {
              slug
              privacy
              defaultPermission
              members {
                login
                email
                name
              }
            }
          }
      - uid: mondoo-incident-response-github-private-repos
        title: GitHub Organization private repositories
        docs:
          desc: |
            This query retrieves all of the public repositories within the GitHub organization. The query returns the repo's name and whether the default branch is [protected](https://docs.github.com/en/repositories/configuring-branches-and-merges-in-your-repository/defining-the-mergeability-of-pull-requests/about-protected-branches) using protection rules.
        mql: |
          github.organization.repositories.
            where( private == false ) {
              name
              branches.
                where( isDefault ) {
                  isProtected
                }
            }
      - uid: mondoo-incident-response-github-packages
        title: GitHub Organization private repositories
        docs:
          desc: |
            This query retrieves the packages published to GHCR.io.
        mql: |
          github.organization {
            packages {
              name
              visibility
              packageType
              owner {
                name
              }
            }
          }
