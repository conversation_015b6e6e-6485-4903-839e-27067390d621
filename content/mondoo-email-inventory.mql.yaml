# Copyright (c) Mondoo, Inc.
# SPDX-License-Identifier: BUSL-1.1

packs:
  - uid: mondoo-email-inventory
    name: Email Inventory Pack
    version: 1.0.0
    license: BUSL-1.1
    authors:
      - name: <PERSON><PERSON><PERSON>, Inc
        email: <EMAIL>
    tags:
      mondoo.com/platform: host,network
      mondoo.com/category: security
    docs:
      desc: |
        ### Overview

        The Email Inventory Pack by Mondoo query pack retrieves information about Email entries.

        ### Prerequisites

        To run this query pack, you will need to install the cnquery binary ([Get Started with cnquery](https://mondoo.com/docs/cnquery/)).

        ### Run query pack

        To run this query pack against a Domain:

        ```bash
        cnquery scan host <domain> -f mondoo-mail-inventory.mql.yaml
        ```
    filters: asset.family.contains('network')
    queries:
      - uid: mondoo-email-inventory-mail-records
        title: Retrieve reverse IP Lookup PTR record
        mql: |
          reverseDNSDomain =
            dns.params.A.rData.first.split(".")[3] + "."
              + dns.params.A.rData.first.split(".")[2] + "."
              +  dns.params.A.rData.first.split(".")[1] + "."
              +  dns.params.A.rData.first.split(".")[0]
              + ".in-addr.arpa"
          dns(reverseDNSDomain).params.PTR
      - uid: mondoo-email-inventory-spf-record
        title: Retrieve SPF record
        mql: dns.params.TXT
      - uid: mondoo-email-inventory-dmarc-entry
        title: Retrieve DMARC DNS entry
        mql: dns("_dmarc."+domainName.fqdn).params.TXT
      - uid: mondoo-email-inventory-dkim-configuration
        title: Retrieve DKIM entry
        props:
          - uid: mondooEmailSecurityDkimSelectors
            title: Define a list of valid DKIM selectors
            mql: |
              [
                "google",
                "selector1",
                "selector2",
                "k1",
                "dkim",
                "mx",
                "mailjet"
              ]
        mql: |
          props.mondooEmailSecurityDkimSelectors{ dns(_+"._domainkey."+domainName.fqdn).params['TXT'] }
          