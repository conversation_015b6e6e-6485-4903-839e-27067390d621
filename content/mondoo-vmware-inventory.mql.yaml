# Copyright (c) Mondoo, Inc.
# SPDX-License-Identifier: BUSL-1.1

packs:
  - uid: mondoo-vmware-asset-inventory
    name: VMware Asset Inventory Pack
    version: 1.1.0
    license: BUSL-1.1
    authors:
      - name: <PERSON><PERSON><PERSON>, Inc
        email: <EMAIL>
    tags:
      mondoo.com/platform: vmware,vmware-esxi
      mondoo.com/category: security
    docs:
      desc: |
        ## Overview

        VMware vCenter Asset Inventory Pack by Mondoo retrieves data about vCenter and its ESXi hosts.

        ### Run query pack

        To run this query pack against VMware vCenter:

        ```bash
        cnquery <NAME_EMAIL>@************ --ask-pass -f core/mondoo-vmware-inventory.mql.yaml
        ```

        ## Join the community!

        Our goal is to build policies that are simple to deploy, accurate, and actionable. 

        If you have any suggestions for improving this policy, or if you need support, [join the Mondoo community](https://github.com/orgs/mondoohq/discussions) in GitHub Discussions.
    filters: asset.platform == "vmware-esxi" || asset.platform == "vmware-vsphere"
    queries:
      - uid: mondoo-vmware-asset-inventory-vcenter-datacenters
        title: VMware vSphere Datacenters
        filters: asset.platform == "vmware-vsphere"
        mql: |
          vsphere.datacenters { name }
      - uid: mondoo-vmware-asset-inventory-vcenter-clusters
        title: VMware vSphere Clusters per Datacenter
        filters: asset.platform == "vmware-vsphere"
        mql: |
          vsphere.datacenters { clusters }
      - uid: mondoo-vmware-asset-inventory-vcenter-vms
        title: VMware vSphere VMs per Datacenters
        filters: asset.platform == "vmware-vsphere"
        mql: |
          vsphere.datacenters { vms { name advancedSettings["guestInfo.detailed.data"] properties["guest"]["guestState"] } }
      - uid: mondoo-vmware-asset-inventory-esxi-kernel-modules
        title: VMware ESXi Kernel modules
        filters: asset.platform == "vmware-esxi"
        mql: |
          vsphere.host.kernelModules
      - uid: mondoo-vmware-asset-inventory-esxi-installed-packages
        title: VMware ESXi Installed packages
        filters: asset.platform == "vmware-esxi"
        mql: |
          esxi.host.packages
      - uid: mondoo-vmware-asset-inventory-esxi-services
        title: VMware ESXi Services
        filters: asset.platform == "vmware-esxi"
        mql: |
          esxi.host.services
      - uid: mondoo-vmware-asset-inventory-esxi-acceptance-level
        title: VMware ESXi Acceptance Level
        filters: asset.platform == "vmware-esxi"
        mql: |
          esxi.host.acceptanceLevel
      - uid: mondoo-vmware-asset-inventory-esxi-ntp-server
        title: VMware ESXi NTP servers
        filters: asset.platform == "vmware-esxi"
        mql: |
          esxi.host.ntp.server
      - uid: mondoo-vmware-asset-inventory-esxi-ntp-config
        title: VMware ESXi NTP configuration
        filters: asset.platform == "vmware-esxi"
        mql: |
          esxi.host.ntp.config
      - uid: mondoo-vmware-asset-inventory-esxi-fileSystemVolume
        title: VMware ESXi File System Volume
        filters: asset.platform == "vmware-esxi"
        mql: |
          esxi.host.properties["config"]["fileSystemVolume"]
      - uid: mondoo-vmware-asset-inventory-esxi-firewall
        title: VMware ESXi Firewall
        filters: asset.platform == "vmware-esxi"
        mql: |
          esxi.host.properties["config"]["firewall"]
      - uid: mondoo-vmware-asset-inventory-esxi-adapters
        title: VMware ESXi Physical Adapters
        filters: asset.platform == "vmware-esxi"
        mql: |
          esxi.host.adapters
      - uid: mondoo-vmware-asset-inventory-esxi-standardSwitch
        title: VMware ESXi Standard vSwitch
        filters: asset.platform == "vmware-esxi"
        mql: |
          esxi.host.standardSwitch

