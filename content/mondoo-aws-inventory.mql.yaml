# Copyright (c) Mon<PERSON>o, Inc.
# SPDX-License-Identifier: BUSL-1.1

packs:
  - uid: mondoo-asset-inventory-aws
    name: AWS Asset Inventory Pack
    version: 1.1.0
    license: BUSL-1.1
    authors:
      - name: <PERSON><PERSON><PERSON>, Inc
        email: <EMAIL>
    tags:
      mondoo.com/platform: aws,cloud
      mondoo.com/category: best-practices
    docs:
      desc: |
        The AWS Asset Inventory Pack retrieves information about AWS accounts for asset inventory.
    groups:
      - uid: mondoo-asset-inventory-aws-group
        title: AWS Asset Inventory Pack Group
        filters: |
          asset.runtime == "aws"
        queries:
          - uid: mondoo-asset-inventory-aws-account-id
          - uid: mondoo-asset-inventory-aws-enabled-regions
          - uid: mondoo-asset-inventory-aws-vpcs
          - uid: mondoo-asset-inventory-aws-iam-users
          - uid: mondoo-asset-inventory-aws-iam-groups
          - uid: mondoo-asset-inventory-aws-iam-roles
          - uid: mondoo-asset-inventory-aws-iam-policies
          - uid: mondoo-asset-inventory-aws-ec2-security-groups
          - uid: mondoo-asset-inventory-aws-ec2-volumes
          - uid: mondoo-asset-inventory-aws-ec2-retrieve-all-data
          - uid: mondoo-asset-inventory-aws-rds-dbclusters-all-data
          - uid: mondoo-asset-inventory-aws-rds-dbinstances-all-data
          - uid: mondoo-asset-inventory-aws-s3-retrieve-all-data
          - uid: mondoo-asset-inventory-aws-eks-clusters
          - uid: mondoo-asset-inventory-aws-lambda
          - uid: mondoo-asset-inventory-aws-access-analyzer
          - uid: mondoo-asset-inventory-aws-acm-certificates
          - uid: mondoo-asset-inventory-aws-cloudtrail-trails

queries:
      - uid: mondoo-asset-inventory-aws-account-id
        filters: |
          asset.platform == "aws"
        title: AWS account ID
        mql: |
          aws.account.id



      - uid: mondoo-asset-inventory-aws-enabled-regions
        title: Regions enabled in the AWS account
        filters: |
          asset.platform == "aws"
        docs:
          desc: |
            This query retrieves all AWS regions enabled in the account
        mql: |
          aws { regions }



      - uid: mondoo-asset-inventory-aws-vpcs
        title: VPCs
        docs:
          desc: |
            This query retrieves all of the configuration data for AWS VPCs
        variants:
          - uid: mondoo-asset-inventory-aws-vpcs-all
          - uid: mondoo-asset-inventory-aws-vpcs-single
      - uid: mondoo-asset-inventory-aws-vpcs-all
        filters: |
          asset.platform == "aws"
        mql: |
          aws.vpcs
      - uid: mondoo-asset-inventory-aws-vpcs-single
        filters: |
          asset.platform == "aws-vpc"
        mql: |
          aws.vpc



      - uid: mondoo-asset-inventory-aws-iam-users
        title: IAM users
        docs:
          desc: |
            This query retrieves data for all IAM users
        variants:
          - uid: mondoo-asset-inventory-aws-iam-users-all
          - uid: mondoo-asset-inventory-aws-iam-users-single
      - uid: mondoo-asset-inventory-aws-iam-users-all
        filters: |
          asset.platform == "aws"
        mql: |
          aws.iam.users
      - uid: mondoo-asset-inventory-aws-iam-users-single
        filters: |
          asset.platform == "aws-iam-user"
        mql: |
          aws.iam.user



      - uid: mondoo-asset-inventory-aws-iam-groups
        title: IAM groups
        docs:
          desc: |
            This query retrieves all of the IAM groups.
        variants:
          - uid: mondoo-asset-inventory-aws-iam-groups-all
          - uid: mondoo-asset-inventory-aws-iam-groups-single
      - uid: mondoo-asset-inventory-aws-iam-groups-all
        filters: |
          asset.platform == "aws"
        mql: |
          aws.iam.groups
      - uid: mondoo-asset-inventory-aws-iam-groups-single
        filters: |
          asset.platform == "aws-iam-group"
        mql: |
          aws.iam.group



      - uid: mondoo-asset-inventory-aws-iam-roles
        title: IAM roles
        docs:
          desc: |
            This query retrieves all IAM Roles
        variants:
          - uid: mondoo-asset-inventory-aws-iam-roles-all
      - uid: mondoo-asset-inventory-aws-iam-roles-all
        filters: |
          asset.platform == "aws"
        mql: |
          aws.iam.roles



      - uid: mondoo-asset-inventory-aws-iam-policies
        title: Attached IAM policies
        filters: |
          asset.platform == "aws"
        docs:
          desc: |
            This query retrieves all IAM policies attached to a user, group, or role.
        mql: aws.iam.policies.where( attachmentCount > 0 )



      - uid: mondoo-asset-inventory-aws-ec2-security-groups
        title: EC2 Security Groups
        docs:
          desc: |
            This query retrieves all AWS EC2 Security Groups
        variants:
          - uid: mondoo-asset-inventory-aws-ec2-security-groups-all
          - uid: mondoo-asset-inventory-aws-ec2-security-groups-single
      - uid: mondoo-asset-inventory-aws-ec2-security-groups-all
        filters: |
          asset.platform == "aws"
        mql: |
          aws.ec2.securityGroups
      - uid: mondoo-asset-inventory-aws-ec2-security-groups-single
        filters: |
          asset.platform == "aws-security-group"
        mql: |
          aws.ec2.securitygroup



      - uid: mondoo-asset-inventory-aws-ec2-volumes
        title: EBS volumes
        docs:
          desc: |
            This query retrieves all AWS EBS volumes
        variants:
          - uid: mondoo-asset-inventory-aws-ec2-volumes-all
          - uid: mondoo-asset-inventory-aws-ec2-volumes-single
      - uid: mondoo-asset-inventory-aws-ec2-volumes-all
        filters: |
          asset.platform == "aws"
        mql: |
          aws.ec2.volumes
      - uid: mondoo-asset-inventory-aws-ec2-volumes-single
        filters: |
          asset.platform == "aws-ebs-volume"
        mql: |
          aws.ec2.volume



      - uid: mondoo-asset-inventory-aws-ec2-retrieve-all-data
        title: Running EC2 instances
        variants:
          - uid: mondoo-asset-inventory-aws-ec2-retrieve-all-data-all
          - uid: mondoo-asset-inventory-aws-ec2-retrieve-all-data-single
      - uid: mondoo-asset-inventory-aws-ec2-retrieve-all-data-all
        filters: |
          asset.platform == "aws"
        mql: |
          aws.ec2.instances.where(state != "terminated")
      - uid: mondoo-asset-inventory-aws-ec2-retrieve-all-data-single
        filters: |
          asset.platform == "aws-ec2-instance"
          aws.ec2.instance.state != "terminated"
        mql: |
          aws.ec2.instance



      - uid: mondoo-asset-inventory-aws-rds-dbclusters-all-data
        title: RDS database clusters configuration
        variants:
          - uid: mondoo-asset-inventory-aws-rds-dbclusters-all-data-all
      - uid: mondoo-asset-inventory-aws-rds-dbclusters-all-data-all
        filters: |
          asset.platform == "aws"
        mql: |
          aws.rds.clusters



      - uid: mondoo-asset-inventory-aws-rds-dbinstances-all-data
        title: RDS database instances
        variants:
          - uid: mondoo-asset-inventory-aws-rds-dbinstances-all-data-all
          - uid: mondoo-asset-inventory-aws-rds-dbinstances-all-data-single
      - uid: mondoo-asset-inventory-aws-rds-dbinstances-all-data-all
        filters: |
          asset.platform == "aws"
        mql: |
          aws.rds.instances
      - uid: mondoo-asset-inventory-aws-rds-dbinstances-all-data-single
        filters: |
          asset.platform == "aws-rds-dbinstance"
        mql: |
          aws.rds.dbinstance



      - uid: mondoo-asset-inventory-aws-s3-retrieve-all-data
        title: S3 buckets
        variants:
          - uid: mondoo-asset-inventory-aws-s3-retrieve-all-data-all
          - uid: mondoo-asset-inventory-aws-s3-retrieve-all-data-single
      - uid: mondoo-asset-inventory-aws-s3-retrieve-all-data-all
        filters: |
          asset.platform == "aws"
        mql: |
          aws.s3.buckets
      - uid: mondoo-asset-inventory-aws-s3-retrieve-all-data-single
        filters: |
          asset.platform == "aws-s3-bucket"
        mql: |
          aws.s3.bucket



      - uid: mondoo-asset-inventory-aws-eks-clusters
        title: EKS clusters
        variants:
          - uid: mondoo-asset-inventory-aws-eks-clusters-all
      - uid: mondoo-asset-inventory-aws-eks-clusters-all
        filters: |
          asset.platform == "aws"
        mql: |
          aws.eks.clusters



      - uid: mondoo-asset-inventory-aws-lambda
        title: Lambda functions
        variants:
          - uid: mondoo-asset-inventory-aws-lambda-all
          - uid: mondoo-asset-inventory-aws-lambda-single
      - uid: mondoo-asset-inventory-aws-lambda-all
        filters: |
          asset.platform == "aws"
        mql: |
          aws.lambda.functions
      - uid: mondoo-asset-inventory-aws-lambda-single
        filters: |
          asset.platform == "aws-lambda-function"
        mql: |
          aws.lambda.function



      - uid: mondoo-asset-inventory-aws-access-analyzer
        title: Access Analyzers
        variants:
          - uid: mondoo-asset-inventory-aws-access-analyzer-all
      - uid: mondoo-asset-inventory-aws-access-analyzer-all
        filters: |
          asset.platform == "aws"
        mql: |
          aws.accessAnalyzer.analyzers



      - uid: mondoo-asset-inventory-aws-acm-certificates
        title: Certificate Manager certificates
        variants:
          - uid: mondoo-asset-inventory-aws-acm-certificates-all
      - uid: mondoo-asset-inventory-aws-acm-certificates-all
        filters: |
          asset.platform == "aws"
        mql: |
          aws.acm.certificates



      - uid: mondoo-asset-inventory-aws-cloudtrail-trails
        title: CloudTrail trails
        variants:
          - uid: mondoo-asset-inventory-aws-cloudtrail-trails-all
          - uid: mondoo-asset-inventory-aws-cloudtrail-trails-single
      - uid: mondoo-asset-inventory-aws-cloudtrail-trails-all
        filters: |
          asset.platform == "aws"
        mql: |
          aws.cloudtrail.trails
      - uid: mondoo-asset-inventory-aws-cloudtrail-trails-single
        filters: |
          asset.platform == "aws-cloudtrail-trail"
        mql: |
          aws.cloudtrail.trail
