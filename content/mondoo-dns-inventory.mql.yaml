# Copyright (c) Mondoo, Inc.
# SPDX-License-Identifier: BUSL-1.1

packs:
  - uid: mondoo-dns-inventory
    name: DNS Inventory Pack
    version: 1.0.0
    license: BUSL-1.1
    authors:
      - name: <PERSON><PERSON><PERSON>, <PERSON>
        email: <EMAIL>
    tags:
      mondoo.com/platform: host,network
      mondoo.com/category: security
    docs:
      desc: |
        ### Overview

        The DNS Inventory Pack by Mondoo query pack retrieves information about DNS entries.

        ### Prerequisites

        To run this query pack, you will need to install the cnquery binary ([Get Started with cnquery](https://mondoo.com/docs/cnquery/)).

        ### Run query pack

        To run this query pack against a Domain:

        ```bash
        cnquery scan host <domain> -f mondoo-dns-inventory.mql.yaml
        ```
    filters:
      - asset.family.contains('network')
    queries:
      - uid: mondoo-dns-inventory-dns-records
        title: Retrieve information about DNS records
        mql: dns.params
      - uid: mondoo-dns-inventory-dns-mx-records
        title: Retrieve information about the MX records
        filters: dns.params.MX.name != empty
        mql:  dns.mx { domainName preference }