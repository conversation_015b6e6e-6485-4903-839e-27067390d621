# Copyright (c) Mondoo, Inc.
# SPDX-License-Identifier: BUSL-1.1

packs:
  - uid: mondoo-linux-incident-response
    name: Linux Incident Response Pack
    version: 1.2.0
    license: BUSL-1.1
    authors:
      - name: <PERSON><PERSON><PERSON>, Inc
        email: <EMAIL>
    tags:
      mondoo.com/platform: linux
      mondoo.com/category: security
    filters:
      - asset.family.contains("linux")
    queries:
      - uid: mondoo-linux-incident-response-installed-kernel
        title: Installed kernels
        filters: mondoo.capabilities.contains("run-command")
        mql: kernel.installed
      - uid: mondoo-linux-incident-response-kernel-info
        title: Running kernel version
        filters: mondoo.capabilities.contains("run-command")
        mql: kernel.info
      - uid: mondoo-linux-incident-response-kernel-modules
        title: Kernel modules
        mql: kernel.modules { name loaded }
      - uid: mondoo-linux-incident-response-processes
        title: Running processes
        filters: mondoo.capabilities.contains("run-command")
        mql: processes { pid  command }
      - uid: mondoo-linux-incident-response-mounts
        title: Mounted devices
        mql: mount.list { path fstype device options }
      - uid: mondoo-linux-incident-response-listening-ports
        title: Listening ports
        filters: mondoo.capabilities.contains("run-command")
        mql: ports.listening
      - uid: mondoo-linux-incident-response-uptime
        title: Operating system uptime
        filters: mondoo.capabilities.contains("run-command")
        mql: os.uptime
      - uid: mondoo-linux-incident-response-installed-packages
        title: Installed packages
        mql: packages { name version arch installed }
      - uid: mondoo-linux-incident-response-running-services
        title: Running services
        mql: services.where(running == true) { name running enabled masked type }
