# Copyright (c) Mondoo, Inc.
# SPDX-License-Identifier: BUSL-1.1

packs:
  - uid: mondoo-slack-inventory
    name: Slack Inventory Pack
    version: 1.0.0
    license: BUSL-1.1
    authors:
      - name: <PERSON><PERSON><PERSON>, Inc
        email: <EMAIL>
    tags:
      mondoo.com/platform: slack-team,saas
      mondoo.com/category: best-practices
    docs:
      desc: |
        ### Overview

        The Slack Inventory Pack retrieves information about Slack teams for asset inventory.

        ### Prerequisites

        To run this query pack, you will need access to the Slack API. To get a token, you need to create an App for the Slack workspace 
        and assign the appropriate permissions:

        1. Sign in to [the Slack website](https://api.slack.com/apps/), and view "Your Apps"
        2. Select "Create New App"
        3. Select "From scratch"
        4. Enter an "App Name" e.g. cnquery and select the workspace, then select "Create App"
        5. In the section "Add features & functionality" select "Permissions"
        6. Scroll to "Scopes" and then "User Token Scopes"

        Note: Bots are very limited in their access; therefore we need to set the user scopes

        7. Add the required permissions to "User Token Scopes"

        | OAuth Scope  |
        | ---- | 
        | [channels:read](https://api.slack.com/scopes/channels:read) | 
        | [groups:read](https://api.slack.com/scopes/groups:read) |
        | [im:read](https://api.slack.com/scopes/im:read) |
        | [mpim:read](https://api.slack.com/scopes/mpim:read) | 
        | [team:read](https://api.slack.com/scopes/team:read) | 
        | [usergroups:read](https://api.slack.com/scopes/usergroups:read) | 
        | [users:read](https://api.slack.com/scopes/users:read) |

        8. Scroll up to "OAuth Tokens for Your Workspace" and select "Install to Workspace"
        9. Copy the provided "User OAuth Token", it will look like `*******************************************************************************`

        ### Run query pack

        To run this query pack against a Slack workspace:

        ```bash
        export SLACK_TOKEN=xoxp-TOKEN
        cnquery scan slack --query-pack mondoo-slack-inventory
        ```
    filters:
      - asset.platform == "slack" || asset.platform == "slack-team"
    queries:
      - uid: mondoo-slack-inventory-team-domain
        title: Slack Team Domain
        mql: |
          slack.team.domain
      - uid: mondoo-slack-inventory-team-id
        title: Slack Team ID
        mql: |
          slack.team.id
      - uid: mondoo-slack-inventory-mfa-status
        title: Slack Team MFA status
        docs:
          desc: |
            This query retrieves the status of whether MFA is configured for all users.
        mql: |
          slack.users { id name profile["email"] isBot teamId has2FA }
      - uid: mondoo-slack-inventory-owners
        title: Slack Team Owners
        docs:
          desc: |
            This query retrieves the list of all users with the Owner privilege.
        mql: |
          slack.users.owners.length
          slack.users.owners { id name profile["email"] isBot teamId has2FA }
      - uid: mondoo-slack-inventory-admins
        title: Slack Admins
        docs:
          desc: |
            This query retrieves the list of all users with the Admin privilege.
        mql: slack.users.admins { id name }
      - uid: mondoo-slack-inventory-external-channels
        title: Externally shared channels
        docs:
          desc: |
            This query retrieves the list of all channels that have been externally shared.
        mql: slack.conversations.where(isExtShared == true) { id name }
