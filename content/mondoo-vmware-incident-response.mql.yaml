# Copyright (c) Mondoo, Inc.
# SPDX-License-Identifier: BUSL-1.1

packs:
  - uid: mondoo-vmware-incident-response
    name: VMware vCenter Incident Response Pack
    version: 1.1.0
    license: BUSL-1.1
    authors:
      - name: <PERSON><PERSON><PERSON>, Inc
        email: <EMAIL>
    tags:
      mondoo.com/platform: vmware,vmware-esxi
      mondoo.com/category: security
    docs:
      desc: |
        ## Overview

        VMware vCenter Incident Response Pack by Mondoo retrieves data about vCenter and its ESXi hosts.

        ### Run query pack

        To run this query pack against VMware vCenter:

        ```bash
        cnquery <NAME_EMAIL>@************ --ask-pass -f core/mondoo-vmware-incident-response.mql.yaml
        ```

        ## Join the community!

        Our goal is to build policies that are simple to deploy, accurate, and actionable. 

        If you have any suggestions for improving this policy, or if you need support, [join the Mondoo community](https://github.com/orgs/mondoohq/discussions) in GitHub Discussions.
    filters:
      - asset.platform == "vmware-esxi"
    queries:
      - uid: mondoo-vmware-incident-response-kernel-modules
        title: Kernel modules
        mql: vsphere.host.kernelModules
      - uid: mondoo-vmware-incident-response-installed-packages
        title: Installed packages
        mql: vsphere.host.packages
      - uid: mondoo-vmware-incident-response-running-services
        title: All services
        mql: vsphere.host.services
        refs:
          - title: VMSA-2021-0002
            url: https://www.vmware.com/security/advisories/VMSA-2021-0002.html
          - title: How to Disable/Enable the SLP Service on VMware ESXi (76372)
            url: https://kb.vmware.com/s/article/76372
      - uid: mondoo-vmware-incident-response-acceptance-level
        title: Host acceptance level
        docs:
          desc: The host acceptance level determines which VIBs can be installed on a host.
        mql: vsphere.host.acceptanceLevel
        refs:
          - title:
            url: https://docs.vmware.com/en/VMware-vSphere/6.5/com.vmware.vsphere.upgrade.doc/GUID-27BBBAB8-01EA-4238-8140-1C3C3EFC0AA6.html
      - uid: mondoo-vmware-incident-response-ntp-servers
        title: Configured NTP servers
        mql: vsphere.host.ntp.server
