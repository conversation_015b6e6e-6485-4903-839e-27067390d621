# Copyright (c) Mondoo, Inc.
# SPDX-License-Identifier: BUSL-1.1

packs:
  - uid: mondoo-asset-inventory-gcp
    name: GCP Asset Inventory Pack
    version: 1.1.0
    license: BUSL-1.1
    authors:
      - name: <PERSON><PERSON><PERSON>, Inc
        email: <EMAIL>
    tags:
      mondoo.com/platform: gcp,gcp-project,cloud
      mondoo.com/category: best-practices
    docs:
      desc: |
        The GCP Asset Inventory by Mondoo query pack retrieves information about GCP projects for asset inventory.
    groups:
      - uid: mondoo-asset-inventory-gcp-group
        title: GCP Asset Inventory Pack Group
        filters: |
          asset.runtime == "gcp"
        queries:
          - uid: mondoo-asset-inventory-gcp-project-info
          - uid: mondoo-asset-inventory-gcp-project-owners
          - uid: mondoo-asset-inventory-gcp-project-editors
          - uid: mondoo-asset-inventory-gcp-iam-roles
          - uid: mondoo-asset-inventory-gcp-enabled-services
          - uid: mondoo-asset-inventory-gcp-gke-clusters-count
          - uid: mondoo-asset-inventory-gcp-gke-clusters-data
          - uid: mondoo-asset-inventory-gcp-compute-instances-count
          - uid: mondoo-asset-inventory-gcp-compute-instances-data
          - uid: mondoo-asset-inventory-gcp-compute-instances-public
          - uid: mondoo-asset-inventory-gcp-compute-networks-count
          - uid: mondoo-asset-inventory-gcp-compute-networks-data
queries:
      - uid: mondoo-asset-inventory-gcp-project-info
        title: GCP Project Information
        filters: asset.platform == "gcp-project"
        mql: |
          gcp.project {
            name
            id
            number
            state
            labels
          }



      - uid: mondoo-asset-inventory-gcp-project-owners
        title: GCP project owners
        filters: asset.platform == "gcp-project"
        docs:
          desc: |
            This query retrieves data for all owners of the GCP project
        mql: |
          gcp.project.iamPolicy.where( role == "roles/owner" ) {
            id
            members
          }



      - uid: mondoo-asset-inventory-gcp-project-editors
        title: GCP project editors
        filters: asset.platform == "gcp-project"
        docs:
          desc: |
            This query retrieves data for all editors of the GCP project
        mql: |
          gcp.project.iamPolicy.where( role == "roles/editors" ) {
            id
            members
          }



      - uid: mondoo-asset-inventory-gcp-iam-roles
        title: IAM Policy roles
        filters: asset.platform == "gcp-project"
        docs:
          desc: |
            This query retrieves all roles defined for a GCP project
        mql: gcp.project.iamPolicy { role }



      - uid: mondoo-asset-inventory-gcp-enabled-services
        title: Services enabled in the GCP project
        filters: asset.platform == "gcp-project"
        docs:
          desc: |
            This query retrieves all services enabled in the GCP Project
        mql: gcp.project.services.where( enabled == true )



      - uid: mondoo-asset-inventory-gcp-gke-clusters-count
        title: GKE clusters count
        filters: asset.platform == "gcp-project"
        docs:
          desc: |
            This query retrieves a count of GKE clusters running in a GCP project
        mql: gcp.project.gke.clusters.length



      - uid: mondoo-asset-inventory-gcp-gke-clusters-data
        title: GKE clusters configuration
        docs:
          desc: |
            This query retrieves all of the configuration data for GKE clusters within a project
        variants:
          - uid: mondoo-asset-inventory-gcp-gke-clusters-data-all
          - uid: mondoo-asset-inventory-gcp-gke-clusters-data-single
      - uid: mondoo-asset-inventory-gcp-gke-clusters-data-all
        filters: asset.platform == "gcp-project"
        mql: |
          gcp.project.gke.clusters
      - uid: mondoo-asset-inventory-gcp-gke-clusters-data-single
        filters: asset.platform == "gcp-gke-cluster"
        mql: |
          gcp.project.gke.cluster


      - uid: mondoo-asset-inventory-gcp-compute-instances-count
        title: GCP compute instances count
        filters: asset.platform == "gcp-project"
        docs:
          desc: |
            This query retrieves a count of running GCP compute instances in a GCP project
        mql: gcp.compute.instances.where( status == "RUNNING" ).length



      - uid: mondoo-asset-inventory-gcp-compute-instances-data
        title: GCP compute instances
        docs:
          desc: |
            This query retrieves the data for all running GCP compute instances in a GCP project
        variants:
          - uid: mondoo-asset-inventory-gcp-compute-instances-data-all
          - uid: mondoo-asset-inventory-gcp-compute-instances-data-single
      - uid: mondoo-asset-inventory-gcp-compute-instances-data-all
        filters: asset.platform == "gcp-project"
        mql: |
          gcp.compute.instances.where( status == "RUNNING" )
      - uid: mondoo-asset-inventory-gcp-compute-instances-data-single
        filters: |
          asset.platform == "gcp-compute-instance"
          gcp.compute.instance.status == "RUNNING"
        mql: |
          gcp.compute.instance



      - uid: mondoo-asset-inventory-gcp-compute-instances-public
        title: GCP Compute Engine instances
        docs:
          desc: |
            This query retrieves the data for all GCP Compute Engine instances that have been configured with an external IP address.
        variants:
          - uid: mondoo-asset-inventory-gcp-compute-instances-public-all
          - uid: mondoo-asset-inventory-gcp-compute-instances-public-single
      - uid: mondoo-asset-inventory-gcp-compute-instances-public-all
        filters: asset.platform == "gcp-project"
        mql: |
          gcp.compute.instances.where(networkInterfaces.where(_['accessConfigs'].where(_['name'] == "External NAT")))
      - uid: mondoo-asset-inventory-gcp-compute-instances-public-single
        filters: |
          asset.platform == "gcp-compute-instance"
          gcp.compute.instance.networkInterfaces.any(_['accessConfigs'].where(_['name'] == "External NAT"))
        mql: |
          gcp.compute.instance



      - uid: mondoo-asset-inventory-gcp-compute-networks-count
        title: GCP Compute Engine networks count
        filters: asset.platform == "gcp-project"
        docs:
          desc: |
            This query retrieves a count of GCP Compute Engine networks configured in a GCP project
        mql: gcp.compute.networks.length



      - uid: mondoo-asset-inventory-gcp-compute-networks-data
        title: GCP Compute Engine networks
        docs:
          desc: |
            This query retrieves the data for all GCP Compute Engine networks configured in a GCP project.
        variants:
          - uid: mondoo-asset-inventory-gcp-compute-networks-data-all
          - uid: mondoo-asset-inventory-gcp-compute-networks-data-single
          - uid: mondoo-asset-inventory-gcp-compute-networks-data-subnet
      - uid: mondoo-asset-inventory-gcp-compute-networks-data-all
        filters: |
          asset.platform == "gcp-project"
        mql: |
          gcp.compute.networks
      - uid: mondoo-asset-inventory-gcp-compute-networks-data-single
        filters: |
          asset.platform == "gcp-compute-network"
        mql: |
          gcp.compute.network
      - uid: mondoo-asset-inventory-gcp-compute-networks-data-subnet
        filters: |
          asset.platform == "gcp-compute-subnetwork"
        mql: |
          gcp.compute.subnetwork
