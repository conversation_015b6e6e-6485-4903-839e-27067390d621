# Copyright (c) Mondoo, Inc.
# SPDX-License-Identifier: BUSL-1.1

packs:
  - uid: mondoo-ssl-tls-certificate-incident-response
    name: SSL/TLS Certificate Incident Response Pack
    version: 1.1.0
    license: BUSL-1.1
    authors:
      - name: <PERSON><PERSON><PERSON>, Inc
        email: <EMAIL>
    tags:
      mondoo.com/platform: host,network
      mondoo.com/category: security
    docs:
      desc: |
        ### Overview

        The SSL/TLS Certificate Incident Response Pack by Mondoo query pack retrieves information about SSL/TLS certificates of a domain for investigation during a security incident.

        ### Prerequisites

        To run this query pack, you will need to install the cnquery binary ([Get Started with cnquery](https://mondoo.com/docs/cnquery/)).

        ### Run query pack

        To run this query pack against a Domain:

        ```bash
        cnquery scan host <domain> -f mondoo-ssl-tls-certificate-incident-response.mql.yaml
        ```
    filters:
      - asset.family.contains('network')
    queries:
      - uid: mondoo-ssl-tls-certificate-incident-response-domain-name
        title: Domain Name
        mql: |
          tls.domainName
      - uid: mondoo-ssl-tls-certificate-incident-response-versions
        title: Supported SSL and TLS versions
        mql: |
          tls.versions
      - uid: mondoo-ssl-tls-certificate-incident-response-ciphers
        title: Supported SSl/TLS ciphers
        mql: |
          tls.ciphers
      - uid: mondoo-ssl-tls-certificate-incident-response-signing-algo
        title: Signature algorithm of all certificates in the certificate chain
        mql: |
          tls.certificates {
            signingAlgorithm
            subject.commonName
          }
      - uid: mondoo-ssl-tls-certificate-incident-response-is-revoked
        title: Revoked, verified, and CA status of all certificates in the certificate chain
        mql: |
          tls.certificates {
            subject.commonName
            isCA
            isRevoked
            isVerified
          }
      - uid: mondoo-ssl-tls-certificate-incident-response-when-expire
        title: Expiration dates for all certificates in the certificate chain
        mql: |
          tls.certificates {
            subject.commonName
            expiresIn
            notAfter
            notBefore
          }
