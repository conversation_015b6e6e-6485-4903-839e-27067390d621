# Copyright (c) Mondoo, Inc.
# SPDX-License-Identifier: BUSL-1.1

packs:
  - uid: mondoo-macos-incident-response
    name: macOS Incident Response Pack
    version: 1.1.0
    license: BUSL-1.1
    authors:
      - name: <PERSON><PERSON><PERSON>, Inc
        email: <EMAIL>
    tags:
      mondoo.com/platform: macos
      mondoo.com/category: security
    filters:
      - asset.platform == "macos"
    queries:
      - uid: mondoo-macos-incident-response-platform-info
        title: Platform information
        mql: asset { platform title version arch }
      - uid: mondoo-macos-incident-response-regular-users
        title: Regular users
        mql: users.where( name != /^_/ && shell != /\/usr\/bin\/false/ )
      - uid: mondoo-macos-incident-response-kernel-info
        title: Running macOS kernel
        mql: kernel.info["version"]
      - uid: mondoo-macos-incident-response-kernel-modules
        title: macOS kernel modules
        mql: kernel.modules { name loaded }
      - uid: mondoo-macos-incident-response-processes
        title: Running processes
        mql: processes.list { pid  command }
      - uid: mondoo-macos-incident-response-mounts
        title: Mounted devices
        mql: mount.list
      - uid: mondoo-macos-incident-response-uptime
        title: Operating system uptime
        mql: os.uptime
      - uid: mondoo-macos-incident-response-installed-packages
        title: Installed packages
        mql: packages
      - uid: mondoo-macos-incident-response-running-services
        title: Running services
        mql: services.where(running == true) { name running enabled masked type }
      - uid: mondoo-macos-incident-response-alf-extensions
        title: Exceptions from the Application Layer Firewall
        mql: macos.alf.exceptions
      - uid: mondoo-macos-incident-response-check-recommended-updates
        title: Recommended OS and application updates
        mql: parse.plist('/Library/Preferences/com.apple.SoftwareUpdate.plist').params['RecommendedUpdates']
