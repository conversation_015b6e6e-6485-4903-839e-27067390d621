# Copyright (c) Mondoo, Inc.
# SPDX-License-Identifier: BUSL-1.1

packs:
  - uid: mondoo-okta-incident-response
    name: Okta Incident Response Pack
    version: 1.1.0
    license: BUSL-1.1
    authors:
      - name: <PERSON><PERSON><PERSON>, <PERSON>
        email: <EMAIL>
    tags:
      mondoo.com/platform: okta-org,saas
      mondoo.com/category: security
    docs:
      desc: |
        ### Overview

        During a security incident, the Okta Incident Response query pack retrieves configuration data about your Okta configuration.

        ### Prerequisites

        To run this query pack, you will need access to the Okta API:

        1. Create an Okta [API token](https://developer.okta.com/docs/guides/create-an-api-token/create-the-token/) by going to https:/DOMAIN.okta.com/admin/access/api/tokens
        2. Note your Okta domain https://DOMAIN.okta.com

        ### Run query pack

        To run this query pack against an Okta domain:

        ```bash
        export OKTA_TOKEN=<TOKEN>
        cnquery shell okta --organization DOMAIN.okta.com --token $OKTA_TOKEN 
        ```
    filters:
      - asset.platform == "okta" || asset.platform == "okta-org"
    queries:
      - uid: mondoo-okta-incident-response-users
        title: Users
        mql: okta.users
      - uid: mondoo-okta-incident-response-team-id
        title: Installed applications
        mql: okta.applications
