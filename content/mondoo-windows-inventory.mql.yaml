# Copyright (c) Mondoo, Inc.
# SPDX-License-Identifier: BUSL-1.1

packs:
  - uid: mondoo-windows-asset-inventory
    name: Windows Asset Inventory Pack
    version: 1.6.1
    license: BUSL-1.1
    authors:
      - name: <PERSON><PERSON><PERSON>, Inc
        email: <EMAIL>
    tags:
      mondoo.com/platform: windows
      mondoo.com/category: best-practices
    docs:
      desc: |
        The Windows Asset Inventory Pack by Mondoo retrieves data about Windows hosts for asset inventory.

        ## Local scan
        To run this pack locally on a Windows host:

        ```bash
        cnquery scan local -f mondoo-windows-inventory.mql.yaml
        ```

        ## Remote scan
        To run this pack against a remote macOS host using SSH (requires Remote Management is activated in System Preferences):

        ```bash
        cnquery scan winrm <user>@<ip_address> -f mondoo-windows-inventory.mql.yaml
        ```

        ## Join the community!
        Our goal is to build query packs that are simple to deploy and provide accurate and useful data.

        If you have any suggestions for improving this query pack, or if you need support, [join the Mondoo community](https://github.com/orgs/mondoohq/discussions) in GitHub Discussions.
    filters:
      - asset.platform == "windows"
    queries:
      - uid: mondoo-windows-asset-info
        title: Asset information
        mql: asset { kind title platform name arch runtime version }
      - uid: mondoo-windows-hostname
        title: Hostname
        mql: os.hostname
      - uid: mondoo-windows-uptime
        title: Operating system uptime
        filters: mondoo.capabilities.contains("run-command")
        mql: os.uptime
      - uid: mondoo-windows-processes
        title: Running processes
        filters: mondoo.capabilities.contains("run-command")
        mql: processes { pid executable }
      - uid: mondoo-windows-users
        title: Regular users
        mql: users
      - uid: mondoo-windows-packages
        title: Installed packages
        mql: packages { name version arch installed }
      - uid: mondoo-windows-hotfixes
        title: All installed Windows hotfixes
        mql: windows.hotfixes { hotfixId installedOn }
      - uid: mondoo-windows-features
        title: Installed Windows features
        mql: windows.features.where(installed == true) { path name displayName }
      - uid: mondoo-windows-running-services
        title: Running services
        filters: mondoo.capabilities.contains("run-command")
        mql: services.where(running == true) { name running enabled masked type }
      - uid: mondoo-windows-ports-listening
        title: Listening ports
        filters: mondoo.capabilities.contains("run-command")
        mql: ports.listening { user state port address protocol process remoteAddress remotePort }
      - uid: mondoo-windows-active-connections
        title: Active connections of the system
        filters: mondoo.capabilities.contains("run-command")
        mql: ports.where(state != "close") { user state port address protocol process remoteAddress remotePort }
      - uid: mondoo-windows-interface-configuration
        title: Network interfaces
        mql: windows.computerInfo['CsNetworkAdapters']
      - uid: mondoo-windows-computer-info
        title: Windows Computer/ System information
        mql: windows.computerInfo
      - uid: mondoo-windows-security-products
        title: Installed Security Products
        filters: |
          windows.computerInfo['OsProductType'] == 1
        mql: windows.security.products { guid state type name productState signatureState timestamp }
      - uid: mondoo-windows-bitlocker-volumes
        title: BitLocker Volumes
        filters: |
          windows.computerInfo['OsProductType'] == 1
        mql: windows.bitlocker.volumes { deviceID driveLetter encryptionMethod version persistentVolumeID protectionStatus lockStatus conversionStatus }
      - uid: mondoo-windows-security-center-health
        title: Windows Security Health Information
        filters: |
          windows.computerInfo['OsProductType'] == 1
        mql: windows.security.health { autoUpdate internetSettings securityCenterService firewall uac antiVirus antiSpyware }
      - uid: mondoo-windows-windows-firewall-settings
        title: Windows Firewall settings
        mql: windows.firewall { settings profiles { allowUnicastResponseToMulticast logIgnored enabled allowLocalFirewallRules allowLocalIPsecRules logAllowed logBlocked allowUserApps instanceID allowUserPorts name notifyOnListen logFileName enableStealthModeForIPsec defaultInboundAction logMaxSizeKilobytes defaultOutboundAction allowInboundRules } }
      - uid: mondoo-windows-windows-firewall-rules
        title: Windows Firewall rules
        mql: windows.firewall.rules { edgeTraversalPolicy status instanceID enabled looseSourceMapping displayGroup policyStoreSource name enforcementStatus description direction displayName policyStoreSourceType primaryStatus localOnlyMapping action }
      - uid: mondoo-windows-windows-audit-policies
        title: Windows audit policies
        mql: auditpol { exclusionsetting machinename policytarget subcategory inclusionsetting subcategoryguid }
      - uid: mondoo-windows-windows-system-access-policy
        title: Windows local System Access security policy
        mql: secpol.systemaccess
      - uid: mondoo-windows-windows-event-audit-policy
        title: Windows local Event Audit security policy
        mql: secpol.eventaudit
      - uid: mondoo-windows-registry-values-policy
        title: Windows local Registry Values security policy
        mql: secpol.registryvalues
      - uid: mondoo-windows-privilege-rights-policy
        title: Windows local Privilege Rights security policy
        mql: secpol.privilegerights
      - uid: mondoo-windows-smbios-baseboard
        title: SMBIOS baseboard (or module) information
        mql: machine.baseboard { manufacturer version serial assetTag product }
      - uid: mondoo-windows-smbios-bios
        title: SMBIOS BIOS information
        mql: machine.bios { vendor version releaseDate }
      - uid: mondoo-windows-smbios-system
        title: SMBIOS System information
        mql: machine.system { sku serial family version product uuid manufacturer }
      - uid: mondoo-windows-smbios-chassis
        title: SMBIOS Chassis information
        mql: machine.chassis { manufacturer serial version assetTag }
      - uid: mondoo-windows-scheduled-tasks
        title: Scheduled tasks
        mql: |
          parse.json(content: powershell("Get-ScheduledTask | ConvertTo-Json").stdout).params
      - uid: mondoo-windows-logged-in-users
        title: Logged-in users
        mql: |
          parse.json(content: powershell("Get-Process -IncludeUserName explorer | Select-Object Username | ConvertTo-Json").stdout).params
      - uid: mondoo-windows-exchange-server-version
        title: Exchange Server Version
        filters: |
          package('Microsoft Exchange Server').installed
        mql: |
         powershell('(Get-Command ExSetup.exe | ForEach-Object { $_.FileVersionInfo } | Select-Object -First 1).FileVersion').stdout
