# Copyright (c) Mondoo, Inc.
# SPDX-License-Identifier: BUSL-1.1

packs:
  - uid: mondoo-asset-inventory-terraform
    name: Terraform Asset Inventory Pack
    version: 1.0.0
    license: BUSL-1.1
    authors:
      - name: <PERSON><PERSON><PERSON>, Inc
        email: <EMAIL>
    tags:
      mondoo.com/platform: terraform
      mondoo.com/category: inventory
    docs:
      desc: |
        The Terraform Asset Inventory Pack retrieves information about Terraform HCL, Terraform Plan, and Terraform State for asset inventory.
    groups:
      - title: Terraform State Asset inventory
        filters: asset.platform == "terraform-state"
        queries:
          - uid: mondoo-asset-inventory-terraform-state-version
          - uid: mondoo-asset-inventory-terraform-state-resources
queries:
  - uid: mondoo-asset-inventory-terraform-state-version
    title: Terraform State Terraform Version
    docs:
      desc: |
        This query gathers the version of Terraform that was used to execute a Terraform run.
    mql: terraform.state.terraformVersion
  - uid: mondoo-asset-inventory-terraform-state-resources
    title: Terraform State resources
    docs:
      desc: |
        This query gathers the resources stored in Terraform state files to provide an inventory of infrastructure managed by Terraform.
    variants:
      - uid: mondoo-asset-inventory-terraform-state-aws-resources
      - uid: mondoo-asset-inventory-terraform-state-gcp-resources
      - uid: mondoo-asset-inventory-terraform-state-azure-resources
  - uid: mondoo-asset-inventory-terraform-state-aws-resources
    filters: asset.platform == "terraform-state" && terraform.state.resources.any( type == /^aws_/ )
    docs:
      desc: |
        This query gathers the resources stored in Terraform state files that manage any AWS resources. The data is only gather if any of the resources match 'aws_' such as 'aws_s3_bucket'.
    mql: terraform.state.resources { type providerName values['arn']  values['owner_id'] }
  - uid: mondoo-asset-inventory-terraform-state-gcp-resources
    filters: asset.platform == "terraform-state" && terraform.state.resources.any( type == /^google_/ )
    docs:
      desc: |
        This query gathers the resources stored in Terraform state files that manage any Google Cloud resources. The data is only gather if any of the resources match 'google_' such as 'google_compute_instance'.
    mql: terraform.state.resources { type providerName values['project'] values['id'] }
  - uid: mondoo-asset-inventory-terraform-state-azure-resources
    filters: asset.platform == "terraform-state" && terraform.state.resources.any( type == /^azurerm_/ )
    docs:
      desc: |
        This query gathers the resources stored in Terraform state files that manage any Microsoft Azure resources. The data is only gather if any of the resources match 'azurerm_' such as 'azurerm_resource_group'.
    mql: terraform.state.resources { type providerName values['id'] }