# Copyright (c) Mondoo, Inc.
# SPDX-License-Identifier: BUSL-1.1

packs:
  - uid: mondoo-windows-operational-inventory
    name: Windows Client/Server Operational Inventory Pack
    version: 1.0.0
    license: BUSL-1.1
    authors:
      - name: <PERSON><PERSON><PERSON>, Inc
        email: <EMAIL>
    tags:
      mondoo.com/platform: windows
      mondoo.com/category: best-practices
    docs:
      desc: |-
        The Windows Operational Inventory Pack by Mondoo retrieves monitoring data about Windows hosts.

        ## Local scan
        To run this pack locally on a Windows host:

        ```bash
        cnquery scan local -f mondoo-windows-operational-inventory.mql.yaml
        ```

        ## Remote execution
        To run this pack against a remote Windows host using SSH:

        ```bash
        cnquery scan ssh <user>@<ip_address> -f mondoo-windows-operational-inventory.mql.yaml
        ```

        ## Join the community!
        Our goal is to build query packs that are simple to deploy and provide accurate and useful data.

        If you have any suggestions for improving this query pack, or if you need support, [join the Mondoo community](https://github.com/orgs/mondoohq/discussions) in GitHub Discussions.
    filters:
      - mql: asset.family.contains("windows")
    queries:
      - uid: mondoo-windows-operational-inventory-memory-usage
        title: Memory usage in %
        mql: |
          parse.json(content: powershell("Get-Counter '\\Memory\\% Committed Bytes In Use' | ConvertTo-Json -Compress").stdout).params['CounterSamples'] { Path CookedValue }
      - uid: mondoo-windows-operational-inventory-cpu-usage
        title: CPU usage in %
        mql: |
          parse.json(content: powershell("Get-Counter '\\Processor(*)\\% Processor Time' | ConvertTo-Json -Compress").stdout).params['CounterSamples'] { Path CookedValue }
      - uid: mondoo-windows-operational-inventory-disk-usage
        title: Disk usage in %
        mql: |
          parse.json(content: powershell("Get-Counter '\\LogicalDisk(*)\\% Free Space' | ConvertTo-Json -Compress").stdout).params['CounterSamples'] { Path CookedValue }
    