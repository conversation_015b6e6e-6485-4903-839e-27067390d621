# Copyright (c) Mon<PERSON>o, Inc.
# SPDX-License-Identifier: BUSL-1.1

packs:
  - uid: mondoo-asset-inventory-azure
    name: Azure Asset Inventory Pack
    version: 1.2.0
    license: BUSL-1.1
    authors:
      - name: <PERSON><PERSON><PERSON>, Inc
        email: <EMAIL>
    tags:
      mondoo.com/platform: azure,cloud
      mondoo.com/category: best-practices
    docs:
      desc: |
        The Azure Asset Inventory by Mondoo query pack retrieves information about Azure subscriptions and resources for asset inventory.
    groups:
      - uid: mondoo-incident-response-aws-group
        title: AWS Asset Inventory Pack Group
        filters: asset.runtime == "azure"
        queries:
          - uid: mondoo-asset-inventory-azure-roleDefinitions
          - uid: mondoo-asset-inventory-azure-cloudDefender
          - uid: mondoo-asset-inventory-azure-storageAccounts
          - uid: mondoo-asset-inventory-azure-storageAccounts-containers
          - uid: mondoo-asset-inventory-azure-storageAccounts-blobs
          - uid: mondoo-asset-inventory-azure-storageAccounts-tables
          - uid: mondoo-asset-inventory-azure-sqlServers
          - uid: mondoo-asset-inventory-azure-sqlServers-firewallrules
          - uid: mondoo-asset-inventory-azure-sqlServers-databases
          - uid: mondoo-asset-inventory-azure-postgresql
          - uid: mondoo-asset-inventory-azure-postgresql-firewallrules
          - uid: mondoo-asset-inventory-azure-mysql
          - uid: mondoo-asset-inventory-azure-mysql-firewallrules
          - uid: mondoo-asset-inventory-azure-mariaDb
          - uid: mondoo-asset-inventory-azure-keyVaults
          - uid: mondoo-asset-inventory-azure-keyVaults-keys
          - uid: mondoo-asset-inventory-azure-keyVaults-secrets
          - uid: mondoo-asset-inventory-azure-keyVaults-certificates
          - uid: mondoo-asset-inventory-azure-activitylogs
          - uid: mondoo-asset-inventory-azure-networkSecurityGroups
          - uid: mondoo-asset-inventory-azure-publicip
          - uid: mondoo-asset-inventory-azure-virtualmachines
          - uid: mondoo-asset-inventory-azure-virtualmachines-managedDisk
          - uid: mondoo-asset-inventory-azure-webapp
          - uid: mondoo-asset-inventory-azure-cosmosDb
          - uid: mondoo-asset-inventory-azure-applicationInsight
          - uid: mondoo-asset-inventory-azure-networkWatcher
          - uid: mondoo-asset-inventory-azure-bastionHosts
          - uid: mondoo-asset-inventory-azure-compute-disks
          - uid: mondoo-asset-inventory-azure-network-interfaces
          - uid: mondoo-asset-inventory-azure-resourcegroups
          - uid: mondoo-asset-inventory-azure-resources
queries:
  - uid: mondoo-asset-inventory-azure-roleDefinitions
    title: Azure role definitions
    filters: asset.platform == "azure"
    docs:
      desc: |
        This query retrieves data for all role definitions in the subscription
    mql: azure.subscription.authorization.roleDefinitions


  - uid: mondoo-asset-inventory-azure-cloudDefender
    title: Microsoft Defender for Cloud configuration
    filters: asset.platform == "azure"
    docs:
      desc: |
        This query retrieves data for Microsoft Defender for Cloud
    mql: azure.subscription.cloudDefender { defenderForServers defenderForContainers securityContacts { name alertNotifications }  }


  - uid: mondoo-asset-inventory-azure-storageAccounts
    title: Azure Storage accounts
    docs:
      desc: |
        This query retrieves data for all storage accounts
    variants:
      - uid: mondoo-asset-inventory-azure-storageAccounts-single
      - uid: mondoo-asset-inventory-azure-storageAccounts-api
  - uid: mondoo-asset-inventory-azure-storageAccounts-single
    filters: asset.platform == "azure-storage-account"
    mql: azure.subscription.storage.account
  - uid: mondoo-asset-inventory-azure-storageAccounts-api
    filters: asset.platform == "azure"
    mql: azure.subscription.storage.accounts


  - uid: mondoo-asset-inventory-azure-storageAccounts-containers
    title: Azure Storage account containers
    docs:
      desc: |
        This query retrieves data for all containers in storage accounts
    variants:
      - uid: mondoo-asset-inventory-azure-storageAccounts-containers-single
      - uid: mondoo-asset-inventory-azure-storageAccounts-containers-api
  - uid: mondoo-asset-inventory-azure-storageAccounts-containers-api
    filters: asset.platform == "azure"
    mql: azure.subscription.storage.accounts { containers }
  - uid: mondoo-asset-inventory-azure-storageAccounts-containers-single
    filters: asset.platform == "azure-storage-account" && azure.subscription.storage.account.containers != empty
    mql: azure.subscription.storage.account.containers


  - uid: mondoo-asset-inventory-azure-storageAccounts-blobs
    title: Azure storage accounts blobs
    docs:
      desc: |
        This query retrieves data for all blobs in storage accounts
    variants:
      - uid: mondoo-asset-inventory-azure-storageAccounts-blobs-single
      - uid: mondoo-asset-inventory-azure-storageAccounts-blobs-api
  - uid: mondoo-asset-inventory-azure-storageAccounts-blobs-api
    filters: asset.platform == "azure"
    mql: azure.subscription.storage.accounts { blobProperties }
  - uid: mondoo-asset-inventory-azure-storageAccounts-blobs-single
    filters: asset.platform == "azure-storage-account"
    mql: azure.subscription.storage.account.blobProperties


  - uid: mondoo-asset-inventory-azure-storageAccounts-tables
    title: Azure Storage accounts tables
    docs:
      desc: |
        This query retrieves data for all tables in storage accounts
    variants:
      - uid: mondoo-asset-inventory-azure-storageAccounts-tables-single
      - uid: mondoo-asset-inventory-azure-storageAccounts-tables-api
  - uid: mondoo-asset-inventory-azure-storageAccounts-tables-api
    filters: asset.platform == "azure"
    mql: azure.subscription.storage.accounts { tableProperties }
  - uid: mondoo-asset-inventory-azure-storageAccounts-tables-single
    filters: asset.platform == "azure-storage-account"
    mql: azure.subscription.storage.account.tableProperties


  - uid: mondoo-asset-inventory-azure-sqlServers
    title: Azure SQL Database servers
    docs:
      desc: |
        This query retrieves data for all Azure SQL Database servers
    variants:
      - uid: mondoo-asset-inventory-azure-sqlServers-single
      - uid: mondoo-asset-inventory-azure-sqlServers-api
  - uid: mondoo-asset-inventory-azure-sqlServers-api
    filters: asset.platform == "azure"
    mql: azure.subscription.sql.servers
  - uid: mondoo-asset-inventory-azure-sqlServers-single
    filters: asset.platform == "azure-sql-server"
    mql: azure.subscription.sql.server


  - uid: mondoo-asset-inventory-azure-sqlServers-firewallrules
    title: Azure SQL Database server firewall rules
    docs:
      desc: |
        This query retrieves data for all firewall rules in Azure SQL Database servers
    variants:
      - uid: mondoo-asset-inventory-azure-sqlServers-firewallrules-single
      - uid: mondoo-asset-inventory-azure-sqlServers-firewallrules-api
  - uid: mondoo-asset-inventory-azure-sqlServers-firewallrules-api
    filters: asset.platform == "azure"
    mql: azure.subscription.sql.servers { firewallRules }
  - uid: mondoo-asset-inventory-azure-sqlServers-firewallrules-single
    filters: asset.platform == "azure-sql-server"
    mql: azure.subscription.sql.server.firewallRules


  - uid: mondoo-asset-inventory-azure-sqlServers-databases
    title: Azure SQL Database server databases
    docs:
      desc: |
        This query retrieves data for all databases in Azure SQL Database servers
    variants:
      - uid: mondoo-asset-inventory-azure-sqlServers-databases-single
      - uid: mondoo-asset-inventory-azure-sqlServers-databases-api
  - uid: mondoo-asset-inventory-azure-sqlServers-databases-api
    filters: asset.platform == "azure"
    mql: azure.subscription.sql.servers { databases }
  - uid: mondoo-asset-inventory-azure-sqlServers-databases-single
    filters: asset.platform == "azure-sql-server"
    mql: azure.subscription.sql.server.databases


  - uid: mondoo-asset-inventory-azure-postgresql
    title: Azure PostgreSQL servers
    docs:
      desc: |
        This query retrieves data for all PostgreSQL servers
    variants:
      - uid: mondoo-asset-inventory-azure-postgresql-all
      - uid: mondoo-asset-inventory-azure-postgresql-legacy
      - uid: mondoo-asset-inventory-azure-postgresql-flexible
  - uid: mondoo-asset-inventory-azure-postgresql-all
    filters: asset.platform == "azure"
    mql: |
      azure.subscription.postgreSql.servers
      azure.subscription.postgreSql.flexibleServers
  - uid: mondoo-asset-inventory-azure-postgresql-legacy
    filters: asset.platform == "azure-postgresql-server"
    mql: azure.subscription.postgreSql.server
  - uid: mondoo-asset-inventory-azure-postgresql-flexible
    filters: asset.platform == "azure-postgresql-flexible-server"
    mql: azure.subscription.postgreSql.flexibleServer


  - uid: mondoo-asset-inventory-azure-postgresql-firewallrules
    title: Azure PostgreSQL server firewall rules
    docs:
      desc: |
        This query retrieves data for all firewall rules in Azure PostgreSQL servers
    variants:
      - uid: mondoo-asset-inventory-azure-postgresql-firewallrules-all
      - uid: mondoo-asset-inventory-azure-postgresql-firewallrules-legacy
      - uid: mondoo-asset-inventory-azure-postgresql-firewallrules-flexible
  - uid: mondoo-asset-inventory-azure-postgresql-firewallrules-all
    filters: asset.platform == "azure"
    mql: |
      azure.subscription.postgreSql.servers { firewallRules }
      azure.subscription.postgreSql.flexibleServers { firewallRules }
  - uid: mondoo-asset-inventory-azure-postgresql-firewallrules-legacy
    filters: asset.platform == "azure-postgresql-server"
    mql: azure.subscription.postgreSql.server.firewallRules
  - uid: mondoo-asset-inventory-azure-postgresql-firewallrules-flexible
    filters: asset.platform == "azure-postgresql-flexible-server"
    mql: azure.subscription.postgreSql.flexibleServer.firewallRules


  - uid: mondoo-asset-inventory-azure-mysql-firewallrules
    title: Azure MySQL servers
    docs:
      desc: |
        This query retrieves data for all Azure MySQL servers
    variants:
      - uid: mondoo-asset-inventory-azure-mysql-firewallrules-all
      - uid: mondoo-asset-inventory-azure-mysql-firewallrules-legacy
      - uid: mondoo-asset-inventory-azure-mysql-firewallrules-flexible
  - uid: mondoo-asset-inventory-azure-mysql-firewallrules-all
    filters: asset.platform == "azure"
    mql: |
      azure.subscription.mySql.servers { firewallRules }
      azure.subscription.mySql.flexibleServers { firewallRules }
  - uid: mondoo-asset-inventory-azure-mysql-firewallrules-legacy
    filters: asset.platform == "azure-mysql-server"
    mql: azure.subscription.mySql.server.firewallRules
  - uid: mondoo-asset-inventory-azure-mysql-firewallrules-flexible
    filters: asset.platform == "azure-mysql-flexible-server"
    mql: azure.subscription.mySql.flexibleServer.firewallRules


  - uid: mondoo-asset-inventory-azure-mysql
    title: Azure MySQL servers
    docs:
      desc: |
        This query retrieves data for all Azure MySQL servers
    variants:
      - uid: mondoo-asset-inventory-azure-mysql-all
      - uid: mondoo-asset-inventory-azure-mysql-legacy
      - uid: mondoo-asset-inventory-azure-mysql-flexible
  - uid: mondoo-asset-inventory-azure-mysql-all
    filters: asset.platform == "azure"
    mql: |
      azure.subscription.mySql.servers
      azure.subscription.mySql.flexibleServers
  - uid: mondoo-asset-inventory-azure-mysql-legacy
    filters: asset.platform == "azure-mysql-server"
    mql: azure.subscription.mySql.server
  - uid: mondoo-asset-inventory-azure-mysql-flexible
    filters: asset.platform == "azure-mysql-flexible-server"
    mql: azure.subscription.mySql.flexibleServer


  - uid: mondoo-asset-inventory-azure-mariaDb
    title: Azure MariaDB servers
    docs:
      desc: |
        This query retrieves data for all Azure MariaDB servers
    variants:
      - uid: mondoo-asset-inventory-azure-mariaDb-single
      - uid: mondoo-asset-inventory-azure-mariaDb-api
  - uid: mondoo-asset-inventory-azure-mariaDb-api
    filters: asset.platform == "azure"
    mql: azure.subscription.mariaDb.servers
  - uid: mondoo-asset-inventory-azure-mariaDb-single
    filters: asset.platform == "azure-mariadb-server"
    mql: azure.subscription.mariaDb.server


  - uid: mondoo-asset-inventory-azure-diagnosticSettings
    title: Azure diagnostic settings
    filters: asset.platform == "azure"
    docs:
      desc: |
        This query retrieves data for all diagnostic settings
    mql: azure.subscription.monitor.diagnosticSettings


  - uid: mondoo-asset-inventory-azure-keyVaults
    title: Azure Key Vault vaults
    docs:
      desc: |
        This query retrieves data for all Azure Key Vault vaults
    variants:
      - uid: mondoo-asset-inventory-azure-keyVaults-single
      - uid: mondoo-asset-inventory-azure-keyVaults-api
  - uid: mondoo-asset-inventory-azure-keyVaults-api
    filters: asset.platform == "azure"
    mql: azure.subscription.keyVault.vaults
  - uid: mondoo-asset-inventory-azure-keyVaults-single
    filters: asset.platform == "azure-keyvault-vault"
    mql: azure.subscription.keyVault.vault


  - uid: mondoo-asset-inventory-azure-keyVaults-keys
    title: Azure Key Vault vault keys
    docs:
      desc: |
        This query retrieves data for all keys in Key Vaults
    variants:
      - uid: mondoo-asset-inventory-azure-keyVaults-keys-api
      - uid: mondoo-asset-inventory-azure-keyVaults-keys-single
  - uid: mondoo-asset-inventory-azure-keyVaults-keys-api
    filters: asset.platform == "azure"
    mql: azure.subscription.keyVault.vaults { keys }
  - uid: mondoo-asset-inventory-azure-keyVaults-keys-single
    filters: asset.platform == "azure-keyvault-vault"
    mql: azure.subscription.keyVault.vault.keys


  - uid: mondoo-asset-inventory-azure-keyVaults-secrets
    title: Azure Key Vault vault secrets
    docs:
      desc: |
        This query retrieves data for all secrets in Key Vaults
    variants:
      - uid: mondoo-asset-inventory-azure-keyVaults-secrets-api
      - uid: mondoo-asset-inventory-azure-keyVaults-secrets-single
  - uid: mondoo-asset-inventory-azure-keyVaults-secrets-api
    filters: asset.platform == "azure"
    mql: azure.subscription.keyVault.vaults { secrets }
  - uid: mondoo-asset-inventory-azure-keyVaults-secrets-single
    filters: asset.platform == "azure-keyvault-vault"
    mql: azure.subscription.keyVault.vault.secrets


  - uid: mondoo-asset-inventory-azure-keyVaults-certificates
    title: Azure Key Vault vault certificates
    docs:
      desc: |
        This query retrieves data for all certificates in Key Vaults
    variants:
      - uid: mondoo-asset-inventory-azure-keyVaults-certificates-api
      - uid: mondoo-asset-inventory-azure-keyVaults-certificates-single
  - uid: mondoo-asset-inventory-azure-keyVaults-certificates-api
    filters: asset.platform == "azure"
    mql: azure.subscription.keyVault.vaults { certificates }
  - uid: mondoo-asset-inventory-azure-keyVaults-certificates-single
    filters: asset.platform == "azure-keyvault-vault"
    mql: azure.subscription.keyVault.vault.certificates


  - uid: mondoo-asset-inventory-azure-activitylogs
    title: Azure activity logs
    filters: asset.platform == "azure"
    docs:
      desc: |
        This query retrieves data for all activity logs
    mql: azure.subscription.monitor.activityLog


  - uid: mondoo-asset-inventory-azure-networkSecurityGroups
    title: Azure network security groups
    docs:
      desc: |
        This query retrieves data for all network security groups
    variants:
      - uid: mondoo-asset-inventory-azure-networkSecurityGroups-api
      - uid: mondoo-asset-inventory-azure-networkSecurityGroups-single
  - uid: mondoo-asset-inventory-azure-networkSecurityGroups-api
    filters: asset.platform == "azure"
    mql: azure.subscription.network.securityGroups
  - uid: mondoo-asset-inventory-azure-networkSecurityGroups-single
    filters: asset.platform == "azure-network-security-group"
    mql: azure.subscription.network.securityGroup


  - uid: mondoo-asset-inventory-azure-publicip
    title: Azure public IP addresses
    filters: asset.platform == "azure"
    docs:
      desc: |
        This query retrieves all public IP addresses in your subscription
    mql: azure.subscription.networkService.publicIpAddresses{ name location ipAddress }


  - uid: mondoo-asset-inventory-azure-virtualmachines
    title: Azure virtual machines
    docs:
      desc: |
        This query retrieves data for all virtual machines
    variants:
      - uid: mondoo-asset-inventory-azure-virtualmachines-api
      - uid: mondoo-asset-inventory-azure-virtualmachines-single
  - uid: mondoo-asset-inventory-azure-virtualmachines-api
    filters: asset.platform == "azure"
    mql: azure.subscription.compute.vms
  - uid: mondoo-asset-inventory-azure-virtualmachines-single
    filters: asset.platform == "azure-compute-vm-api"
    mql: azure.subscription.compute.vm


  - uid: mondoo-asset-inventory-azure-virtualmachines-managedDisk
    title: Azure virtual machines with managed disks
    docs:
      desc: |
        This query retrieves data for all virtual machines with managed disks
    variants:
      - uid: mondoo-asset-inventory-azure-virtualmachines-managedDisk-api
      - uid: mondoo-asset-inventory-azure-virtualmachines-managedDisk-single
  - uid: mondoo-asset-inventory-azure-virtualmachines-managedDisk-api
    filters: asset.platform == "azure"
    mql: azure.subscription.compute.vms.where( properties["storageProfile"]["osDisk"]["managedDisk"] != empty )
  - uid: mondoo-asset-inventory-azure-virtualmachines-managedDisk-single
    filters: asset.platform == "azure-compute-vm-api" && azure.subscription.compute.vm.properties["storageProfile"]["osDisk"]["managedDisk"] != empty
    mql: azure.subscription.compute.vm.properties["storageProfile"]["osDisk"]["managedDisk"] != empty


  - uid: mondoo-asset-inventory-azure-webapp
    title: Azure web apps
    filters: asset.platform == "azure"
    docs:
      desc: |
        This query retrieves data for all web apps
    mql: azure.subscription.web.apps


  - uid: mondoo-asset-inventory-azure-cosmosDb
    title: Azure Cosmos DB accounts
    filters: asset.platform == "azure"
    docs:
      desc: |
        This query retrieves data for all Cosmos DB accounts
    mql: azure.subscription.cosmosDb.accounts


  - uid: mondoo-asset-inventory-azure-applicationInsight
    title: Azure Monitor Application Insights
    filters: asset.platform == "azure"
    docs:
      desc: |
        This query retrieves data for all Application Insights
    mql: azure.subscription.monitor.applicationInsights


  - uid: mondoo-asset-inventory-azure-networkWatcher
    title: Azure Network Watchers
    filters: asset.platform == "azure"
    docs:
      desc: |
        This query retrieves data for Azure Network Watchers
    mql: azure.subscription.network.watchers


  - uid: mondoo-asset-inventory-azure-bastionHosts
    title: Azure Bastion hosts
    filters: asset.platform == "azure"
    docs:
      desc: |
        This query retrieves data for all Bastion hosts
    mql: azure.subscription.network.bastionHosts


  - uid: mondoo-asset-inventory-azure-compute-disks
    title: Compute disks
    filters: asset.platform == "azure"
    docs:
      desc: |
        This query retrieves data for all compute disks available in the subscription
    mql: azure.subscription.compute.disks


  - uid: mondoo-asset-inventory-azure-network-interfaces
    title: Network interfaces
    filters: asset.platform == "azure"
    docs:
      desc: |
        This query retrieves data for all network interfaces
    mql: azure.subscription.network.interfaces{ name location properties['nicType'] properties['nicType'] properties['macAddress'] properties['virtualMachine']['id'] }


  - uid: mondoo-asset-inventory-azure-resourcegroups
    title: Azure subscription resource groups
    filters: asset.platform == "azure"
    docs:
      desc: |
        This query retrieves data for all resource groups inside the subscription
    mql: azure.subscription.resourceGroups


  - uid: mondoo-asset-inventory-azure-resources
    title: Azure subscription resources
    filters: asset.platform == "azure"
    docs:
      desc: |
        This query retrieves data for all resources inside the subscription
    mql: azure.subscription.resources
