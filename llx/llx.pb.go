// Copyright (c) Mondoo, Inc.
// SPDX-License-Identifier: BUSL-1.1

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: llx.proto

package llx

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// protolint:disable:next ENUM_FIELD_NAMES_PREFIX
type Chunk_Call int32

const (
	// protolint:disable:next ENUM_FIELD_NAMES_ZERO_VALUE_END_WITH
	Chunk_PRIMITIVE Chunk_Call = 0
	Chunk_FUNCTION  Chunk_Call = 1
	Chunk_PROPERTY  Chunk_Call = 2
)

// Enum value maps for Chunk_Call.
var (
	Chunk_Call_name = map[int32]string{
		0: "PRIMITIVE",
		1: "FUNCTION",
		2: "PROPERTY",
	}
	Chunk_Call_value = map[string]int32{
		"PRIMITIVE": 0,
		"FUNCTION":  1,
		"PROPERTY":  2,
	}
)

func (x Chunk_Call) Enum() *Chunk_Call {
	p := new(Chunk_Call)
	*p = x
	return p
}

func (x Chunk_Call) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Chunk_Call) Descriptor() protoreflect.EnumDescriptor {
	return file_llx_proto_enumTypes[0].Descriptor()
}

func (Chunk_Call) Type() protoreflect.EnumType {
	return &file_llx_proto_enumTypes[0]
}

func (x Chunk_Call) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Chunk_Call.Descriptor instead.
func (Chunk_Call) EnumDescriptor() ([]byte, []int) {
	return file_llx_proto_rawDescGZIP(), []int{2, 0}
}

type Primitive struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	Type  string                 `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty"`
	// The value of the primitive in case of bool|int|float|string|ref|json
	// For array/map it holds the exact datatype as a string,
	// e.g. []int or map[string]string
	Value []byte `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
	// In case of an array primitive, holds the data of the array
	// protolint:disable REPEATED_FIELD_NAMES_PLURALIZED
	Array []*Primitive `protobuf:"bytes,3,rep,name=array,proto3" json:"array,omitempty"`
	// In case of a map primitive, holds the data of the map
	Map           map[string]*Primitive `protobuf:"bytes,4,rep,name=map,proto3" json:"map,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Primitive) Reset() {
	*x = Primitive{}
	mi := &file_llx_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Primitive) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Primitive) ProtoMessage() {}

func (x *Primitive) ProtoReflect() protoreflect.Message {
	mi := &file_llx_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Primitive.ProtoReflect.Descriptor instead.
func (*Primitive) Descriptor() ([]byte, []int) {
	return file_llx_proto_rawDescGZIP(), []int{0}
}

func (x *Primitive) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *Primitive) GetValue() []byte {
	if x != nil {
		return x.Value
	}
	return nil
}

func (x *Primitive) GetArray() []*Primitive {
	if x != nil {
		return x.Array
	}
	return nil
}

func (x *Primitive) GetMap() map[string]*Primitive {
	if x != nil {
		return x.Map
	}
	return nil
}

type Function struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	Type  string                 `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty"`
	Args  []*Primitive           `protobuf:"bytes,3,rep,name=args,proto3" json:"args,omitempty"`
	// FIXME: this is a suggestion to allow function calls to be bound
	// to non-local references; Remove this comment or remove the feature
	Binding       uint64 `protobuf:"varint,4,opt,name=binding,proto3" json:"binding,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Function) Reset() {
	*x = Function{}
	mi := &file_llx_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Function) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Function) ProtoMessage() {}

func (x *Function) ProtoReflect() protoreflect.Message {
	mi := &file_llx_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Function.ProtoReflect.Descriptor instead.
func (*Function) Descriptor() ([]byte, []int) {
	return file_llx_proto_rawDescGZIP(), []int{1}
}

func (x *Function) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *Function) GetArgs() []*Primitive {
	if x != nil {
		return x.Args
	}
	return nil
}

func (x *Function) GetBinding() uint64 {
	if x != nil {
		return x.Binding
	}
	return 0
}

type Chunk struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Call          Chunk_Call             `protobuf:"varint,1,opt,name=call,proto3,enum=cnquery.llx.Chunk_Call" json:"call,omitempty"`
	Id            string                 `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
	Primitive     *Primitive             `protobuf:"bytes,3,opt,name=primitive,proto3" json:"primitive,omitempty"`
	Function      *Function              `protobuf:"bytes,4,opt,name=function,proto3" json:"function,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Chunk) Reset() {
	*x = Chunk{}
	mi := &file_llx_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Chunk) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Chunk) ProtoMessage() {}

func (x *Chunk) ProtoReflect() protoreflect.Message {
	mi := &file_llx_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Chunk.ProtoReflect.Descriptor instead.
func (*Chunk) Descriptor() ([]byte, []int) {
	return file_llx_proto_rawDescGZIP(), []int{2}
}

func (x *Chunk) GetCall() Chunk_Call {
	if x != nil {
		return x.Call
	}
	return Chunk_PRIMITIVE
}

func (x *Chunk) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Chunk) GetPrimitive() *Primitive {
	if x != nil {
		return x.Primitive
	}
	return nil
}

func (x *Chunk) GetFunction() *Function {
	if x != nil {
		return x.Function
	}
	return nil
}

type AssertionMessage struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Template      string                 `protobuf:"bytes,1,opt,name=template,proto3" json:"template,omitempty"`
	Refs          []uint64               `protobuf:"varint,2,rep,packed,name=refs,proto3" json:"refs,omitempty"`
	Checksums     []string               `protobuf:"bytes,3,rep,name=checksums,proto3" json:"checksums,omitempty"`
	DecodeBlock   bool                   `protobuf:"varint,21,opt,name=decode_block,json=decodeBlock,proto3" json:"decode_block,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AssertionMessage) Reset() {
	*x = AssertionMessage{}
	mi := &file_llx_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AssertionMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssertionMessage) ProtoMessage() {}

func (x *AssertionMessage) ProtoReflect() protoreflect.Message {
	mi := &file_llx_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssertionMessage.ProtoReflect.Descriptor instead.
func (*AssertionMessage) Descriptor() ([]byte, []int) {
	return file_llx_proto_rawDescGZIP(), []int{3}
}

func (x *AssertionMessage) GetTemplate() string {
	if x != nil {
		return x.Template
	}
	return ""
}

func (x *AssertionMessage) GetRefs() []uint64 {
	if x != nil {
		return x.Refs
	}
	return nil
}

func (x *AssertionMessage) GetChecksums() []string {
	if x != nil {
		return x.Checksums
	}
	return nil
}

func (x *AssertionMessage) GetDecodeBlock() bool {
	if x != nil {
		return x.DecodeBlock
	}
	return false
}

type CodeV1 struct {
	state       protoimpl.MessageState `protogen:"open.v1"`
	Id          string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Code        []*Chunk               `protobuf:"bytes,2,rep,name=code,proto3" json:"code,omitempty"`
	Parameters  int32                  `protobuf:"varint,3,opt,name=parameters,proto3" json:"parameters,omitempty"`
	Entrypoints []int32                `protobuf:"varint,4,rep,packed,name=entrypoints,proto3" json:"entrypoints,omitempty"`
	Datapoints  []int32                `protobuf:"varint,5,rep,packed,name=datapoints,proto3" json:"datapoints,omitempty"`
	Checksums   map[int32]string       `protobuf:"bytes,6,rep,name=checksums,proto3" json:"checksums,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	Functions   []*CodeV1              `protobuf:"bytes,7,rep,name=functions,proto3" json:"functions,omitempty"`
	// protolint:disable:next FIELD_NAMES_LOWER_SNAKE_CASE
	SingleValue   bool                        `protobuf:"varint,8,opt,name=singleValue,proto3" json:"singleValue,omitempty"`
	Assertions    map[int32]*AssertionMessage `protobuf:"bytes,20,rep,name=assertions,proto3" json:"assertions,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CodeV1) Reset() {
	*x = CodeV1{}
	mi := &file_llx_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CodeV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CodeV1) ProtoMessage() {}

func (x *CodeV1) ProtoReflect() protoreflect.Message {
	mi := &file_llx_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CodeV1.ProtoReflect.Descriptor instead.
func (*CodeV1) Descriptor() ([]byte, []int) {
	return file_llx_proto_rawDescGZIP(), []int{4}
}

func (x *CodeV1) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *CodeV1) GetCode() []*Chunk {
	if x != nil {
		return x.Code
	}
	return nil
}

func (x *CodeV1) GetParameters() int32 {
	if x != nil {
		return x.Parameters
	}
	return 0
}

func (x *CodeV1) GetEntrypoints() []int32 {
	if x != nil {
		return x.Entrypoints
	}
	return nil
}

func (x *CodeV1) GetDatapoints() []int32 {
	if x != nil {
		return x.Datapoints
	}
	return nil
}

func (x *CodeV1) GetChecksums() map[int32]string {
	if x != nil {
		return x.Checksums
	}
	return nil
}

func (x *CodeV1) GetFunctions() []*CodeV1 {
	if x != nil {
		return x.Functions
	}
	return nil
}

func (x *CodeV1) GetSingleValue() bool {
	if x != nil {
		return x.SingleValue
	}
	return false
}

func (x *CodeV1) GetAssertions() map[int32]*AssertionMessage {
	if x != nil {
		return x.Assertions
	}
	return nil
}

type Block struct {
	state  protoimpl.MessageState `protogen:"open.v1"`
	Chunks []*Chunk               `protobuf:"bytes,1,rep,name=chunks,proto3" json:"chunks,omitempty"`
	// Identifies if we return multiple or just a single value.
	// The difference is that blocks usually return the block type (a kind of map)
	// where this flag instructs it to just return the value instead.
	// protolint:disable:next FIELD_NAMES_LOWER_SNAKE_CASE
	SingleValue bool `protobuf:"varint,2,opt,name=singleValue,proto3" json:"singleValue,omitempty"`
	// Number of parameters that are directly provided to this block
	Parameters    int32    `protobuf:"varint,3,opt,name=parameters,proto3" json:"parameters,omitempty"`
	Entrypoints   []uint64 `protobuf:"varint,4,rep,packed,name=entrypoints,proto3" json:"entrypoints,omitempty"`
	Datapoints    []uint64 `protobuf:"varint,5,rep,packed,name=datapoints,proto3" json:"datapoints,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Block) Reset() {
	*x = Block{}
	mi := &file_llx_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Block) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Block) ProtoMessage() {}

func (x *Block) ProtoReflect() protoreflect.Message {
	mi := &file_llx_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Block.ProtoReflect.Descriptor instead.
func (*Block) Descriptor() ([]byte, []int) {
	return file_llx_proto_rawDescGZIP(), []int{5}
}

func (x *Block) GetChunks() []*Chunk {
	if x != nil {
		return x.Chunks
	}
	return nil
}

func (x *Block) GetSingleValue() bool {
	if x != nil {
		return x.SingleValue
	}
	return false
}

func (x *Block) GetParameters() int32 {
	if x != nil {
		return x.Parameters
	}
	return 0
}

func (x *Block) GetEntrypoints() []uint64 {
	if x != nil {
		return x.Entrypoints
	}
	return nil
}

func (x *Block) GetDatapoints() []uint64 {
	if x != nil {
		return x.Datapoints
	}
	return nil
}

type CodeV2 struct {
	state         protoimpl.MessageState       `protogen:"open.v1"`
	Id            string                       `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Blocks        []*Block                     `protobuf:"bytes,2,rep,name=blocks,proto3" json:"blocks,omitempty"`
	Checksums     map[uint64]string            `protobuf:"bytes,5,rep,name=checksums,proto3" json:"checksums,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	Assertions    map[uint64]*AssertionMessage `protobuf:"bytes,20,rep,name=assertions,proto3" json:"assertions,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CodeV2) Reset() {
	*x = CodeV2{}
	mi := &file_llx_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CodeV2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CodeV2) ProtoMessage() {}

func (x *CodeV2) ProtoReflect() protoreflect.Message {
	mi := &file_llx_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CodeV2.ProtoReflect.Descriptor instead.
func (*CodeV2) Descriptor() ([]byte, []int) {
	return file_llx_proto_rawDescGZIP(), []int{6}
}

func (x *CodeV2) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *CodeV2) GetBlocks() []*Block {
	if x != nil {
		return x.Blocks
	}
	return nil
}

func (x *CodeV2) GetChecksums() map[uint64]string {
	if x != nil {
		return x.Checksums
	}
	return nil
}

func (x *CodeV2) GetAssertions() map[uint64]*AssertionMessage {
	if x != nil {
		return x.Assertions
	}
	return nil
}

type Labels struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Labels        map[string]string      `protobuf:"bytes,1,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Labels) Reset() {
	*x = Labels{}
	mi := &file_llx_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Labels) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Labels) ProtoMessage() {}

func (x *Labels) ProtoReflect() protoreflect.Message {
	mi := &file_llx_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Labels.ProtoReflect.Descriptor instead.
func (*Labels) Descriptor() ([]byte, []int) {
	return file_llx_proto_rawDescGZIP(), []int{7}
}

func (x *Labels) GetLabels() map[string]string {
	if x != nil {
		return x.Labels
	}
	return nil
}

type Documentation struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Field         string                 `protobuf:"bytes,1,opt,name=field,proto3" json:"field,omitempty"`
	Title         string                 `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Desc          string                 `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Documentation) Reset() {
	*x = Documentation{}
	mi := &file_llx_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Documentation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Documentation) ProtoMessage() {}

func (x *Documentation) ProtoReflect() protoreflect.Message {
	mi := &file_llx_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Documentation.ProtoReflect.Descriptor instead.
func (*Documentation) Descriptor() ([]byte, []int) {
	return file_llx_proto_rawDescGZIP(), []int{8}
}

func (x *Documentation) GetField() string {
	if x != nil {
		return x.Field
	}
	return ""
}

func (x *Documentation) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *Documentation) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

type CodeBundle struct {
	state            protoimpl.MessageState       `protogen:"open.v1"`
	CodeV2           *CodeV2                      `protobuf:"bytes,6,opt,name=code_v2,json=codeV2,proto3" json:"code_v2,omitempty"`
	Suggestions      []*Documentation             `protobuf:"bytes,2,rep,name=suggestions,proto3" json:"suggestions,omitempty"`
	Source           string                       `protobuf:"bytes,3,opt,name=source,proto3" json:"source,omitempty"`
	Labels           *Labels                      `protobuf:"bytes,4,opt,name=labels,proto3" json:"labels,omitempty"`
	Props            map[string]string            `protobuf:"bytes,5,rep,name=props,proto3" json:"props,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"` // name + type
	Version          string                       `protobuf:"bytes,20,opt,name=version,proto3" json:"version,omitempty"`
	MinMondooVersion string                       `protobuf:"bytes,22,opt,name=min_mondoo_version,json=minMondooVersion,proto3" json:"min_mondoo_version,omitempty"`
	Assertions       map[string]*AssertionMessage `protobuf:"bytes,23,rep,name=assertions,proto3" json:"assertions,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	// name + ref to the block of data
	AutoExpand map[string]uint64 `protobuf:"bytes,24,rep,name=auto_expand,json=autoExpand,proto3" json:"auto_expand,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	// ref + variable name; only used during label creation and discarded
	// afterwards
	Vars          map[uint64]string `protobuf:"bytes,25,rep,name=vars,proto3" json:"vars,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CodeBundle) Reset() {
	*x = CodeBundle{}
	mi := &file_llx_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CodeBundle) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CodeBundle) ProtoMessage() {}

func (x *CodeBundle) ProtoReflect() protoreflect.Message {
	mi := &file_llx_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CodeBundle.ProtoReflect.Descriptor instead.
func (*CodeBundle) Descriptor() ([]byte, []int) {
	return file_llx_proto_rawDescGZIP(), []int{9}
}

func (x *CodeBundle) GetCodeV2() *CodeV2 {
	if x != nil {
		return x.CodeV2
	}
	return nil
}

func (x *CodeBundle) GetSuggestions() []*Documentation {
	if x != nil {
		return x.Suggestions
	}
	return nil
}

func (x *CodeBundle) GetSource() string {
	if x != nil {
		return x.Source
	}
	return ""
}

func (x *CodeBundle) GetLabels() *Labels {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *CodeBundle) GetProps() map[string]string {
	if x != nil {
		return x.Props
	}
	return nil
}

func (x *CodeBundle) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *CodeBundle) GetMinMondooVersion() string {
	if x != nil {
		return x.MinMondooVersion
	}
	return ""
}

func (x *CodeBundle) GetAssertions() map[string]*AssertionMessage {
	if x != nil {
		return x.Assertions
	}
	return nil
}

func (x *CodeBundle) GetAutoExpand() map[string]uint64 {
	if x != nil {
		return x.AutoExpand
	}
	return nil
}

func (x *CodeBundle) GetVars() map[uint64]string {
	if x != nil {
		return x.Vars
	}
	return nil
}

type Result struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          *Primitive             `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	Error         string                 `protobuf:"bytes,2,opt,name=error,proto3" json:"error,omitempty"`
	CodeId        string                 `protobuf:"bytes,3,opt,name=code_id,json=codeId,proto3" json:"code_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Result) Reset() {
	*x = Result{}
	mi := &file_llx_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Result) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Result) ProtoMessage() {}

func (x *Result) ProtoReflect() protoreflect.Message {
	mi := &file_llx_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Result.ProtoReflect.Descriptor instead.
func (*Result) Descriptor() ([]byte, []int) {
	return file_llx_proto_rawDescGZIP(), []int{10}
}

func (x *Result) GetData() *Primitive {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *Result) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

func (x *Result) GetCodeId() string {
	if x != nil {
		return x.CodeId
	}
	return ""
}

type ResourceRecording struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Resource      string                 `protobuf:"bytes,1,opt,name=resource,proto3" json:"resource,omitempty"`
	Id            string                 `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
	Fields        map[string]*Result     `protobuf:"bytes,3,rep,name=fields,proto3" json:"fields,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	Created       int64                  `protobuf:"varint,4,opt,name=created,proto3" json:"created,omitempty"`
	Updated       int64                  `protobuf:"varint,5,opt,name=updated,proto3" json:"updated,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ResourceRecording) Reset() {
	*x = ResourceRecording{}
	mi := &file_llx_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ResourceRecording) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResourceRecording) ProtoMessage() {}

func (x *ResourceRecording) ProtoReflect() protoreflect.Message {
	mi := &file_llx_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResourceRecording.ProtoReflect.Descriptor instead.
func (*ResourceRecording) Descriptor() ([]byte, []int) {
	return file_llx_proto_rawDescGZIP(), []int{11}
}

func (x *ResourceRecording) GetResource() string {
	if x != nil {
		return x.Resource
	}
	return ""
}

func (x *ResourceRecording) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ResourceRecording) GetFields() map[string]*Result {
	if x != nil {
		return x.Fields
	}
	return nil
}

func (x *ResourceRecording) GetCreated() int64 {
	if x != nil {
		return x.Created
	}
	return 0
}

func (x *ResourceRecording) GetUpdated() int64 {
	if x != nil {
		return x.Updated
	}
	return 0
}

type Rating struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"` // can either be the collection or the query
	Endpoint      string                 `protobuf:"bytes,2,opt,name=endpoint,proto3" json:"endpoint,omitempty"`
	Tests         int32                  `protobuf:"varint,3,opt,name=tests,proto3" json:"tests,omitempty"`
	Score         int32                  `protobuf:"varint,4,opt,name=score,proto3" json:"score,omitempty"`
	Trend         int32                  `protobuf:"varint,5,opt,name=trend,proto3" json:"trend,omitempty"`
	Date          string                 `protobuf:"bytes,6,opt,name=date,proto3" json:"date,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Rating) Reset() {
	*x = Rating{}
	mi := &file_llx_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Rating) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Rating) ProtoMessage() {}

func (x *Rating) ProtoReflect() protoreflect.Message {
	mi := &file_llx_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Rating.ProtoReflect.Descriptor instead.
func (*Rating) Descriptor() ([]byte, []int) {
	return file_llx_proto_rawDescGZIP(), []int{12}
}

func (x *Rating) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Rating) GetEndpoint() string {
	if x != nil {
		return x.Endpoint
	}
	return ""
}

func (x *Rating) GetTests() int32 {
	if x != nil {
		return x.Tests
	}
	return 0
}

func (x *Rating) GetScore() int32 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *Rating) GetTrend() int32 {
	if x != nil {
		return x.Trend
	}
	return 0
}

func (x *Rating) GetDate() string {
	if x != nil {
		return x.Date
	}
	return ""
}

type AssessmentItem struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Checksum      string                 `protobuf:"bytes,2,opt,name=checksum,proto3" json:"checksum,omitempty"`
	Entrypoint    uint64                 `protobuf:"varint,3,opt,name=entrypoint,proto3" json:"entrypoint,omitempty"`
	Ref           uint64                 `protobuf:"varint,9,opt,name=ref,proto3" json:"ref,omitempty"`
	Expected      *Primitive             `protobuf:"bytes,4,opt,name=expected,proto3" json:"expected,omitempty"`
	Actual        *Primitive             `protobuf:"bytes,5,opt,name=actual,proto3" json:"actual,omitempty"`
	Operation     string                 `protobuf:"bytes,6,opt,name=operation,proto3" json:"operation,omitempty"`
	Error         string                 `protobuf:"bytes,7,opt,name=error,proto3" json:"error,omitempty"`
	IsAssertion   bool                   `protobuf:"varint,8,opt,name=is_assertion,json=isAssertion,proto3" json:"is_assertion,omitempty"`
	Template      string                 `protobuf:"bytes,20,opt,name=template,proto3" json:"template,omitempty"`
	Data          []*Primitive           `protobuf:"bytes,21,rep,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AssessmentItem) Reset() {
	*x = AssessmentItem{}
	mi := &file_llx_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AssessmentItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssessmentItem) ProtoMessage() {}

func (x *AssessmentItem) ProtoReflect() protoreflect.Message {
	mi := &file_llx_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssessmentItem.ProtoReflect.Descriptor instead.
func (*AssessmentItem) Descriptor() ([]byte, []int) {
	return file_llx_proto_rawDescGZIP(), []int{13}
}

func (x *AssessmentItem) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *AssessmentItem) GetChecksum() string {
	if x != nil {
		return x.Checksum
	}
	return ""
}

func (x *AssessmentItem) GetEntrypoint() uint64 {
	if x != nil {
		return x.Entrypoint
	}
	return 0
}

func (x *AssessmentItem) GetRef() uint64 {
	if x != nil {
		return x.Ref
	}
	return 0
}

func (x *AssessmentItem) GetExpected() *Primitive {
	if x != nil {
		return x.Expected
	}
	return nil
}

func (x *AssessmentItem) GetActual() *Primitive {
	if x != nil {
		return x.Actual
	}
	return nil
}

func (x *AssessmentItem) GetOperation() string {
	if x != nil {
		return x.Operation
	}
	return ""
}

func (x *AssessmentItem) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

func (x *AssessmentItem) GetIsAssertion() bool {
	if x != nil {
		return x.IsAssertion
	}
	return false
}

func (x *AssessmentItem) GetTemplate() string {
	if x != nil {
		return x.Template
	}
	return ""
}

func (x *AssessmentItem) GetData() []*Primitive {
	if x != nil {
		return x.Data
	}
	return nil
}

type Assessment struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Checksum      string                 `protobuf:"bytes,1,opt,name=checksum,proto3" json:"checksum,omitempty"`
	Success       bool                   `protobuf:"varint,2,opt,name=success,proto3" json:"success,omitempty"`
	IsAssertion   bool                   `protobuf:"varint,3,opt,name=is_assertion,json=isAssertion,proto3" json:"is_assertion,omitempty"`
	Results       []*AssessmentItem      `protobuf:"bytes,4,rep,name=results,proto3" json:"results,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Assessment) Reset() {
	*x = Assessment{}
	mi := &file_llx_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Assessment) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Assessment) ProtoMessage() {}

func (x *Assessment) ProtoReflect() protoreflect.Message {
	mi := &file_llx_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Assessment.ProtoReflect.Descriptor instead.
func (*Assessment) Descriptor() ([]byte, []int) {
	return file_llx_proto_rawDescGZIP(), []int{14}
}

func (x *Assessment) GetChecksum() string {
	if x != nil {
		return x.Checksum
	}
	return ""
}

func (x *Assessment) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *Assessment) GetIsAssertion() bool {
	if x != nil {
		return x.IsAssertion
	}
	return false
}

func (x *Assessment) GetResults() []*AssessmentItem {
	if x != nil {
		return x.Results
	}
	return nil
}

type IP struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Address       []byte                 `protobuf:"bytes,1,opt,name=address,proto3" json:"address,omitempty"`
	HasPrefix     bool                   `protobuf:"varint,2,opt,name=has_prefix,json=hasPrefix,proto3" json:"has_prefix,omitempty"`
	PrefixLength  int32                  `protobuf:"varint,3,opt,name=prefix_length,json=prefixLength,proto3" json:"prefix_length,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *IP) Reset() {
	*x = IP{}
	mi := &file_llx_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IP) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IP) ProtoMessage() {}

func (x *IP) ProtoReflect() protoreflect.Message {
	mi := &file_llx_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IP.ProtoReflect.Descriptor instead.
func (*IP) Descriptor() ([]byte, []int) {
	return file_llx_proto_rawDescGZIP(), []int{15}
}

func (x *IP) GetAddress() []byte {
	if x != nil {
		return x.Address
	}
	return nil
}

func (x *IP) GetHasPrefix() bool {
	if x != nil {
		return x.HasPrefix
	}
	return false
}

func (x *IP) GetPrefixLength() int32 {
	if x != nil {
		return x.PrefixLength
	}
	return 0
}

var File_llx_proto protoreflect.FileDescriptor

const file_llx_proto_rawDesc = "" +
	"\n" +
	"\tllx.proto\x12\vcnquery.llx\"\xe6\x01\n" +
	"\tPrimitive\x12\x12\n" +
	"\x04type\x18\x01 \x01(\tR\x04type\x12\x14\n" +
	"\x05value\x18\x02 \x01(\fR\x05value\x12,\n" +
	"\x05array\x18\x03 \x03(\v2\x16.cnquery.llx.PrimitiveR\x05array\x121\n" +
	"\x03map\x18\x04 \x03(\v2\x1f.cnquery.llx.Primitive.MapEntryR\x03map\x1aN\n" +
	"\bMapEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12,\n" +
	"\x05value\x18\x02 \x01(\v2\x16.cnquery.llx.PrimitiveR\x05value:\x028\x01\"d\n" +
	"\bFunction\x12\x12\n" +
	"\x04type\x18\x01 \x01(\tR\x04type\x12*\n" +
	"\x04args\x18\x03 \x03(\v2\x16.cnquery.llx.PrimitiveR\x04args\x12\x18\n" +
	"\abinding\x18\x04 \x01(\x04R\abinding\"\xe0\x01\n" +
	"\x05Chunk\x12+\n" +
	"\x04call\x18\x01 \x01(\x0e2\x17.cnquery.llx.Chunk.CallR\x04call\x12\x0e\n" +
	"\x02id\x18\x02 \x01(\tR\x02id\x124\n" +
	"\tprimitive\x18\x03 \x01(\v2\x16.cnquery.llx.PrimitiveR\tprimitive\x121\n" +
	"\bfunction\x18\x04 \x01(\v2\x15.cnquery.llx.FunctionR\bfunction\"1\n" +
	"\x04Call\x12\r\n" +
	"\tPRIMITIVE\x10\x00\x12\f\n" +
	"\bFUNCTION\x10\x01\x12\f\n" +
	"\bPROPERTY\x10\x02\"\x83\x01\n" +
	"\x10AssertionMessage\x12\x1a\n" +
	"\btemplate\x18\x01 \x01(\tR\btemplate\x12\x12\n" +
	"\x04refs\x18\x02 \x03(\x04R\x04refs\x12\x1c\n" +
	"\tchecksums\x18\x03 \x03(\tR\tchecksums\x12!\n" +
	"\fdecode_block\x18\x15 \x01(\bR\vdecodeBlock\"\x9a\x04\n" +
	"\x06CodeV1\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12&\n" +
	"\x04code\x18\x02 \x03(\v2\x12.cnquery.llx.ChunkR\x04code\x12\x1e\n" +
	"\n" +
	"parameters\x18\x03 \x01(\x05R\n" +
	"parameters\x12 \n" +
	"\ventrypoints\x18\x04 \x03(\x05R\ventrypoints\x12\x1e\n" +
	"\n" +
	"datapoints\x18\x05 \x03(\x05R\n" +
	"datapoints\x12@\n" +
	"\tchecksums\x18\x06 \x03(\v2\".cnquery.llx.CodeV1.ChecksumsEntryR\tchecksums\x121\n" +
	"\tfunctions\x18\a \x03(\v2\x13.cnquery.llx.CodeV1R\tfunctions\x12 \n" +
	"\vsingleValue\x18\b \x01(\bR\vsingleValue\x12C\n" +
	"\n" +
	"assertions\x18\x14 \x03(\v2#.cnquery.llx.CodeV1.AssertionsEntryR\n" +
	"assertions\x1a<\n" +
	"\x0eChecksumsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\x05R\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\x1a\\\n" +
	"\x0fAssertionsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\x05R\x03key\x123\n" +
	"\x05value\x18\x02 \x01(\v2\x1d.cnquery.llx.AssertionMessageR\x05value:\x028\x01\"\xb7\x01\n" +
	"\x05Block\x12*\n" +
	"\x06chunks\x18\x01 \x03(\v2\x12.cnquery.llx.ChunkR\x06chunks\x12 \n" +
	"\vsingleValue\x18\x02 \x01(\bR\vsingleValue\x12\x1e\n" +
	"\n" +
	"parameters\x18\x03 \x01(\x05R\n" +
	"parameters\x12 \n" +
	"\ventrypoints\x18\x04 \x03(\x04R\ventrypoints\x12\x1e\n" +
	"\n" +
	"datapoints\x18\x05 \x03(\x04R\n" +
	"datapoints\"\xe7\x02\n" +
	"\x06CodeV2\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12*\n" +
	"\x06blocks\x18\x02 \x03(\v2\x12.cnquery.llx.BlockR\x06blocks\x12@\n" +
	"\tchecksums\x18\x05 \x03(\v2\".cnquery.llx.CodeV2.ChecksumsEntryR\tchecksums\x12C\n" +
	"\n" +
	"assertions\x18\x14 \x03(\v2#.cnquery.llx.CodeV2.AssertionsEntryR\n" +
	"assertions\x1a<\n" +
	"\x0eChecksumsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\x04R\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\x1a\\\n" +
	"\x0fAssertionsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\x04R\x03key\x123\n" +
	"\x05value\x18\x02 \x01(\v2\x1d.cnquery.llx.AssertionMessageR\x05value:\x028\x01\"|\n" +
	"\x06Labels\x127\n" +
	"\x06labels\x18\x01 \x03(\v2\x1f.cnquery.llx.Labels.LabelsEntryR\x06labels\x1a9\n" +
	"\vLabelsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"O\n" +
	"\rDocumentation\x12\x14\n" +
	"\x05field\x18\x01 \x01(\tR\x05field\x12\x14\n" +
	"\x05title\x18\x02 \x01(\tR\x05title\x12\x12\n" +
	"\x04desc\x18\x03 \x01(\tR\x04desc\"\xa5\x06\n" +
	"\n" +
	"CodeBundle\x12,\n" +
	"\acode_v2\x18\x06 \x01(\v2\x13.cnquery.llx.CodeV2R\x06codeV2\x12<\n" +
	"\vsuggestions\x18\x02 \x03(\v2\x1a.cnquery.llx.DocumentationR\vsuggestions\x12\x16\n" +
	"\x06source\x18\x03 \x01(\tR\x06source\x12+\n" +
	"\x06labels\x18\x04 \x01(\v2\x13.cnquery.llx.LabelsR\x06labels\x128\n" +
	"\x05props\x18\x05 \x03(\v2\".cnquery.llx.CodeBundle.PropsEntryR\x05props\x12\x18\n" +
	"\aversion\x18\x14 \x01(\tR\aversion\x12,\n" +
	"\x12min_mondoo_version\x18\x16 \x01(\tR\x10minMondooVersion\x12G\n" +
	"\n" +
	"assertions\x18\x17 \x03(\v2'.cnquery.llx.CodeBundle.AssertionsEntryR\n" +
	"assertions\x12H\n" +
	"\vauto_expand\x18\x18 \x03(\v2'.cnquery.llx.CodeBundle.AutoExpandEntryR\n" +
	"autoExpand\x125\n" +
	"\x04vars\x18\x19 \x03(\v2!.cnquery.llx.CodeBundle.VarsEntryR\x04vars\x1a8\n" +
	"\n" +
	"PropsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\x1a\\\n" +
	"\x0fAssertionsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x123\n" +
	"\x05value\x18\x02 \x01(\v2\x1d.cnquery.llx.AssertionMessageR\x05value:\x028\x01\x1a=\n" +
	"\x0fAutoExpandEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\x04R\x05value:\x028\x01\x1a7\n" +
	"\tVarsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\x04R\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01J\x04\b\x01\x10\x02J\x04\b\x15\x10\x16\"c\n" +
	"\x06Result\x12*\n" +
	"\x04data\x18\x01 \x01(\v2\x16.cnquery.llx.PrimitiveR\x04data\x12\x14\n" +
	"\x05error\x18\x02 \x01(\tR\x05error\x12\x17\n" +
	"\acode_id\x18\x03 \x01(\tR\x06codeId\"\x87\x02\n" +
	"\x11ResourceRecording\x12\x1a\n" +
	"\bresource\x18\x01 \x01(\tR\bresource\x12\x0e\n" +
	"\x02id\x18\x02 \x01(\tR\x02id\x12B\n" +
	"\x06fields\x18\x03 \x03(\v2*.cnquery.llx.ResourceRecording.FieldsEntryR\x06fields\x12\x18\n" +
	"\acreated\x18\x04 \x01(\x03R\acreated\x12\x18\n" +
	"\aupdated\x18\x05 \x01(\x03R\aupdated\x1aN\n" +
	"\vFieldsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12)\n" +
	"\x05value\x18\x02 \x01(\v2\x13.cnquery.llx.ResultR\x05value:\x028\x01\"\x8a\x01\n" +
	"\x06Rating\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x1a\n" +
	"\bendpoint\x18\x02 \x01(\tR\bendpoint\x12\x14\n" +
	"\x05tests\x18\x03 \x01(\x05R\x05tests\x12\x14\n" +
	"\x05score\x18\x04 \x01(\x05R\x05score\x12\x14\n" +
	"\x05trend\x18\x05 \x01(\x05R\x05trend\x12\x12\n" +
	"\x04date\x18\x06 \x01(\tR\x04date\"\xfb\x02\n" +
	"\x0eAssessmentItem\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x1a\n" +
	"\bchecksum\x18\x02 \x01(\tR\bchecksum\x12\x1e\n" +
	"\n" +
	"entrypoint\x18\x03 \x01(\x04R\n" +
	"entrypoint\x12\x10\n" +
	"\x03ref\x18\t \x01(\x04R\x03ref\x122\n" +
	"\bexpected\x18\x04 \x01(\v2\x16.cnquery.llx.PrimitiveR\bexpected\x12.\n" +
	"\x06actual\x18\x05 \x01(\v2\x16.cnquery.llx.PrimitiveR\x06actual\x12\x1c\n" +
	"\toperation\x18\x06 \x01(\tR\toperation\x12\x14\n" +
	"\x05error\x18\a \x01(\tR\x05error\x12!\n" +
	"\fis_assertion\x18\b \x01(\bR\visAssertion\x12\x1a\n" +
	"\btemplate\x18\x14 \x01(\tR\btemplate\x12*\n" +
	"\x04data\x18\x15 \x03(\v2\x16.cnquery.llx.PrimitiveR\x04data\"\x9c\x01\n" +
	"\n" +
	"Assessment\x12\x1a\n" +
	"\bchecksum\x18\x01 \x01(\tR\bchecksum\x12\x18\n" +
	"\asuccess\x18\x02 \x01(\bR\asuccess\x12!\n" +
	"\fis_assertion\x18\x03 \x01(\bR\visAssertion\x125\n" +
	"\aresults\x18\x04 \x03(\v2\x1b.cnquery.llx.AssessmentItemR\aresults\"b\n" +
	"\x02IP\x12\x18\n" +
	"\aaddress\x18\x01 \x01(\fR\aaddress\x12\x1d\n" +
	"\n" +
	"has_prefix\x18\x02 \x01(\bR\thasPrefix\x12#\n" +
	"\rprefix_length\x18\x03 \x01(\x05R\fprefixLengthB\x1fZ\x1dgo.mondoo.com/cnquery/v11/llxb\x06proto3"

var (
	file_llx_proto_rawDescOnce sync.Once
	file_llx_proto_rawDescData []byte
)

func file_llx_proto_rawDescGZIP() []byte {
	file_llx_proto_rawDescOnce.Do(func() {
		file_llx_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_llx_proto_rawDesc), len(file_llx_proto_rawDesc)))
	})
	return file_llx_proto_rawDescData
}

var file_llx_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_llx_proto_msgTypes = make([]protoimpl.MessageInfo, 27)
var file_llx_proto_goTypes = []any{
	(Chunk_Call)(0),           // 0: cnquery.llx.Chunk.Call
	(*Primitive)(nil),         // 1: cnquery.llx.Primitive
	(*Function)(nil),          // 2: cnquery.llx.Function
	(*Chunk)(nil),             // 3: cnquery.llx.Chunk
	(*AssertionMessage)(nil),  // 4: cnquery.llx.AssertionMessage
	(*CodeV1)(nil),            // 5: cnquery.llx.CodeV1
	(*Block)(nil),             // 6: cnquery.llx.Block
	(*CodeV2)(nil),            // 7: cnquery.llx.CodeV2
	(*Labels)(nil),            // 8: cnquery.llx.Labels
	(*Documentation)(nil),     // 9: cnquery.llx.Documentation
	(*CodeBundle)(nil),        // 10: cnquery.llx.CodeBundle
	(*Result)(nil),            // 11: cnquery.llx.Result
	(*ResourceRecording)(nil), // 12: cnquery.llx.ResourceRecording
	(*Rating)(nil),            // 13: cnquery.llx.Rating
	(*AssessmentItem)(nil),    // 14: cnquery.llx.AssessmentItem
	(*Assessment)(nil),        // 15: cnquery.llx.Assessment
	(*IP)(nil),                // 16: cnquery.llx.IP
	nil,                       // 17: cnquery.llx.Primitive.MapEntry
	nil,                       // 18: cnquery.llx.CodeV1.ChecksumsEntry
	nil,                       // 19: cnquery.llx.CodeV1.AssertionsEntry
	nil,                       // 20: cnquery.llx.CodeV2.ChecksumsEntry
	nil,                       // 21: cnquery.llx.CodeV2.AssertionsEntry
	nil,                       // 22: cnquery.llx.Labels.LabelsEntry
	nil,                       // 23: cnquery.llx.CodeBundle.PropsEntry
	nil,                       // 24: cnquery.llx.CodeBundle.AssertionsEntry
	nil,                       // 25: cnquery.llx.CodeBundle.AutoExpandEntry
	nil,                       // 26: cnquery.llx.CodeBundle.VarsEntry
	nil,                       // 27: cnquery.llx.ResourceRecording.FieldsEntry
}
var file_llx_proto_depIdxs = []int32{
	1,  // 0: cnquery.llx.Primitive.array:type_name -> cnquery.llx.Primitive
	17, // 1: cnquery.llx.Primitive.map:type_name -> cnquery.llx.Primitive.MapEntry
	1,  // 2: cnquery.llx.Function.args:type_name -> cnquery.llx.Primitive
	0,  // 3: cnquery.llx.Chunk.call:type_name -> cnquery.llx.Chunk.Call
	1,  // 4: cnquery.llx.Chunk.primitive:type_name -> cnquery.llx.Primitive
	2,  // 5: cnquery.llx.Chunk.function:type_name -> cnquery.llx.Function
	3,  // 6: cnquery.llx.CodeV1.code:type_name -> cnquery.llx.Chunk
	18, // 7: cnquery.llx.CodeV1.checksums:type_name -> cnquery.llx.CodeV1.ChecksumsEntry
	5,  // 8: cnquery.llx.CodeV1.functions:type_name -> cnquery.llx.CodeV1
	19, // 9: cnquery.llx.CodeV1.assertions:type_name -> cnquery.llx.CodeV1.AssertionsEntry
	3,  // 10: cnquery.llx.Block.chunks:type_name -> cnquery.llx.Chunk
	6,  // 11: cnquery.llx.CodeV2.blocks:type_name -> cnquery.llx.Block
	20, // 12: cnquery.llx.CodeV2.checksums:type_name -> cnquery.llx.CodeV2.ChecksumsEntry
	21, // 13: cnquery.llx.CodeV2.assertions:type_name -> cnquery.llx.CodeV2.AssertionsEntry
	22, // 14: cnquery.llx.Labels.labels:type_name -> cnquery.llx.Labels.LabelsEntry
	7,  // 15: cnquery.llx.CodeBundle.code_v2:type_name -> cnquery.llx.CodeV2
	9,  // 16: cnquery.llx.CodeBundle.suggestions:type_name -> cnquery.llx.Documentation
	8,  // 17: cnquery.llx.CodeBundle.labels:type_name -> cnquery.llx.Labels
	23, // 18: cnquery.llx.CodeBundle.props:type_name -> cnquery.llx.CodeBundle.PropsEntry
	24, // 19: cnquery.llx.CodeBundle.assertions:type_name -> cnquery.llx.CodeBundle.AssertionsEntry
	25, // 20: cnquery.llx.CodeBundle.auto_expand:type_name -> cnquery.llx.CodeBundle.AutoExpandEntry
	26, // 21: cnquery.llx.CodeBundle.vars:type_name -> cnquery.llx.CodeBundle.VarsEntry
	1,  // 22: cnquery.llx.Result.data:type_name -> cnquery.llx.Primitive
	27, // 23: cnquery.llx.ResourceRecording.fields:type_name -> cnquery.llx.ResourceRecording.FieldsEntry
	1,  // 24: cnquery.llx.AssessmentItem.expected:type_name -> cnquery.llx.Primitive
	1,  // 25: cnquery.llx.AssessmentItem.actual:type_name -> cnquery.llx.Primitive
	1,  // 26: cnquery.llx.AssessmentItem.data:type_name -> cnquery.llx.Primitive
	14, // 27: cnquery.llx.Assessment.results:type_name -> cnquery.llx.AssessmentItem
	1,  // 28: cnquery.llx.Primitive.MapEntry.value:type_name -> cnquery.llx.Primitive
	4,  // 29: cnquery.llx.CodeV1.AssertionsEntry.value:type_name -> cnquery.llx.AssertionMessage
	4,  // 30: cnquery.llx.CodeV2.AssertionsEntry.value:type_name -> cnquery.llx.AssertionMessage
	4,  // 31: cnquery.llx.CodeBundle.AssertionsEntry.value:type_name -> cnquery.llx.AssertionMessage
	11, // 32: cnquery.llx.ResourceRecording.FieldsEntry.value:type_name -> cnquery.llx.Result
	33, // [33:33] is the sub-list for method output_type
	33, // [33:33] is the sub-list for method input_type
	33, // [33:33] is the sub-list for extension type_name
	33, // [33:33] is the sub-list for extension extendee
	0,  // [0:33] is the sub-list for field type_name
}

func init() { file_llx_proto_init() }
func file_llx_proto_init() {
	if File_llx_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_llx_proto_rawDesc), len(file_llx_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   27,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_llx_proto_goTypes,
		DependencyIndexes: file_llx_proto_depIdxs,
		EnumInfos:         file_llx_proto_enumTypes,
		MessageInfos:      file_llx_proto_msgTypes,
	}.Build()
	File_llx_proto = out.File
	file_llx_proto_goTypes = nil
	file_llx_proto_depIdxs = nil
}
