// Copyright (c) Mondoo, Inc.
// SPDX-License-Identifier: BUSL-1.1

syntax = "proto3";

package cnquery.llx;

option go_package = "go.mondoo.com/cnquery/v11/llx";

message Primitive {
  string type = 1;

  // The value of the primitive in case of bool|int|float|string|ref|json
  // For array/map it holds the exact datatype as a string,
  // e.g. []int or map[string]string
  bytes value = 2;

  // In case of an array primitive, holds the data of the array
  // protolint:disable REPEATED_FIELD_NAMES_PLURALIZED
  repeated Primitive array = 3;

  // In case of a map primitive, holds the data of the map
  map<string, Primitive> map = 4;
}

message Function {
  string type = 1;
  repeated Primitive args = 3;
  // FIXME: this is a suggestion to allow function calls to be bound
  // to non-local references; Remove this comment or remove the feature
  uint64 binding = 4;
}

message Chunk {
  // protolint:disable:next ENUM_FIELD_NAMES_PREFIX
  enum Call {
    // protolint:disable:next ENUM_FIELD_NAMES_ZERO_VALUE_END_WITH
    PRIMITIVE = 0;
    FUNCTION = 1;
    PROPERTY = 2;
  }
  Call call = 1;
  string id = 2;
  Primitive primitive = 3;
  Function function = 4;
}

message AssertionMessage {
  string template = 1;
  repeated uint64 refs = 2;
  repeated string checksums = 3;
  bool decode_block = 21;
}

message CodeV1 {
  string id = 1;
  repeated Chunk code = 2;
  int32 parameters = 3;
  repeated int32 entrypoints = 4;
  repeated int32 datapoints = 5;
  map<int32,string> checksums = 6;
  repeated CodeV1 functions = 7;
  // protolint:disable:next FIELD_NAMES_LOWER_SNAKE_CASE
  bool singleValue = 8; 
  map<int32,AssertionMessage> assertions = 20;
}

message Block {
  repeated Chunk chunks = 1;
  // Identifies if we return multiple or just a single value.
  // The difference is that blocks usually return the block type (a kind of map)
  // where this flag instructs it to just return the value instead.
  // protolint:disable:next FIELD_NAMES_LOWER_SNAKE_CASE
  bool singleValue = 2; 
  // Number of parameters that are directly provided to this block
  int32 parameters = 3;
  repeated uint64 entrypoints = 4;
  repeated uint64 datapoints = 5;
}

message CodeV2 {
  string id = 1;
  repeated Block blocks = 2;
  map<uint64,string> checksums = 5;
  map<uint64,AssertionMessage> assertions = 20;
}

message Labels {
  map<string,string> labels = 1;
}

message Documentation {
  string field = 1;
  string title = 2;
  string desc = 3;
}

message CodeBundle {
  reserved 1, 21;
  CodeV2 code_v2 = 6;
  repeated Documentation suggestions = 2;
  string source = 3;
  Labels labels = 4;
  map<string,string> props = 5; // name + type
  string version = 20;
  string min_mondoo_version = 22;
  map<string,AssertionMessage> assertions = 23;
  // name + ref to the block of data
  map<string,uint64> auto_expand = 24;
  // ref + variable name; only used during label creation and discarded 
  // afterwards
  map<uint64,string> vars = 25;
}

message Result {
  Primitive data = 1;
  string error = 2;
  string code_id = 3;
}

message ResourceRecording {
  string resource = 1;
  string id = 2;
  map<string, Result> fields = 3;
  int64 created = 4;
  int64 updated = 5;
}

message Rating {
  string id = 1; // can either be the collection or the query
  string endpoint = 2;
  int32 tests = 3;
  int32 score = 4;
  int32 trend = 5;
  string date = 6;
}

message AssessmentItem {
  bool success = 1;
  string checksum = 2;
  uint64 entrypoint = 3;
  uint64 ref = 9;
  Primitive expected = 4;
  Primitive actual = 5;
  string operation = 6;
  string error = 7;
  bool is_assertion = 8;
  string template = 20;
  repeated Primitive data = 21;
}

message Assessment {
  string checksum = 1;
  bool success = 2;
  bool is_assertion = 3;
  repeated AssessmentItem results = 4;
}

message IP {
  bytes address = 1;
  bool has_prefix = 2;
  int32 prefix_length = 3;
}