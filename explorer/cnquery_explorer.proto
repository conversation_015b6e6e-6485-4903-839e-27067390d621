// Copyright (c) Mondoo, Inc.
// SPDX-License-Identifier: BUSL-1.1

syntax = "proto3";

package cnquery.explorer;

import "google/protobuf/any.proto";
import "llx/llx.proto";
import "providers-sdk/v1/inventory/inventory.proto";

option go_package = "go.mondoo.com/cnquery/v11/explorer";

message Bundle {
  string owner_mrn = 1; 
  repeated QueryPack packs = 2;
  repeated Mquery queries = 3;
  repeated Property props = 4;
}

message QueryGroup {
  repeated Mquery queries = 3;

  // filter for the assets this applies to
  Filters filters = 20;

  // metadata
  string title = 24;

  int64 created = 32;
  int64 modified = 33;
}

message QueryPack {
  reserved 7, 20, 43;
  
  // user-defined UID, which is used to generate the MRN
  string uid = 36;
  // user-defined, only used during query ingest in case they have no context
  string context = 8;

  // MRN to universally identify this query.
  // UIDs are local to QueryPacks, MRNs are global
  string mrn = 1;

  string name = 2;
  string version = 3;
  string owner_mrn = 4; // auto-generated
  
  repeated Mquery queries = 6;
  repeated QueryGroup groups = 11;

  repeated Property props = 35;
  Filters computed_filters = 47; // auto-generated
  Filters filters = 48;
  
  string license = 21;
  QueryPackDocs docs = 22;
  string summary = 46;

  repeated Author authors = 30;
  int64 created = 32;
  int64 modified = 33;
  map<string,string> tags = 34;

  // internal fields
  string local_content_checksum = 23;
  string local_execution_checksum = 24;
}

message ObjectRef {
  string mrn = 1;
  string uid = 2;
  map<string,string> tags = 3;
}

// User-defined and internal filters. Users may use:
// 1. filters: asset.name == /sth/
// 2. filters:
//    - mql: asset.name == /sth/
//
// Internally we use:
// 3. filters:
//      items:
//        <checksum>: { ..mquery.. }
message Filters {
  map<string,Mquery> items = 1;
}

message QueryPacks {
  repeated QueryPack items = 1;
}

message Docs {
  string desc = 1;
  repeated MqueryRef refs = 2;
}

// Properties allow users to configure queries and bundles in pre-defined ways.
// They return one value only, have a type, and may specify which query they
// target, while used in querypacks (or other groupings like policies).
// They may contain additional metadata to provide more information.
//
// Note: At the time of writing properties do not pull from other properties.
// They may do that later.
message Property {
  string mql = 1;
  string code_id = 2;
  string checksum = 3;
  string mrn = 4;
  // UID is only needed on Mquery upload, when the MRN is computed.
  // It is not be persisted.
  string uid = 5;
  string type = 6;
  string context = 7;
  // protolint:disable REPEATED_FIELD_NAMES_PLURALIZED
  repeated ObjectRef for = 8;
  string title = 20;
  string desc = 35;
}

// protolint:disable ENUM_FIELD_NAMES_PREFIX
// protolint:disable ENUM_FIELD_NAMES_ZERO_VALUE_END_WITH
enum Action {
  UNSPECIFIED = 0;
  MODIFY = 1;
  DEACTIVATE = 2;
  ACTIVATE = 3;
  IGNORE = 4;
  OUT_OF_SCOPE = 5;
}

// Mquery represents the MQL and metadata that form a query
message Mquery {
  // FIXME: DEPRECATED, remove in v12.0 vv
  // This is replaced by the field "mql"
  string query = 40;
  // This is moved into docs.refs
  repeated MqueryRef refs = 22;
  // ^^

  string mql = 1;
  string code_id = 2;
  string checksum = 3;
  string mrn = 4;
  // UID is only needed on Mquery upload, when the MRN is computed.
  // It is not be persisted.
  string uid = 5;
  string type = 6;
  string context = 7;
  string title = 20;
  MqueryDocs docs = 21;
  // Desc is only used if docs.desc is not defined. It is not persisted.
  string desc = 35;
  Impact impact = 23;
  map<string,string> tags = 34;
  Filters filters = 37;
  repeated Property props = 38;
  repeated ObjectRef variants = 39;
  // Action is used for all query overrides (eg: in packs, policies, APIs etc)
  Action action = 41;
}

// protolint:disable ENUM_FIELD_NAMES_PREFIX
// protolint:disable ENUM_FIELD_NAMES_ZERO_VALUE_END_WITH
enum ScoringSystem {
  // Default value, should not be used
  SCORING_UNSPECIFIED = 0;
  // Weighted scoring mechanism which uses the weight defined at the query
  // level to calculate the score.
  WEIGHTED = 1;
  // Worst scoring mechanism which uses the worst score of all findings
  // to calculate the policy score.
  WORST = 2;
  // Average scoring mechanism which uses the average score of all findings
  // to calculate the policy score.
  AVERAGE = 3;
  // Treats the query as data only, no scoring is applied.
  DATA_ONLY = 4;
  // Ignore scoring mechanism which ignores the score of each query.
  IGNORE_SCORE = 5;
  // Experimental: BANDED scoring mechanism which creates a score based on
  // the 4 categories of criticality (critical, high, medium, low) and
  // positions scores so that:
  // 1. Any critical/high issues won't generate a high score (upper limit)
  // 2. Lower scoring categories can have an impact on the score
  //    (e.g. 1 crit + 200 medium failures will be lower than 1 crit only)
  // 3. A large collection of medium findings won't generate a critical score
  BANDED = 6;
  // Experimental: DECAYED scoring uses a scaled decay function to estimate a
  // score value. This means that a few critical findings will quickly reduce
  // the resulting score, but it won't just drop it to the lowest value.
  DECAYED = 7;
  DISABLED = 8;
}

// Impact explains how important certain queries are. They are especially useful
// in weighted testing where results need to be prioritized. They can also
// serve as a priority list for data that is collected.
message Impact {
  // Value is between 0 to 100 (most impactful).
  ImpactValue value = 1;
  // The scoring system to apply for evaluating multiple results
  ScoringSystem scoring = 2;
  // The weight of this query. Has to be > 0, otherwise counts as unset.
  int32 weight = 3;
  // Action is copied into the impact to correctly reconstruct the v7
  // ScoringSpec
  Action action = 4;
}

message QueryPackDocs {
  string desc = 1;
}

// Documentation for Mquery
message MqueryDocs {
  reserved 3;
  // Description of the query
  string desc = 1;
  // Optional. Audit instructions to verify the query results
  string audit = 2;
  // Optional. References to external sources, typical URLs
  repeated MqueryRef refs = 4;
  //  Optional. Remediation instructions for the query
  Remediation remediation = 5;
}

// Remediation is used to provide a fix for a check.
message Remediation {
  // items is a list of TypedDoc items that include the description of the
  // remediation for different platforms.
  repeated TypedDoc items = 1;
}

// A typed documentation for a remediation
message TypedDoc {
  // Identifier
  string id = 1;
  // Description
  string desc = 2;
  // Optional. Language of the description (e.g. markdown, english)
  string language = 3;
  // Optional. Creation date of the description
  int64 created = 20;
  // Optional. Modification date of the description
  int64 modified = 21;
  // Optional. Tags for the description
  map<string,string> tags = 22;
}

// Author is used to identify the author of a query
message Author {
  // Name of the author
  string name = 1;
  // Email of the author
  string email = 2;
}

// MqueryRef is used to reference external sources
message MqueryRef {
  // Title of the reference
  string title = 1;
  // URL of the reference
  string url = 2;
}

// **********  Resolution / Execution **************

/*
  The list of queries that an asset needs to execute
  May be identical amongst multiple packs
*/
message ExecutionJob {
  string checksum = 1;
  // map of all queries that should be executed, checksum => query
  map<string,ExecutionQuery> queries = 2;
  // map of all datapoints to their info
  map<string,DataQueryInfo> datapoints = 3;
}

message DataQueryInfo {
  string type = 1;
  repeated string notify = 2;
}

/*
  A query which is part of an ExecutionJob
  Reduced to the bare minimum for the execution
*/
message ExecutionQuery {
  string query = 1;
  string checksum = 2;
  // mapping from name => checksum, which is in the execution job
  map<string,string> properties = 3;
  // list of checksums that we collect as data points
  repeated string datapoints = 4;
  cnquery.llx.CodeBundle code = 5;
}

// **********       Query Hub        **************

service QueryHub {
  rpc SetBundle(Bundle) returns (Empty) {}
  rpc DeleteQueryPack(Mrn) returns (Empty) {}
  rpc ValidateBundle(Bundle) returns (Empty) {}
  rpc GetBundle(Mrn) returns (Bundle) {}
  rpc GetQueryPack(Mrn) returns (QueryPack) {}
  rpc GetFilters(Mrn) returns (Mqueries) {}
  rpc List(ListReq) returns (QueryPacks) {}
  rpc DefaultPacks(DefaultPacksReq) returns (URLs) {}
}

message Empty {

}

// MRNs are used to uniquely identify resources. They are globally unique.
message Mrn {
  string mrn = 1;
}

message Mqueries {
  repeated Mquery items = 1;
}

message ListReq {
  string owner_mrn = 1;
  string name = 2;
}

message DefaultPacksReq {
  string kind = 1;
  string platform = 2;
  string runtime = 3;
  string version = 4;
  repeated string family = 5;
}

message URLs {
  repeated string urls = 1;
}

// **********       Query Conductor        **************

service QueryConductor {
  rpc Assign(Assignment) returns (Empty) {}
  rpc Unassign(Assignment) returns (Empty) {}
  rpc SetProps(PropsReq) returns (Empty) {}
  rpc Resolve(ResolveReq) returns (ResolvedPack) {}
  rpc StoreResults(StoreResultsReq) returns (Empty) {}
  rpc GetReport(EntityDataRequest) returns (Report) {}
  rpc SynchronizeAssets(SynchronizeAssetsReq) returns (SynchronizeAssetsResp) {}
}

// Assign a number of packs to an asset. All of these are identified by MRN.
// Generally query packs are assigned (via MRN).
message Assignment {
  string asset_mrn = 1;
  repeated string pack_mrns = 2;
}

// PropsReq is used to set, change, or remove properties.
message PropsReq {
  string entity_mrn = 1;
  repeated Property props = 2;
}

// Resolve a given entity via its MRN. Typically used to resolve assets.
// Can also be used to resolve query packs.
message ResolveReq {
  string entity_mrn = 1;
  repeated Mquery asset_filters = 2;
}

// ResolvedPack is returned from a resolve request. It includes the execution
// job with all things that need to be run.
message ResolvedPack {
  ExecutionJob execution_job = 2;
  repeated Mquery filters = 4;
  string graph_execution_checksum = 7;
  string filters_checksum = 20;
}

// Update asset jobs forces all jobs for a given asset to get refreshed.
message UpdateAssetJobsReq {
  string asset_mrn = 1;
  repeated Mquery asset_filters = 2;
}

// Store results for a given asset
message StoreResultsReq {
  string asset_mrn = 1;
  map<string, cnquery.llx.Result> data = 3;
  map<string, cnquery.llx.ResourceRecording> resources = 4;
  // Determines if this is the last batch of results for the asset
  bool is_last_batch = 5;
}

// Retrieve data for a given set of entities which was previously stored
message EntityDataRequest {
  string entity_mrn = 1;
  string data_mrn = 2;
}

// The report of all the things collected for an entity (typically asset). The
// provided pack is used as the root to decide what data fields will be
// returned.
message Report {
  string pack_mrn = 1;
  string entity_mrn = 2;
  map<string, cnquery.llx.Result> data = 5;

  int64 created = 20;
  int64 modified = 21;

  string resolved_version = 33;
}

// Asset is a lean layer of information about an asset
message Asset {
  string mrn = 1;
  map<string, string> labels = 3;
  string name = 18;
  string trace_id = 19;
}

message ReportCollection {
  map<string, Asset> assets = 1;
  Bundle bundle = 2;
  map<string, Report> reports = 3;
  map<string, ErrorStatus> errors = 4;
  map<string, ResolvedPack> resolved = 5;
}

message ErrorStatus {
  // The status code.
  int32 code = 1;
  // A user-facing error message, which should be in English.
  string message = 2;
  // A list of messages that carry the error details.
  repeated google.protobuf.Any details = 3;
}

message AssignmentDelta {
  string mrn = 1;
  // protolint:disable ENUM_FIELD_NAMES_PREFIX
  // protolint:disable ENUM_FIELD_NAMES_ZERO_VALUE_END_WITH
  enum Action { 
    UNKNOWN = 0;
    ADD = 1;
    DELETE = 2;
  }
  Action action = 2;
}

message BundleMutationDelta {
  string owner_mrn = 1;
  map<string,AssignmentDelta> deltas = 2;
}

message SynchronizeAssetsReq {
  string space_mrn = 1;
  repeated cnquery.providers.v1.Asset list = 2;
}

message SynchronizeAssetsRespAssetDetail {
  string platform_mrn = 1;
  string asset_mrn = 2;
  string url = 3;
}

message SynchronizeAssetsResp {
  map<string, SynchronizeAssetsRespAssetDetail> details = 1;
}

message ImpactValue {
  int32 value = 1;
}
