// Copyright (c) Mondoo, Inc.
// SPDX-License-Identifier: BUSL-1.1

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: cnquery_explorer.proto

package explorer

import (
	llx "go.mondoo.com/cnquery/v11/llx"
	inventory "go.mondoo.com/cnquery/v11/providers-sdk/v1/inventory"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	anypb "google.golang.org/protobuf/types/known/anypb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// protolint:disable ENUM_FIELD_NAMES_PREFIX
// protolint:disable ENUM_FIELD_NAMES_ZERO_VALUE_END_WITH
type Action int32

const (
	Action_UNSPECIFIED  Action = 0
	Action_MODIFY       Action = 1
	Action_DEACTIVATE   Action = 2
	Action_ACTIVATE     Action = 3
	Action_IGNORE       Action = 4
	Action_OUT_OF_SCOPE Action = 5
)

// Enum value maps for Action.
var (
	Action_name = map[int32]string{
		0: "UNSPECIFIED",
		1: "MODIFY",
		2: "DEACTIVATE",
		3: "ACTIVATE",
		4: "IGNORE",
		5: "OUT_OF_SCOPE",
	}
	Action_value = map[string]int32{
		"UNSPECIFIED":  0,
		"MODIFY":       1,
		"DEACTIVATE":   2,
		"ACTIVATE":     3,
		"IGNORE":       4,
		"OUT_OF_SCOPE": 5,
	}
)

func (x Action) Enum() *Action {
	p := new(Action)
	*p = x
	return p
}

func (x Action) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Action) Descriptor() protoreflect.EnumDescriptor {
	return file_cnquery_explorer_proto_enumTypes[0].Descriptor()
}

func (Action) Type() protoreflect.EnumType {
	return &file_cnquery_explorer_proto_enumTypes[0]
}

func (x Action) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Action.Descriptor instead.
func (Action) EnumDescriptor() ([]byte, []int) {
	return file_cnquery_explorer_proto_rawDescGZIP(), []int{0}
}

// protolint:disable ENUM_FIELD_NAMES_PREFIX
// protolint:disable ENUM_FIELD_NAMES_ZERO_VALUE_END_WITH
type ScoringSystem int32

const (
	// Default value, should not be used
	ScoringSystem_SCORING_UNSPECIFIED ScoringSystem = 0
	// Weighted scoring mechanism which uses the weight defined at the query
	// level to calculate the score.
	ScoringSystem_WEIGHTED ScoringSystem = 1
	// Worst scoring mechanism which uses the worst score of all findings
	// to calculate the policy score.
	ScoringSystem_WORST ScoringSystem = 2
	// Average scoring mechanism which uses the average score of all findings
	// to calculate the policy score.
	ScoringSystem_AVERAGE ScoringSystem = 3
	// Treats the query as data only, no scoring is applied.
	ScoringSystem_DATA_ONLY ScoringSystem = 4
	// Ignore scoring mechanism which ignores the score of each query.
	ScoringSystem_IGNORE_SCORE ScoringSystem = 5
	// Experimental: BANDED scoring mechanism which creates a score based on
	// the 4 categories of criticality (critical, high, medium, low) and
	// positions scores so that:
	//  1. Any critical/high issues won't generate a high score (upper limit)
	//  2. Lower scoring categories can have an impact on the score
	//     (e.g. 1 crit + 200 medium failures will be lower than 1 crit only)
	//  3. A large collection of medium findings won't generate a critical score
	ScoringSystem_BANDED ScoringSystem = 6
	// Experimental: DECAYED scoring uses a scaled decay function to estimate a
	// score value. This means that a few critical findings will quickly reduce
	// the resulting score, but it won't just drop it to the lowest value.
	ScoringSystem_DECAYED  ScoringSystem = 7
	ScoringSystem_DISABLED ScoringSystem = 8
)

// Enum value maps for ScoringSystem.
var (
	ScoringSystem_name = map[int32]string{
		0: "SCORING_UNSPECIFIED",
		1: "WEIGHTED",
		2: "WORST",
		3: "AVERAGE",
		4: "DATA_ONLY",
		5: "IGNORE_SCORE",
		6: "BANDED",
		7: "DECAYED",
		8: "DISABLED",
	}
	ScoringSystem_value = map[string]int32{
		"SCORING_UNSPECIFIED": 0,
		"WEIGHTED":            1,
		"WORST":               2,
		"AVERAGE":             3,
		"DATA_ONLY":           4,
		"IGNORE_SCORE":        5,
		"BANDED":              6,
		"DECAYED":             7,
		"DISABLED":            8,
	}
)

func (x ScoringSystem) Enum() *ScoringSystem {
	p := new(ScoringSystem)
	*p = x
	return p
}

func (x ScoringSystem) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ScoringSystem) Descriptor() protoreflect.EnumDescriptor {
	return file_cnquery_explorer_proto_enumTypes[1].Descriptor()
}

func (ScoringSystem) Type() protoreflect.EnumType {
	return &file_cnquery_explorer_proto_enumTypes[1]
}

func (x ScoringSystem) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ScoringSystem.Descriptor instead.
func (ScoringSystem) EnumDescriptor() ([]byte, []int) {
	return file_cnquery_explorer_proto_rawDescGZIP(), []int{1}
}

// protolint:disable ENUM_FIELD_NAMES_PREFIX
// protolint:disable ENUM_FIELD_NAMES_ZERO_VALUE_END_WITH
type AssignmentDelta_Action int32

const (
	AssignmentDelta_UNKNOWN AssignmentDelta_Action = 0
	AssignmentDelta_ADD     AssignmentDelta_Action = 1
	AssignmentDelta_DELETE  AssignmentDelta_Action = 2
)

// Enum value maps for AssignmentDelta_Action.
var (
	AssignmentDelta_Action_name = map[int32]string{
		0: "UNKNOWN",
		1: "ADD",
		2: "DELETE",
	}
	AssignmentDelta_Action_value = map[string]int32{
		"UNKNOWN": 0,
		"ADD":     1,
		"DELETE":  2,
	}
)

func (x AssignmentDelta_Action) Enum() *AssignmentDelta_Action {
	p := new(AssignmentDelta_Action)
	*p = x
	return p
}

func (x AssignmentDelta_Action) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AssignmentDelta_Action) Descriptor() protoreflect.EnumDescriptor {
	return file_cnquery_explorer_proto_enumTypes[2].Descriptor()
}

func (AssignmentDelta_Action) Type() protoreflect.EnumType {
	return &file_cnquery_explorer_proto_enumTypes[2]
}

func (x AssignmentDelta_Action) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AssignmentDelta_Action.Descriptor instead.
func (AssignmentDelta_Action) EnumDescriptor() ([]byte, []int) {
	return file_cnquery_explorer_proto_rawDescGZIP(), []int{36, 0}
}

type Bundle struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	OwnerMrn      string                 `protobuf:"bytes,1,opt,name=owner_mrn,json=ownerMrn,proto3" json:"owner_mrn,omitempty"`
	Packs         []*QueryPack           `protobuf:"bytes,2,rep,name=packs,proto3" json:"packs,omitempty"`
	Queries       []*Mquery              `protobuf:"bytes,3,rep,name=queries,proto3" json:"queries,omitempty"`
	Props         []*Property            `protobuf:"bytes,4,rep,name=props,proto3" json:"props,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Bundle) Reset() {
	*x = Bundle{}
	mi := &file_cnquery_explorer_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Bundle) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Bundle) ProtoMessage() {}

func (x *Bundle) ProtoReflect() protoreflect.Message {
	mi := &file_cnquery_explorer_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Bundle.ProtoReflect.Descriptor instead.
func (*Bundle) Descriptor() ([]byte, []int) {
	return file_cnquery_explorer_proto_rawDescGZIP(), []int{0}
}

func (x *Bundle) GetOwnerMrn() string {
	if x != nil {
		return x.OwnerMrn
	}
	return ""
}

func (x *Bundle) GetPacks() []*QueryPack {
	if x != nil {
		return x.Packs
	}
	return nil
}

func (x *Bundle) GetQueries() []*Mquery {
	if x != nil {
		return x.Queries
	}
	return nil
}

func (x *Bundle) GetProps() []*Property {
	if x != nil {
		return x.Props
	}
	return nil
}

type QueryGroup struct {
	state   protoimpl.MessageState `protogen:"open.v1"`
	Queries []*Mquery              `protobuf:"bytes,3,rep,name=queries,proto3" json:"queries,omitempty"`
	// filter for the assets this applies to
	Filters *Filters `protobuf:"bytes,20,opt,name=filters,proto3" json:"filters,omitempty"`
	// metadata
	Title         string `protobuf:"bytes,24,opt,name=title,proto3" json:"title,omitempty"`
	Created       int64  `protobuf:"varint,32,opt,name=created,proto3" json:"created,omitempty"`
	Modified      int64  `protobuf:"varint,33,opt,name=modified,proto3" json:"modified,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *QueryGroup) Reset() {
	*x = QueryGroup{}
	mi := &file_cnquery_explorer_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QueryGroup) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryGroup) ProtoMessage() {}

func (x *QueryGroup) ProtoReflect() protoreflect.Message {
	mi := &file_cnquery_explorer_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryGroup.ProtoReflect.Descriptor instead.
func (*QueryGroup) Descriptor() ([]byte, []int) {
	return file_cnquery_explorer_proto_rawDescGZIP(), []int{1}
}

func (x *QueryGroup) GetQueries() []*Mquery {
	if x != nil {
		return x.Queries
	}
	return nil
}

func (x *QueryGroup) GetFilters() *Filters {
	if x != nil {
		return x.Filters
	}
	return nil
}

func (x *QueryGroup) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *QueryGroup) GetCreated() int64 {
	if x != nil {
		return x.Created
	}
	return 0
}

func (x *QueryGroup) GetModified() int64 {
	if x != nil {
		return x.Modified
	}
	return 0
}

type QueryPack struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// user-defined UID, which is used to generate the MRN
	Uid string `protobuf:"bytes,36,opt,name=uid,proto3" json:"uid,omitempty"`
	// user-defined, only used during query ingest in case they have no context
	Context string `protobuf:"bytes,8,opt,name=context,proto3" json:"context,omitempty"`
	// MRN to universally identify this query.
	// UIDs are local to QueryPacks, MRNs are global
	Mrn             string            `protobuf:"bytes,1,opt,name=mrn,proto3" json:"mrn,omitempty"`
	Name            string            `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Version         string            `protobuf:"bytes,3,opt,name=version,proto3" json:"version,omitempty"`
	OwnerMrn        string            `protobuf:"bytes,4,opt,name=owner_mrn,json=ownerMrn,proto3" json:"owner_mrn,omitempty"` // auto-generated
	Queries         []*Mquery         `protobuf:"bytes,6,rep,name=queries,proto3" json:"queries,omitempty"`
	Groups          []*QueryGroup     `protobuf:"bytes,11,rep,name=groups,proto3" json:"groups,omitempty"`
	Props           []*Property       `protobuf:"bytes,35,rep,name=props,proto3" json:"props,omitempty"`
	ComputedFilters *Filters          `protobuf:"bytes,47,opt,name=computed_filters,json=computedFilters,proto3" json:"computed_filters,omitempty"` // auto-generated
	Filters         *Filters          `protobuf:"bytes,48,opt,name=filters,proto3" json:"filters,omitempty"`
	License         string            `protobuf:"bytes,21,opt,name=license,proto3" json:"license,omitempty"`
	Docs            *QueryPackDocs    `protobuf:"bytes,22,opt,name=docs,proto3" json:"docs,omitempty"`
	Summary         string            `protobuf:"bytes,46,opt,name=summary,proto3" json:"summary,omitempty"`
	Authors         []*Author         `protobuf:"bytes,30,rep,name=authors,proto3" json:"authors,omitempty"`
	Created         int64             `protobuf:"varint,32,opt,name=created,proto3" json:"created,omitempty"`
	Modified        int64             `protobuf:"varint,33,opt,name=modified,proto3" json:"modified,omitempty"`
	Tags            map[string]string `protobuf:"bytes,34,rep,name=tags,proto3" json:"tags,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	// internal fields
	LocalContentChecksum   string `protobuf:"bytes,23,opt,name=local_content_checksum,json=localContentChecksum,proto3" json:"local_content_checksum,omitempty"`
	LocalExecutionChecksum string `protobuf:"bytes,24,opt,name=local_execution_checksum,json=localExecutionChecksum,proto3" json:"local_execution_checksum,omitempty"`
	unknownFields          protoimpl.UnknownFields
	sizeCache              protoimpl.SizeCache
}

func (x *QueryPack) Reset() {
	*x = QueryPack{}
	mi := &file_cnquery_explorer_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QueryPack) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryPack) ProtoMessage() {}

func (x *QueryPack) ProtoReflect() protoreflect.Message {
	mi := &file_cnquery_explorer_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryPack.ProtoReflect.Descriptor instead.
func (*QueryPack) Descriptor() ([]byte, []int) {
	return file_cnquery_explorer_proto_rawDescGZIP(), []int{2}
}

func (x *QueryPack) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *QueryPack) GetContext() string {
	if x != nil {
		return x.Context
	}
	return ""
}

func (x *QueryPack) GetMrn() string {
	if x != nil {
		return x.Mrn
	}
	return ""
}

func (x *QueryPack) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *QueryPack) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *QueryPack) GetOwnerMrn() string {
	if x != nil {
		return x.OwnerMrn
	}
	return ""
}

func (x *QueryPack) GetQueries() []*Mquery {
	if x != nil {
		return x.Queries
	}
	return nil
}

func (x *QueryPack) GetGroups() []*QueryGroup {
	if x != nil {
		return x.Groups
	}
	return nil
}

func (x *QueryPack) GetProps() []*Property {
	if x != nil {
		return x.Props
	}
	return nil
}

func (x *QueryPack) GetComputedFilters() *Filters {
	if x != nil {
		return x.ComputedFilters
	}
	return nil
}

func (x *QueryPack) GetFilters() *Filters {
	if x != nil {
		return x.Filters
	}
	return nil
}

func (x *QueryPack) GetLicense() string {
	if x != nil {
		return x.License
	}
	return ""
}

func (x *QueryPack) GetDocs() *QueryPackDocs {
	if x != nil {
		return x.Docs
	}
	return nil
}

func (x *QueryPack) GetSummary() string {
	if x != nil {
		return x.Summary
	}
	return ""
}

func (x *QueryPack) GetAuthors() []*Author {
	if x != nil {
		return x.Authors
	}
	return nil
}

func (x *QueryPack) GetCreated() int64 {
	if x != nil {
		return x.Created
	}
	return 0
}

func (x *QueryPack) GetModified() int64 {
	if x != nil {
		return x.Modified
	}
	return 0
}

func (x *QueryPack) GetTags() map[string]string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *QueryPack) GetLocalContentChecksum() string {
	if x != nil {
		return x.LocalContentChecksum
	}
	return ""
}

func (x *QueryPack) GetLocalExecutionChecksum() string {
	if x != nil {
		return x.LocalExecutionChecksum
	}
	return ""
}

type ObjectRef struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Mrn           string                 `protobuf:"bytes,1,opt,name=mrn,proto3" json:"mrn,omitempty"`
	Uid           string                 `protobuf:"bytes,2,opt,name=uid,proto3" json:"uid,omitempty"`
	Tags          map[string]string      `protobuf:"bytes,3,rep,name=tags,proto3" json:"tags,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ObjectRef) Reset() {
	*x = ObjectRef{}
	mi := &file_cnquery_explorer_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ObjectRef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ObjectRef) ProtoMessage() {}

func (x *ObjectRef) ProtoReflect() protoreflect.Message {
	mi := &file_cnquery_explorer_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ObjectRef.ProtoReflect.Descriptor instead.
func (*ObjectRef) Descriptor() ([]byte, []int) {
	return file_cnquery_explorer_proto_rawDescGZIP(), []int{3}
}

func (x *ObjectRef) GetMrn() string {
	if x != nil {
		return x.Mrn
	}
	return ""
}

func (x *ObjectRef) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *ObjectRef) GetTags() map[string]string {
	if x != nil {
		return x.Tags
	}
	return nil
}

// User-defined and internal filters. Users may use:
// 1. filters: asset.name == /sth/
// 2. filters:
//   - mql: asset.name == /sth/
//
// Internally we use:
//  3. filters:
//     items:
//     <checksum>: { ..mquery.. }
type Filters struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Items         map[string]*Mquery     `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Filters) Reset() {
	*x = Filters{}
	mi := &file_cnquery_explorer_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Filters) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Filters) ProtoMessage() {}

func (x *Filters) ProtoReflect() protoreflect.Message {
	mi := &file_cnquery_explorer_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Filters.ProtoReflect.Descriptor instead.
func (*Filters) Descriptor() ([]byte, []int) {
	return file_cnquery_explorer_proto_rawDescGZIP(), []int{4}
}

func (x *Filters) GetItems() map[string]*Mquery {
	if x != nil {
		return x.Items
	}
	return nil
}

type QueryPacks struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Items         []*QueryPack           `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *QueryPacks) Reset() {
	*x = QueryPacks{}
	mi := &file_cnquery_explorer_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QueryPacks) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryPacks) ProtoMessage() {}

func (x *QueryPacks) ProtoReflect() protoreflect.Message {
	mi := &file_cnquery_explorer_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryPacks.ProtoReflect.Descriptor instead.
func (*QueryPacks) Descriptor() ([]byte, []int) {
	return file_cnquery_explorer_proto_rawDescGZIP(), []int{5}
}

func (x *QueryPacks) GetItems() []*QueryPack {
	if x != nil {
		return x.Items
	}
	return nil
}

type Docs struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Desc          string                 `protobuf:"bytes,1,opt,name=desc,proto3" json:"desc,omitempty"`
	Refs          []*MqueryRef           `protobuf:"bytes,2,rep,name=refs,proto3" json:"refs,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Docs) Reset() {
	*x = Docs{}
	mi := &file_cnquery_explorer_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Docs) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Docs) ProtoMessage() {}

func (x *Docs) ProtoReflect() protoreflect.Message {
	mi := &file_cnquery_explorer_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Docs.ProtoReflect.Descriptor instead.
func (*Docs) Descriptor() ([]byte, []int) {
	return file_cnquery_explorer_proto_rawDescGZIP(), []int{6}
}

func (x *Docs) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *Docs) GetRefs() []*MqueryRef {
	if x != nil {
		return x.Refs
	}
	return nil
}

// Properties allow users to configure queries and bundles in pre-defined ways.
// They return one value only, have a type, and may specify which query they
// target, while used in querypacks (or other groupings like policies).
// They may contain additional metadata to provide more information.
//
// Note: At the time of writing properties do not pull from other properties.
// They may do that later.
type Property struct {
	state    protoimpl.MessageState `protogen:"open.v1"`
	Mql      string                 `protobuf:"bytes,1,opt,name=mql,proto3" json:"mql,omitempty"`
	CodeId   string                 `protobuf:"bytes,2,opt,name=code_id,json=codeId,proto3" json:"code_id,omitempty"`
	Checksum string                 `protobuf:"bytes,3,opt,name=checksum,proto3" json:"checksum,omitempty"`
	Mrn      string                 `protobuf:"bytes,4,opt,name=mrn,proto3" json:"mrn,omitempty"`
	// UID is only needed on Mquery upload, when the MRN is computed.
	// It is not be persisted.
	Uid     string `protobuf:"bytes,5,opt,name=uid,proto3" json:"uid,omitempty"`
	Type    string `protobuf:"bytes,6,opt,name=type,proto3" json:"type,omitempty"`
	Context string `protobuf:"bytes,7,opt,name=context,proto3" json:"context,omitempty"`
	// protolint:disable REPEATED_FIELD_NAMES_PLURALIZED
	For           []*ObjectRef `protobuf:"bytes,8,rep,name=for,proto3" json:"for,omitempty"`
	Title         string       `protobuf:"bytes,20,opt,name=title,proto3" json:"title,omitempty"`
	Desc          string       `protobuf:"bytes,35,opt,name=desc,proto3" json:"desc,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Property) Reset() {
	*x = Property{}
	mi := &file_cnquery_explorer_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Property) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Property) ProtoMessage() {}

func (x *Property) ProtoReflect() protoreflect.Message {
	mi := &file_cnquery_explorer_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Property.ProtoReflect.Descriptor instead.
func (*Property) Descriptor() ([]byte, []int) {
	return file_cnquery_explorer_proto_rawDescGZIP(), []int{7}
}

func (x *Property) GetMql() string {
	if x != nil {
		return x.Mql
	}
	return ""
}

func (x *Property) GetCodeId() string {
	if x != nil {
		return x.CodeId
	}
	return ""
}

func (x *Property) GetChecksum() string {
	if x != nil {
		return x.Checksum
	}
	return ""
}

func (x *Property) GetMrn() string {
	if x != nil {
		return x.Mrn
	}
	return ""
}

func (x *Property) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *Property) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *Property) GetContext() string {
	if x != nil {
		return x.Context
	}
	return ""
}

func (x *Property) GetFor() []*ObjectRef {
	if x != nil {
		return x.For
	}
	return nil
}

func (x *Property) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *Property) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

// Mquery represents the MQL and metadata that form a query
type Mquery struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// FIXME: DEPRECATED, remove in v12.0 vv
	// This is replaced by the field "mql"
	Query string `protobuf:"bytes,40,opt,name=query,proto3" json:"query,omitempty"`
	// This is moved into docs.refs
	Refs     []*MqueryRef `protobuf:"bytes,22,rep,name=refs,proto3" json:"refs,omitempty"` // ^^
	Mql      string       `protobuf:"bytes,1,opt,name=mql,proto3" json:"mql,omitempty"`
	CodeId   string       `protobuf:"bytes,2,opt,name=code_id,json=codeId,proto3" json:"code_id,omitempty"`
	Checksum string       `protobuf:"bytes,3,opt,name=checksum,proto3" json:"checksum,omitempty"`
	Mrn      string       `protobuf:"bytes,4,opt,name=mrn,proto3" json:"mrn,omitempty"`
	// UID is only needed on Mquery upload, when the MRN is computed.
	// It is not be persisted.
	Uid     string      `protobuf:"bytes,5,opt,name=uid,proto3" json:"uid,omitempty"`
	Type    string      `protobuf:"bytes,6,opt,name=type,proto3" json:"type,omitempty"`
	Context string      `protobuf:"bytes,7,opt,name=context,proto3" json:"context,omitempty"`
	Title   string      `protobuf:"bytes,20,opt,name=title,proto3" json:"title,omitempty"`
	Docs    *MqueryDocs `protobuf:"bytes,21,opt,name=docs,proto3" json:"docs,omitempty"`
	// Desc is only used if docs.desc is not defined. It is not persisted.
	Desc     string            `protobuf:"bytes,35,opt,name=desc,proto3" json:"desc,omitempty"`
	Impact   *Impact           `protobuf:"bytes,23,opt,name=impact,proto3" json:"impact,omitempty"`
	Tags     map[string]string `protobuf:"bytes,34,rep,name=tags,proto3" json:"tags,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	Filters  *Filters          `protobuf:"bytes,37,opt,name=filters,proto3" json:"filters,omitempty"`
	Props    []*Property       `protobuf:"bytes,38,rep,name=props,proto3" json:"props,omitempty"`
	Variants []*ObjectRef      `protobuf:"bytes,39,rep,name=variants,proto3" json:"variants,omitempty"`
	// Action is used for all query overrides (eg: in packs, policies, APIs etc)
	Action        Action `protobuf:"varint,41,opt,name=action,proto3,enum=cnquery.explorer.Action" json:"action,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Mquery) Reset() {
	*x = Mquery{}
	mi := &file_cnquery_explorer_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Mquery) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Mquery) ProtoMessage() {}

func (x *Mquery) ProtoReflect() protoreflect.Message {
	mi := &file_cnquery_explorer_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Mquery.ProtoReflect.Descriptor instead.
func (*Mquery) Descriptor() ([]byte, []int) {
	return file_cnquery_explorer_proto_rawDescGZIP(), []int{8}
}

func (x *Mquery) GetQuery() string {
	if x != nil {
		return x.Query
	}
	return ""
}

func (x *Mquery) GetRefs() []*MqueryRef {
	if x != nil {
		return x.Refs
	}
	return nil
}

func (x *Mquery) GetMql() string {
	if x != nil {
		return x.Mql
	}
	return ""
}

func (x *Mquery) GetCodeId() string {
	if x != nil {
		return x.CodeId
	}
	return ""
}

func (x *Mquery) GetChecksum() string {
	if x != nil {
		return x.Checksum
	}
	return ""
}

func (x *Mquery) GetMrn() string {
	if x != nil {
		return x.Mrn
	}
	return ""
}

func (x *Mquery) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *Mquery) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *Mquery) GetContext() string {
	if x != nil {
		return x.Context
	}
	return ""
}

func (x *Mquery) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *Mquery) GetDocs() *MqueryDocs {
	if x != nil {
		return x.Docs
	}
	return nil
}

func (x *Mquery) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *Mquery) GetImpact() *Impact {
	if x != nil {
		return x.Impact
	}
	return nil
}

func (x *Mquery) GetTags() map[string]string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *Mquery) GetFilters() *Filters {
	if x != nil {
		return x.Filters
	}
	return nil
}

func (x *Mquery) GetProps() []*Property {
	if x != nil {
		return x.Props
	}
	return nil
}

func (x *Mquery) GetVariants() []*ObjectRef {
	if x != nil {
		return x.Variants
	}
	return nil
}

func (x *Mquery) GetAction() Action {
	if x != nil {
		return x.Action
	}
	return Action_UNSPECIFIED
}

// Impact explains how important certain queries are. They are especially useful
// in weighted testing where results need to be prioritized. They can also
// serve as a priority list for data that is collected.
type Impact struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Value is between 0 to 100 (most impactful).
	Value *ImpactValue `protobuf:"bytes,1,opt,name=value,proto3" json:"value,omitempty"`
	// The scoring system to apply for evaluating multiple results
	Scoring ScoringSystem `protobuf:"varint,2,opt,name=scoring,proto3,enum=cnquery.explorer.ScoringSystem" json:"scoring,omitempty"`
	// The weight of this query. Has to be > 0, otherwise counts as unset.
	Weight int32 `protobuf:"varint,3,opt,name=weight,proto3" json:"weight,omitempty"`
	// Action is copied into the impact to correctly reconstruct the v7
	// ScoringSpec
	Action        Action `protobuf:"varint,4,opt,name=action,proto3,enum=cnquery.explorer.Action" json:"action,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Impact) Reset() {
	*x = Impact{}
	mi := &file_cnquery_explorer_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Impact) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Impact) ProtoMessage() {}

func (x *Impact) ProtoReflect() protoreflect.Message {
	mi := &file_cnquery_explorer_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Impact.ProtoReflect.Descriptor instead.
func (*Impact) Descriptor() ([]byte, []int) {
	return file_cnquery_explorer_proto_rawDescGZIP(), []int{9}
}

func (x *Impact) GetValue() *ImpactValue {
	if x != nil {
		return x.Value
	}
	return nil
}

func (x *Impact) GetScoring() ScoringSystem {
	if x != nil {
		return x.Scoring
	}
	return ScoringSystem_SCORING_UNSPECIFIED
}

func (x *Impact) GetWeight() int32 {
	if x != nil {
		return x.Weight
	}
	return 0
}

func (x *Impact) GetAction() Action {
	if x != nil {
		return x.Action
	}
	return Action_UNSPECIFIED
}

type QueryPackDocs struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Desc          string                 `protobuf:"bytes,1,opt,name=desc,proto3" json:"desc,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *QueryPackDocs) Reset() {
	*x = QueryPackDocs{}
	mi := &file_cnquery_explorer_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QueryPackDocs) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryPackDocs) ProtoMessage() {}

func (x *QueryPackDocs) ProtoReflect() protoreflect.Message {
	mi := &file_cnquery_explorer_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryPackDocs.ProtoReflect.Descriptor instead.
func (*QueryPackDocs) Descriptor() ([]byte, []int) {
	return file_cnquery_explorer_proto_rawDescGZIP(), []int{10}
}

func (x *QueryPackDocs) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

// Documentation for Mquery
type MqueryDocs struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Description of the query
	Desc string `protobuf:"bytes,1,opt,name=desc,proto3" json:"desc,omitempty"`
	// Optional. Audit instructions to verify the query results
	Audit string `protobuf:"bytes,2,opt,name=audit,proto3" json:"audit,omitempty"`
	// Optional. References to external sources, typical URLs
	Refs []*MqueryRef `protobuf:"bytes,4,rep,name=refs,proto3" json:"refs,omitempty"`
	// Optional. Remediation instructions for the query
	Remediation   *Remediation `protobuf:"bytes,5,opt,name=remediation,proto3" json:"remediation,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MqueryDocs) Reset() {
	*x = MqueryDocs{}
	mi := &file_cnquery_explorer_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MqueryDocs) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MqueryDocs) ProtoMessage() {}

func (x *MqueryDocs) ProtoReflect() protoreflect.Message {
	mi := &file_cnquery_explorer_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MqueryDocs.ProtoReflect.Descriptor instead.
func (*MqueryDocs) Descriptor() ([]byte, []int) {
	return file_cnquery_explorer_proto_rawDescGZIP(), []int{11}
}

func (x *MqueryDocs) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *MqueryDocs) GetAudit() string {
	if x != nil {
		return x.Audit
	}
	return ""
}

func (x *MqueryDocs) GetRefs() []*MqueryRef {
	if x != nil {
		return x.Refs
	}
	return nil
}

func (x *MqueryDocs) GetRemediation() *Remediation {
	if x != nil {
		return x.Remediation
	}
	return nil
}

// Remediation is used to provide a fix for a check.
type Remediation struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// items is a list of TypedDoc items that include the description of the
	// remediation for different platforms.
	Items         []*TypedDoc `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Remediation) Reset() {
	*x = Remediation{}
	mi := &file_cnquery_explorer_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Remediation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Remediation) ProtoMessage() {}

func (x *Remediation) ProtoReflect() protoreflect.Message {
	mi := &file_cnquery_explorer_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Remediation.ProtoReflect.Descriptor instead.
func (*Remediation) Descriptor() ([]byte, []int) {
	return file_cnquery_explorer_proto_rawDescGZIP(), []int{12}
}

func (x *Remediation) GetItems() []*TypedDoc {
	if x != nil {
		return x.Items
	}
	return nil
}

// A typed documentation for a remediation
type TypedDoc struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Identifier
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// Description
	Desc string `protobuf:"bytes,2,opt,name=desc,proto3" json:"desc,omitempty"`
	// Optional. Language of the description (e.g. markdown, english)
	Language string `protobuf:"bytes,3,opt,name=language,proto3" json:"language,omitempty"`
	// Optional. Creation date of the description
	Created int64 `protobuf:"varint,20,opt,name=created,proto3" json:"created,omitempty"`
	// Optional. Modification date of the description
	Modified int64 `protobuf:"varint,21,opt,name=modified,proto3" json:"modified,omitempty"`
	// Optional. Tags for the description
	Tags          map[string]string `protobuf:"bytes,22,rep,name=tags,proto3" json:"tags,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TypedDoc) Reset() {
	*x = TypedDoc{}
	mi := &file_cnquery_explorer_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TypedDoc) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TypedDoc) ProtoMessage() {}

func (x *TypedDoc) ProtoReflect() protoreflect.Message {
	mi := &file_cnquery_explorer_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TypedDoc.ProtoReflect.Descriptor instead.
func (*TypedDoc) Descriptor() ([]byte, []int) {
	return file_cnquery_explorer_proto_rawDescGZIP(), []int{13}
}

func (x *TypedDoc) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *TypedDoc) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *TypedDoc) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *TypedDoc) GetCreated() int64 {
	if x != nil {
		return x.Created
	}
	return 0
}

func (x *TypedDoc) GetModified() int64 {
	if x != nil {
		return x.Modified
	}
	return 0
}

func (x *TypedDoc) GetTags() map[string]string {
	if x != nil {
		return x.Tags
	}
	return nil
}

// Author is used to identify the author of a query
type Author struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Name of the author
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Email of the author
	Email         string `protobuf:"bytes,2,opt,name=email,proto3" json:"email,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Author) Reset() {
	*x = Author{}
	mi := &file_cnquery_explorer_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Author) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Author) ProtoMessage() {}

func (x *Author) ProtoReflect() protoreflect.Message {
	mi := &file_cnquery_explorer_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Author.ProtoReflect.Descriptor instead.
func (*Author) Descriptor() ([]byte, []int) {
	return file_cnquery_explorer_proto_rawDescGZIP(), []int{14}
}

func (x *Author) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Author) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

// MqueryRef is used to reference external sources
type MqueryRef struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Title of the reference
	Title string `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	// URL of the reference
	Url           string `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MqueryRef) Reset() {
	*x = MqueryRef{}
	mi := &file_cnquery_explorer_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MqueryRef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MqueryRef) ProtoMessage() {}

func (x *MqueryRef) ProtoReflect() protoreflect.Message {
	mi := &file_cnquery_explorer_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MqueryRef.ProtoReflect.Descriptor instead.
func (*MqueryRef) Descriptor() ([]byte, []int) {
	return file_cnquery_explorer_proto_rawDescGZIP(), []int{15}
}

func (x *MqueryRef) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *MqueryRef) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

// The list of queries that an asset needs to execute
// May be identical amongst multiple packs
type ExecutionJob struct {
	state    protoimpl.MessageState `protogen:"open.v1"`
	Checksum string                 `protobuf:"bytes,1,opt,name=checksum,proto3" json:"checksum,omitempty"`
	// map of all queries that should be executed, checksum => query
	Queries map[string]*ExecutionQuery `protobuf:"bytes,2,rep,name=queries,proto3" json:"queries,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	// map of all datapoints to their info
	Datapoints    map[string]*DataQueryInfo `protobuf:"bytes,3,rep,name=datapoints,proto3" json:"datapoints,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ExecutionJob) Reset() {
	*x = ExecutionJob{}
	mi := &file_cnquery_explorer_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExecutionJob) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExecutionJob) ProtoMessage() {}

func (x *ExecutionJob) ProtoReflect() protoreflect.Message {
	mi := &file_cnquery_explorer_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExecutionJob.ProtoReflect.Descriptor instead.
func (*ExecutionJob) Descriptor() ([]byte, []int) {
	return file_cnquery_explorer_proto_rawDescGZIP(), []int{16}
}

func (x *ExecutionJob) GetChecksum() string {
	if x != nil {
		return x.Checksum
	}
	return ""
}

func (x *ExecutionJob) GetQueries() map[string]*ExecutionQuery {
	if x != nil {
		return x.Queries
	}
	return nil
}

func (x *ExecutionJob) GetDatapoints() map[string]*DataQueryInfo {
	if x != nil {
		return x.Datapoints
	}
	return nil
}

type DataQueryInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Type          string                 `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty"`
	Notify        []string               `protobuf:"bytes,2,rep,name=notify,proto3" json:"notify,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DataQueryInfo) Reset() {
	*x = DataQueryInfo{}
	mi := &file_cnquery_explorer_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DataQueryInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DataQueryInfo) ProtoMessage() {}

func (x *DataQueryInfo) ProtoReflect() protoreflect.Message {
	mi := &file_cnquery_explorer_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DataQueryInfo.ProtoReflect.Descriptor instead.
func (*DataQueryInfo) Descriptor() ([]byte, []int) {
	return file_cnquery_explorer_proto_rawDescGZIP(), []int{17}
}

func (x *DataQueryInfo) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *DataQueryInfo) GetNotify() []string {
	if x != nil {
		return x.Notify
	}
	return nil
}

// A query which is part of an ExecutionJob
// Reduced to the bare minimum for the execution
type ExecutionQuery struct {
	state    protoimpl.MessageState `protogen:"open.v1"`
	Query    string                 `protobuf:"bytes,1,opt,name=query,proto3" json:"query,omitempty"`
	Checksum string                 `protobuf:"bytes,2,opt,name=checksum,proto3" json:"checksum,omitempty"`
	// mapping from name => checksum, which is in the execution job
	Properties map[string]string `protobuf:"bytes,3,rep,name=properties,proto3" json:"properties,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	// list of checksums that we collect as data points
	Datapoints    []string        `protobuf:"bytes,4,rep,name=datapoints,proto3" json:"datapoints,omitempty"`
	Code          *llx.CodeBundle `protobuf:"bytes,5,opt,name=code,proto3" json:"code,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ExecutionQuery) Reset() {
	*x = ExecutionQuery{}
	mi := &file_cnquery_explorer_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExecutionQuery) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExecutionQuery) ProtoMessage() {}

func (x *ExecutionQuery) ProtoReflect() protoreflect.Message {
	mi := &file_cnquery_explorer_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExecutionQuery.ProtoReflect.Descriptor instead.
func (*ExecutionQuery) Descriptor() ([]byte, []int) {
	return file_cnquery_explorer_proto_rawDescGZIP(), []int{18}
}

func (x *ExecutionQuery) GetQuery() string {
	if x != nil {
		return x.Query
	}
	return ""
}

func (x *ExecutionQuery) GetChecksum() string {
	if x != nil {
		return x.Checksum
	}
	return ""
}

func (x *ExecutionQuery) GetProperties() map[string]string {
	if x != nil {
		return x.Properties
	}
	return nil
}

func (x *ExecutionQuery) GetDatapoints() []string {
	if x != nil {
		return x.Datapoints
	}
	return nil
}

func (x *ExecutionQuery) GetCode() *llx.CodeBundle {
	if x != nil {
		return x.Code
	}
	return nil
}

type Empty struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Empty) Reset() {
	*x = Empty{}
	mi := &file_cnquery_explorer_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Empty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Empty) ProtoMessage() {}

func (x *Empty) ProtoReflect() protoreflect.Message {
	mi := &file_cnquery_explorer_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Empty.ProtoReflect.Descriptor instead.
func (*Empty) Descriptor() ([]byte, []int) {
	return file_cnquery_explorer_proto_rawDescGZIP(), []int{19}
}

// MRNs are used to uniquely identify resources. They are globally unique.
type Mrn struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Mrn           string                 `protobuf:"bytes,1,opt,name=mrn,proto3" json:"mrn,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Mrn) Reset() {
	*x = Mrn{}
	mi := &file_cnquery_explorer_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Mrn) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Mrn) ProtoMessage() {}

func (x *Mrn) ProtoReflect() protoreflect.Message {
	mi := &file_cnquery_explorer_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Mrn.ProtoReflect.Descriptor instead.
func (*Mrn) Descriptor() ([]byte, []int) {
	return file_cnquery_explorer_proto_rawDescGZIP(), []int{20}
}

func (x *Mrn) GetMrn() string {
	if x != nil {
		return x.Mrn
	}
	return ""
}

type Mqueries struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Items         []*Mquery              `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Mqueries) Reset() {
	*x = Mqueries{}
	mi := &file_cnquery_explorer_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Mqueries) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Mqueries) ProtoMessage() {}

func (x *Mqueries) ProtoReflect() protoreflect.Message {
	mi := &file_cnquery_explorer_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Mqueries.ProtoReflect.Descriptor instead.
func (*Mqueries) Descriptor() ([]byte, []int) {
	return file_cnquery_explorer_proto_rawDescGZIP(), []int{21}
}

func (x *Mqueries) GetItems() []*Mquery {
	if x != nil {
		return x.Items
	}
	return nil
}

type ListReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	OwnerMrn      string                 `protobuf:"bytes,1,opt,name=owner_mrn,json=ownerMrn,proto3" json:"owner_mrn,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListReq) Reset() {
	*x = ListReq{}
	mi := &file_cnquery_explorer_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListReq) ProtoMessage() {}

func (x *ListReq) ProtoReflect() protoreflect.Message {
	mi := &file_cnquery_explorer_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListReq.ProtoReflect.Descriptor instead.
func (*ListReq) Descriptor() ([]byte, []int) {
	return file_cnquery_explorer_proto_rawDescGZIP(), []int{22}
}

func (x *ListReq) GetOwnerMrn() string {
	if x != nil {
		return x.OwnerMrn
	}
	return ""
}

func (x *ListReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type DefaultPacksReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Kind          string                 `protobuf:"bytes,1,opt,name=kind,proto3" json:"kind,omitempty"`
	Platform      string                 `protobuf:"bytes,2,opt,name=platform,proto3" json:"platform,omitempty"`
	Runtime       string                 `protobuf:"bytes,3,opt,name=runtime,proto3" json:"runtime,omitempty"`
	Version       string                 `protobuf:"bytes,4,opt,name=version,proto3" json:"version,omitempty"`
	Family        []string               `protobuf:"bytes,5,rep,name=family,proto3" json:"family,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DefaultPacksReq) Reset() {
	*x = DefaultPacksReq{}
	mi := &file_cnquery_explorer_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DefaultPacksReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DefaultPacksReq) ProtoMessage() {}

func (x *DefaultPacksReq) ProtoReflect() protoreflect.Message {
	mi := &file_cnquery_explorer_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DefaultPacksReq.ProtoReflect.Descriptor instead.
func (*DefaultPacksReq) Descriptor() ([]byte, []int) {
	return file_cnquery_explorer_proto_rawDescGZIP(), []int{23}
}

func (x *DefaultPacksReq) GetKind() string {
	if x != nil {
		return x.Kind
	}
	return ""
}

func (x *DefaultPacksReq) GetPlatform() string {
	if x != nil {
		return x.Platform
	}
	return ""
}

func (x *DefaultPacksReq) GetRuntime() string {
	if x != nil {
		return x.Runtime
	}
	return ""
}

func (x *DefaultPacksReq) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *DefaultPacksReq) GetFamily() []string {
	if x != nil {
		return x.Family
	}
	return nil
}

type URLs struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Urls          []string               `protobuf:"bytes,1,rep,name=urls,proto3" json:"urls,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *URLs) Reset() {
	*x = URLs{}
	mi := &file_cnquery_explorer_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *URLs) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*URLs) ProtoMessage() {}

func (x *URLs) ProtoReflect() protoreflect.Message {
	mi := &file_cnquery_explorer_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use URLs.ProtoReflect.Descriptor instead.
func (*URLs) Descriptor() ([]byte, []int) {
	return file_cnquery_explorer_proto_rawDescGZIP(), []int{24}
}

func (x *URLs) GetUrls() []string {
	if x != nil {
		return x.Urls
	}
	return nil
}

// Assign a number of packs to an asset. All of these are identified by MRN.
// Generally query packs are assigned (via MRN).
type Assignment struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AssetMrn      string                 `protobuf:"bytes,1,opt,name=asset_mrn,json=assetMrn,proto3" json:"asset_mrn,omitempty"`
	PackMrns      []string               `protobuf:"bytes,2,rep,name=pack_mrns,json=packMrns,proto3" json:"pack_mrns,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Assignment) Reset() {
	*x = Assignment{}
	mi := &file_cnquery_explorer_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Assignment) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Assignment) ProtoMessage() {}

func (x *Assignment) ProtoReflect() protoreflect.Message {
	mi := &file_cnquery_explorer_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Assignment.ProtoReflect.Descriptor instead.
func (*Assignment) Descriptor() ([]byte, []int) {
	return file_cnquery_explorer_proto_rawDescGZIP(), []int{25}
}

func (x *Assignment) GetAssetMrn() string {
	if x != nil {
		return x.AssetMrn
	}
	return ""
}

func (x *Assignment) GetPackMrns() []string {
	if x != nil {
		return x.PackMrns
	}
	return nil
}

// PropsReq is used to set, change, or remove properties.
type PropsReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	EntityMrn     string                 `protobuf:"bytes,1,opt,name=entity_mrn,json=entityMrn,proto3" json:"entity_mrn,omitempty"`
	Props         []*Property            `protobuf:"bytes,2,rep,name=props,proto3" json:"props,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PropsReq) Reset() {
	*x = PropsReq{}
	mi := &file_cnquery_explorer_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PropsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PropsReq) ProtoMessage() {}

func (x *PropsReq) ProtoReflect() protoreflect.Message {
	mi := &file_cnquery_explorer_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PropsReq.ProtoReflect.Descriptor instead.
func (*PropsReq) Descriptor() ([]byte, []int) {
	return file_cnquery_explorer_proto_rawDescGZIP(), []int{26}
}

func (x *PropsReq) GetEntityMrn() string {
	if x != nil {
		return x.EntityMrn
	}
	return ""
}

func (x *PropsReq) GetProps() []*Property {
	if x != nil {
		return x.Props
	}
	return nil
}

// Resolve a given entity via its MRN. Typically used to resolve assets.
// Can also be used to resolve query packs.
type ResolveReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	EntityMrn     string                 `protobuf:"bytes,1,opt,name=entity_mrn,json=entityMrn,proto3" json:"entity_mrn,omitempty"`
	AssetFilters  []*Mquery              `protobuf:"bytes,2,rep,name=asset_filters,json=assetFilters,proto3" json:"asset_filters,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ResolveReq) Reset() {
	*x = ResolveReq{}
	mi := &file_cnquery_explorer_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ResolveReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResolveReq) ProtoMessage() {}

func (x *ResolveReq) ProtoReflect() protoreflect.Message {
	mi := &file_cnquery_explorer_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResolveReq.ProtoReflect.Descriptor instead.
func (*ResolveReq) Descriptor() ([]byte, []int) {
	return file_cnquery_explorer_proto_rawDescGZIP(), []int{27}
}

func (x *ResolveReq) GetEntityMrn() string {
	if x != nil {
		return x.EntityMrn
	}
	return ""
}

func (x *ResolveReq) GetAssetFilters() []*Mquery {
	if x != nil {
		return x.AssetFilters
	}
	return nil
}

// ResolvedPack is returned from a resolve request. It includes the execution
// job with all things that need to be run.
type ResolvedPack struct {
	state                  protoimpl.MessageState `protogen:"open.v1"`
	ExecutionJob           *ExecutionJob          `protobuf:"bytes,2,opt,name=execution_job,json=executionJob,proto3" json:"execution_job,omitempty"`
	Filters                []*Mquery              `protobuf:"bytes,4,rep,name=filters,proto3" json:"filters,omitempty"`
	GraphExecutionChecksum string                 `protobuf:"bytes,7,opt,name=graph_execution_checksum,json=graphExecutionChecksum,proto3" json:"graph_execution_checksum,omitempty"`
	FiltersChecksum        string                 `protobuf:"bytes,20,opt,name=filters_checksum,json=filtersChecksum,proto3" json:"filters_checksum,omitempty"`
	unknownFields          protoimpl.UnknownFields
	sizeCache              protoimpl.SizeCache
}

func (x *ResolvedPack) Reset() {
	*x = ResolvedPack{}
	mi := &file_cnquery_explorer_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ResolvedPack) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResolvedPack) ProtoMessage() {}

func (x *ResolvedPack) ProtoReflect() protoreflect.Message {
	mi := &file_cnquery_explorer_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResolvedPack.ProtoReflect.Descriptor instead.
func (*ResolvedPack) Descriptor() ([]byte, []int) {
	return file_cnquery_explorer_proto_rawDescGZIP(), []int{28}
}

func (x *ResolvedPack) GetExecutionJob() *ExecutionJob {
	if x != nil {
		return x.ExecutionJob
	}
	return nil
}

func (x *ResolvedPack) GetFilters() []*Mquery {
	if x != nil {
		return x.Filters
	}
	return nil
}

func (x *ResolvedPack) GetGraphExecutionChecksum() string {
	if x != nil {
		return x.GraphExecutionChecksum
	}
	return ""
}

func (x *ResolvedPack) GetFiltersChecksum() string {
	if x != nil {
		return x.FiltersChecksum
	}
	return ""
}

// Update asset jobs forces all jobs for a given asset to get refreshed.
type UpdateAssetJobsReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AssetMrn      string                 `protobuf:"bytes,1,opt,name=asset_mrn,json=assetMrn,proto3" json:"asset_mrn,omitempty"`
	AssetFilters  []*Mquery              `protobuf:"bytes,2,rep,name=asset_filters,json=assetFilters,proto3" json:"asset_filters,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateAssetJobsReq) Reset() {
	*x = UpdateAssetJobsReq{}
	mi := &file_cnquery_explorer_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateAssetJobsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAssetJobsReq) ProtoMessage() {}

func (x *UpdateAssetJobsReq) ProtoReflect() protoreflect.Message {
	mi := &file_cnquery_explorer_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAssetJobsReq.ProtoReflect.Descriptor instead.
func (*UpdateAssetJobsReq) Descriptor() ([]byte, []int) {
	return file_cnquery_explorer_proto_rawDescGZIP(), []int{29}
}

func (x *UpdateAssetJobsReq) GetAssetMrn() string {
	if x != nil {
		return x.AssetMrn
	}
	return ""
}

func (x *UpdateAssetJobsReq) GetAssetFilters() []*Mquery {
	if x != nil {
		return x.AssetFilters
	}
	return nil
}

// Store results for a given asset
type StoreResultsReq struct {
	state     protoimpl.MessageState            `protogen:"open.v1"`
	AssetMrn  string                            `protobuf:"bytes,1,opt,name=asset_mrn,json=assetMrn,proto3" json:"asset_mrn,omitempty"`
	Data      map[string]*llx.Result            `protobuf:"bytes,3,rep,name=data,proto3" json:"data,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	Resources map[string]*llx.ResourceRecording `protobuf:"bytes,4,rep,name=resources,proto3" json:"resources,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	// Determines if this is the last batch of results for the asset
	IsLastBatch   bool `protobuf:"varint,5,opt,name=is_last_batch,json=isLastBatch,proto3" json:"is_last_batch,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StoreResultsReq) Reset() {
	*x = StoreResultsReq{}
	mi := &file_cnquery_explorer_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StoreResultsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoreResultsReq) ProtoMessage() {}

func (x *StoreResultsReq) ProtoReflect() protoreflect.Message {
	mi := &file_cnquery_explorer_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoreResultsReq.ProtoReflect.Descriptor instead.
func (*StoreResultsReq) Descriptor() ([]byte, []int) {
	return file_cnquery_explorer_proto_rawDescGZIP(), []int{30}
}

func (x *StoreResultsReq) GetAssetMrn() string {
	if x != nil {
		return x.AssetMrn
	}
	return ""
}

func (x *StoreResultsReq) GetData() map[string]*llx.Result {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *StoreResultsReq) GetResources() map[string]*llx.ResourceRecording {
	if x != nil {
		return x.Resources
	}
	return nil
}

func (x *StoreResultsReq) GetIsLastBatch() bool {
	if x != nil {
		return x.IsLastBatch
	}
	return false
}

// Retrieve data for a given set of entities which was previously stored
type EntityDataRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	EntityMrn     string                 `protobuf:"bytes,1,opt,name=entity_mrn,json=entityMrn,proto3" json:"entity_mrn,omitempty"`
	DataMrn       string                 `protobuf:"bytes,2,opt,name=data_mrn,json=dataMrn,proto3" json:"data_mrn,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EntityDataRequest) Reset() {
	*x = EntityDataRequest{}
	mi := &file_cnquery_explorer_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EntityDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EntityDataRequest) ProtoMessage() {}

func (x *EntityDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cnquery_explorer_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EntityDataRequest.ProtoReflect.Descriptor instead.
func (*EntityDataRequest) Descriptor() ([]byte, []int) {
	return file_cnquery_explorer_proto_rawDescGZIP(), []int{31}
}

func (x *EntityDataRequest) GetEntityMrn() string {
	if x != nil {
		return x.EntityMrn
	}
	return ""
}

func (x *EntityDataRequest) GetDataMrn() string {
	if x != nil {
		return x.DataMrn
	}
	return ""
}

// The report of all the things collected for an entity (typically asset). The
// provided pack is used as the root to decide what data fields will be
// returned.
type Report struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	PackMrn         string                 `protobuf:"bytes,1,opt,name=pack_mrn,json=packMrn,proto3" json:"pack_mrn,omitempty"`
	EntityMrn       string                 `protobuf:"bytes,2,opt,name=entity_mrn,json=entityMrn,proto3" json:"entity_mrn,omitempty"`
	Data            map[string]*llx.Result `protobuf:"bytes,5,rep,name=data,proto3" json:"data,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	Created         int64                  `protobuf:"varint,20,opt,name=created,proto3" json:"created,omitempty"`
	Modified        int64                  `protobuf:"varint,21,opt,name=modified,proto3" json:"modified,omitempty"`
	ResolvedVersion string                 `protobuf:"bytes,33,opt,name=resolved_version,json=resolvedVersion,proto3" json:"resolved_version,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *Report) Reset() {
	*x = Report{}
	mi := &file_cnquery_explorer_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Report) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Report) ProtoMessage() {}

func (x *Report) ProtoReflect() protoreflect.Message {
	mi := &file_cnquery_explorer_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Report.ProtoReflect.Descriptor instead.
func (*Report) Descriptor() ([]byte, []int) {
	return file_cnquery_explorer_proto_rawDescGZIP(), []int{32}
}

func (x *Report) GetPackMrn() string {
	if x != nil {
		return x.PackMrn
	}
	return ""
}

func (x *Report) GetEntityMrn() string {
	if x != nil {
		return x.EntityMrn
	}
	return ""
}

func (x *Report) GetData() map[string]*llx.Result {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *Report) GetCreated() int64 {
	if x != nil {
		return x.Created
	}
	return 0
}

func (x *Report) GetModified() int64 {
	if x != nil {
		return x.Modified
	}
	return 0
}

func (x *Report) GetResolvedVersion() string {
	if x != nil {
		return x.ResolvedVersion
	}
	return ""
}

// Asset is a lean layer of information about an asset
type Asset struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Mrn           string                 `protobuf:"bytes,1,opt,name=mrn,proto3" json:"mrn,omitempty"`
	Labels        map[string]string      `protobuf:"bytes,3,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	Name          string                 `protobuf:"bytes,18,opt,name=name,proto3" json:"name,omitempty"`
	TraceId       string                 `protobuf:"bytes,19,opt,name=trace_id,json=traceId,proto3" json:"trace_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Asset) Reset() {
	*x = Asset{}
	mi := &file_cnquery_explorer_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Asset) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Asset) ProtoMessage() {}

func (x *Asset) ProtoReflect() protoreflect.Message {
	mi := &file_cnquery_explorer_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Asset.ProtoReflect.Descriptor instead.
func (*Asset) Descriptor() ([]byte, []int) {
	return file_cnquery_explorer_proto_rawDescGZIP(), []int{33}
}

func (x *Asset) GetMrn() string {
	if x != nil {
		return x.Mrn
	}
	return ""
}

func (x *Asset) GetLabels() map[string]string {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *Asset) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Asset) GetTraceId() string {
	if x != nil {
		return x.TraceId
	}
	return ""
}

type ReportCollection struct {
	state         protoimpl.MessageState   `protogen:"open.v1"`
	Assets        map[string]*Asset        `protobuf:"bytes,1,rep,name=assets,proto3" json:"assets,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	Bundle        *Bundle                  `protobuf:"bytes,2,opt,name=bundle,proto3" json:"bundle,omitempty"`
	Reports       map[string]*Report       `protobuf:"bytes,3,rep,name=reports,proto3" json:"reports,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	Errors        map[string]*ErrorStatus  `protobuf:"bytes,4,rep,name=errors,proto3" json:"errors,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	Resolved      map[string]*ResolvedPack `protobuf:"bytes,5,rep,name=resolved,proto3" json:"resolved,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReportCollection) Reset() {
	*x = ReportCollection{}
	mi := &file_cnquery_explorer_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReportCollection) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReportCollection) ProtoMessage() {}

func (x *ReportCollection) ProtoReflect() protoreflect.Message {
	mi := &file_cnquery_explorer_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReportCollection.ProtoReflect.Descriptor instead.
func (*ReportCollection) Descriptor() ([]byte, []int) {
	return file_cnquery_explorer_proto_rawDescGZIP(), []int{34}
}

func (x *ReportCollection) GetAssets() map[string]*Asset {
	if x != nil {
		return x.Assets
	}
	return nil
}

func (x *ReportCollection) GetBundle() *Bundle {
	if x != nil {
		return x.Bundle
	}
	return nil
}

func (x *ReportCollection) GetReports() map[string]*Report {
	if x != nil {
		return x.Reports
	}
	return nil
}

func (x *ReportCollection) GetErrors() map[string]*ErrorStatus {
	if x != nil {
		return x.Errors
	}
	return nil
}

func (x *ReportCollection) GetResolved() map[string]*ResolvedPack {
	if x != nil {
		return x.Resolved
	}
	return nil
}

type ErrorStatus struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The status code.
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	// A user-facing error message, which should be in English.
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	// A list of messages that carry the error details.
	Details       []*anypb.Any `protobuf:"bytes,3,rep,name=details,proto3" json:"details,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ErrorStatus) Reset() {
	*x = ErrorStatus{}
	mi := &file_cnquery_explorer_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ErrorStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ErrorStatus) ProtoMessage() {}

func (x *ErrorStatus) ProtoReflect() protoreflect.Message {
	mi := &file_cnquery_explorer_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ErrorStatus.ProtoReflect.Descriptor instead.
func (*ErrorStatus) Descriptor() ([]byte, []int) {
	return file_cnquery_explorer_proto_rawDescGZIP(), []int{35}
}

func (x *ErrorStatus) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ErrorStatus) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ErrorStatus) GetDetails() []*anypb.Any {
	if x != nil {
		return x.Details
	}
	return nil
}

type AssignmentDelta struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Mrn           string                 `protobuf:"bytes,1,opt,name=mrn,proto3" json:"mrn,omitempty"`
	Action        AssignmentDelta_Action `protobuf:"varint,2,opt,name=action,proto3,enum=cnquery.explorer.AssignmentDelta_Action" json:"action,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AssignmentDelta) Reset() {
	*x = AssignmentDelta{}
	mi := &file_cnquery_explorer_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AssignmentDelta) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssignmentDelta) ProtoMessage() {}

func (x *AssignmentDelta) ProtoReflect() protoreflect.Message {
	mi := &file_cnquery_explorer_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssignmentDelta.ProtoReflect.Descriptor instead.
func (*AssignmentDelta) Descriptor() ([]byte, []int) {
	return file_cnquery_explorer_proto_rawDescGZIP(), []int{36}
}

func (x *AssignmentDelta) GetMrn() string {
	if x != nil {
		return x.Mrn
	}
	return ""
}

func (x *AssignmentDelta) GetAction() AssignmentDelta_Action {
	if x != nil {
		return x.Action
	}
	return AssignmentDelta_UNKNOWN
}

type BundleMutationDelta struct {
	state         protoimpl.MessageState      `protogen:"open.v1"`
	OwnerMrn      string                      `protobuf:"bytes,1,opt,name=owner_mrn,json=ownerMrn,proto3" json:"owner_mrn,omitempty"`
	Deltas        map[string]*AssignmentDelta `protobuf:"bytes,2,rep,name=deltas,proto3" json:"deltas,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BundleMutationDelta) Reset() {
	*x = BundleMutationDelta{}
	mi := &file_cnquery_explorer_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BundleMutationDelta) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BundleMutationDelta) ProtoMessage() {}

func (x *BundleMutationDelta) ProtoReflect() protoreflect.Message {
	mi := &file_cnquery_explorer_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BundleMutationDelta.ProtoReflect.Descriptor instead.
func (*BundleMutationDelta) Descriptor() ([]byte, []int) {
	return file_cnquery_explorer_proto_rawDescGZIP(), []int{37}
}

func (x *BundleMutationDelta) GetOwnerMrn() string {
	if x != nil {
		return x.OwnerMrn
	}
	return ""
}

func (x *BundleMutationDelta) GetDeltas() map[string]*AssignmentDelta {
	if x != nil {
		return x.Deltas
	}
	return nil
}

type SynchronizeAssetsReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SpaceMrn      string                 `protobuf:"bytes,1,opt,name=space_mrn,json=spaceMrn,proto3" json:"space_mrn,omitempty"`
	List          []*inventory.Asset     `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SynchronizeAssetsReq) Reset() {
	*x = SynchronizeAssetsReq{}
	mi := &file_cnquery_explorer_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SynchronizeAssetsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SynchronizeAssetsReq) ProtoMessage() {}

func (x *SynchronizeAssetsReq) ProtoReflect() protoreflect.Message {
	mi := &file_cnquery_explorer_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SynchronizeAssetsReq.ProtoReflect.Descriptor instead.
func (*SynchronizeAssetsReq) Descriptor() ([]byte, []int) {
	return file_cnquery_explorer_proto_rawDescGZIP(), []int{38}
}

func (x *SynchronizeAssetsReq) GetSpaceMrn() string {
	if x != nil {
		return x.SpaceMrn
	}
	return ""
}

func (x *SynchronizeAssetsReq) GetList() []*inventory.Asset {
	if x != nil {
		return x.List
	}
	return nil
}

type SynchronizeAssetsRespAssetDetail struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PlatformMrn   string                 `protobuf:"bytes,1,opt,name=platform_mrn,json=platformMrn,proto3" json:"platform_mrn,omitempty"`
	AssetMrn      string                 `protobuf:"bytes,2,opt,name=asset_mrn,json=assetMrn,proto3" json:"asset_mrn,omitempty"`
	Url           string                 `protobuf:"bytes,3,opt,name=url,proto3" json:"url,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SynchronizeAssetsRespAssetDetail) Reset() {
	*x = SynchronizeAssetsRespAssetDetail{}
	mi := &file_cnquery_explorer_proto_msgTypes[39]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SynchronizeAssetsRespAssetDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SynchronizeAssetsRespAssetDetail) ProtoMessage() {}

func (x *SynchronizeAssetsRespAssetDetail) ProtoReflect() protoreflect.Message {
	mi := &file_cnquery_explorer_proto_msgTypes[39]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SynchronizeAssetsRespAssetDetail.ProtoReflect.Descriptor instead.
func (*SynchronizeAssetsRespAssetDetail) Descriptor() ([]byte, []int) {
	return file_cnquery_explorer_proto_rawDescGZIP(), []int{39}
}

func (x *SynchronizeAssetsRespAssetDetail) GetPlatformMrn() string {
	if x != nil {
		return x.PlatformMrn
	}
	return ""
}

func (x *SynchronizeAssetsRespAssetDetail) GetAssetMrn() string {
	if x != nil {
		return x.AssetMrn
	}
	return ""
}

func (x *SynchronizeAssetsRespAssetDetail) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

type SynchronizeAssetsResp struct {
	state         protoimpl.MessageState                       `protogen:"open.v1"`
	Details       map[string]*SynchronizeAssetsRespAssetDetail `protobuf:"bytes,1,rep,name=details,proto3" json:"details,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SynchronizeAssetsResp) Reset() {
	*x = SynchronizeAssetsResp{}
	mi := &file_cnquery_explorer_proto_msgTypes[40]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SynchronizeAssetsResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SynchronizeAssetsResp) ProtoMessage() {}

func (x *SynchronizeAssetsResp) ProtoReflect() protoreflect.Message {
	mi := &file_cnquery_explorer_proto_msgTypes[40]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SynchronizeAssetsResp.ProtoReflect.Descriptor instead.
func (*SynchronizeAssetsResp) Descriptor() ([]byte, []int) {
	return file_cnquery_explorer_proto_rawDescGZIP(), []int{40}
}

func (x *SynchronizeAssetsResp) GetDetails() map[string]*SynchronizeAssetsRespAssetDetail {
	if x != nil {
		return x.Details
	}
	return nil
}

type ImpactValue struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Value         int32                  `protobuf:"varint,1,opt,name=value,proto3" json:"value,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ImpactValue) Reset() {
	*x = ImpactValue{}
	mi := &file_cnquery_explorer_proto_msgTypes[41]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ImpactValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImpactValue) ProtoMessage() {}

func (x *ImpactValue) ProtoReflect() protoreflect.Message {
	mi := &file_cnquery_explorer_proto_msgTypes[41]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImpactValue.ProtoReflect.Descriptor instead.
func (*ImpactValue) Descriptor() ([]byte, []int) {
	return file_cnquery_explorer_proto_rawDescGZIP(), []int{41}
}

func (x *ImpactValue) GetValue() int32 {
	if x != nil {
		return x.Value
	}
	return 0
}

var File_cnquery_explorer_proto protoreflect.FileDescriptor

const file_cnquery_explorer_proto_rawDesc = "" +
	"\n" +
	"\x16cnquery_explorer.proto\x12\x10cnquery.explorer\x1a\x19google/protobuf/any.proto\x1a\rllx/llx.proto\x1a*providers-sdk/v1/inventory/inventory.proto\"\xbe\x01\n" +
	"\x06Bundle\x12\x1b\n" +
	"\towner_mrn\x18\x01 \x01(\tR\bownerMrn\x121\n" +
	"\x05packs\x18\x02 \x03(\v2\x1b.cnquery.explorer.QueryPackR\x05packs\x122\n" +
	"\aqueries\x18\x03 \x03(\v2\x18.cnquery.explorer.MqueryR\aqueries\x120\n" +
	"\x05props\x18\x04 \x03(\v2\x1a.cnquery.explorer.PropertyR\x05props\"\xc1\x01\n" +
	"\n" +
	"QueryGroup\x122\n" +
	"\aqueries\x18\x03 \x03(\v2\x18.cnquery.explorer.MqueryR\aqueries\x123\n" +
	"\afilters\x18\x14 \x01(\v2\x19.cnquery.explorer.FiltersR\afilters\x12\x14\n" +
	"\x05title\x18\x18 \x01(\tR\x05title\x12\x18\n" +
	"\acreated\x18  \x01(\x03R\acreated\x12\x1a\n" +
	"\bmodified\x18! \x01(\x03R\bmodified\"\xf4\x06\n" +
	"\tQueryPack\x12\x10\n" +
	"\x03uid\x18$ \x01(\tR\x03uid\x12\x18\n" +
	"\acontext\x18\b \x01(\tR\acontext\x12\x10\n" +
	"\x03mrn\x18\x01 \x01(\tR\x03mrn\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x18\n" +
	"\aversion\x18\x03 \x01(\tR\aversion\x12\x1b\n" +
	"\towner_mrn\x18\x04 \x01(\tR\bownerMrn\x122\n" +
	"\aqueries\x18\x06 \x03(\v2\x18.cnquery.explorer.MqueryR\aqueries\x124\n" +
	"\x06groups\x18\v \x03(\v2\x1c.cnquery.explorer.QueryGroupR\x06groups\x120\n" +
	"\x05props\x18# \x03(\v2\x1a.cnquery.explorer.PropertyR\x05props\x12D\n" +
	"\x10computed_filters\x18/ \x01(\v2\x19.cnquery.explorer.FiltersR\x0fcomputedFilters\x123\n" +
	"\afilters\x180 \x01(\v2\x19.cnquery.explorer.FiltersR\afilters\x12\x18\n" +
	"\alicense\x18\x15 \x01(\tR\alicense\x123\n" +
	"\x04docs\x18\x16 \x01(\v2\x1f.cnquery.explorer.QueryPackDocsR\x04docs\x12\x18\n" +
	"\asummary\x18. \x01(\tR\asummary\x122\n" +
	"\aauthors\x18\x1e \x03(\v2\x18.cnquery.explorer.AuthorR\aauthors\x12\x18\n" +
	"\acreated\x18  \x01(\x03R\acreated\x12\x1a\n" +
	"\bmodified\x18! \x01(\x03R\bmodified\x129\n" +
	"\x04tags\x18\" \x03(\v2%.cnquery.explorer.QueryPack.TagsEntryR\x04tags\x124\n" +
	"\x16local_content_checksum\x18\x17 \x01(\tR\x14localContentChecksum\x128\n" +
	"\x18local_execution_checksum\x18\x18 \x01(\tR\x16localExecutionChecksum\x1a7\n" +
	"\tTagsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01J\x04\b\a\x10\bJ\x04\b\x14\x10\x15J\x04\b+\x10,\"\xa3\x01\n" +
	"\tObjectRef\x12\x10\n" +
	"\x03mrn\x18\x01 \x01(\tR\x03mrn\x12\x10\n" +
	"\x03uid\x18\x02 \x01(\tR\x03uid\x129\n" +
	"\x04tags\x18\x03 \x03(\v2%.cnquery.explorer.ObjectRef.TagsEntryR\x04tags\x1a7\n" +
	"\tTagsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\x99\x01\n" +
	"\aFilters\x12:\n" +
	"\x05items\x18\x01 \x03(\v2$.cnquery.explorer.Filters.ItemsEntryR\x05items\x1aR\n" +
	"\n" +
	"ItemsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12.\n" +
	"\x05value\x18\x02 \x01(\v2\x18.cnquery.explorer.MqueryR\x05value:\x028\x01\"?\n" +
	"\n" +
	"QueryPacks\x121\n" +
	"\x05items\x18\x01 \x03(\v2\x1b.cnquery.explorer.QueryPackR\x05items\"K\n" +
	"\x04Docs\x12\x12\n" +
	"\x04desc\x18\x01 \x01(\tR\x04desc\x12/\n" +
	"\x04refs\x18\x02 \x03(\v2\x1b.cnquery.explorer.MqueryRefR\x04refs\"\xfc\x01\n" +
	"\bProperty\x12\x10\n" +
	"\x03mql\x18\x01 \x01(\tR\x03mql\x12\x17\n" +
	"\acode_id\x18\x02 \x01(\tR\x06codeId\x12\x1a\n" +
	"\bchecksum\x18\x03 \x01(\tR\bchecksum\x12\x10\n" +
	"\x03mrn\x18\x04 \x01(\tR\x03mrn\x12\x10\n" +
	"\x03uid\x18\x05 \x01(\tR\x03uid\x12\x12\n" +
	"\x04type\x18\x06 \x01(\tR\x04type\x12\x18\n" +
	"\acontext\x18\a \x01(\tR\acontext\x12-\n" +
	"\x03for\x18\b \x03(\v2\x1b.cnquery.explorer.ObjectRefR\x03for\x12\x14\n" +
	"\x05title\x18\x14 \x01(\tR\x05title\x12\x12\n" +
	"\x04desc\x18# \x01(\tR\x04desc\"\xb9\x05\n" +
	"\x06Mquery\x12\x14\n" +
	"\x05query\x18( \x01(\tR\x05query\x12/\n" +
	"\x04refs\x18\x16 \x03(\v2\x1b.cnquery.explorer.MqueryRefR\x04refs\x12\x10\n" +
	"\x03mql\x18\x01 \x01(\tR\x03mql\x12\x17\n" +
	"\acode_id\x18\x02 \x01(\tR\x06codeId\x12\x1a\n" +
	"\bchecksum\x18\x03 \x01(\tR\bchecksum\x12\x10\n" +
	"\x03mrn\x18\x04 \x01(\tR\x03mrn\x12\x10\n" +
	"\x03uid\x18\x05 \x01(\tR\x03uid\x12\x12\n" +
	"\x04type\x18\x06 \x01(\tR\x04type\x12\x18\n" +
	"\acontext\x18\a \x01(\tR\acontext\x12\x14\n" +
	"\x05title\x18\x14 \x01(\tR\x05title\x120\n" +
	"\x04docs\x18\x15 \x01(\v2\x1c.cnquery.explorer.MqueryDocsR\x04docs\x12\x12\n" +
	"\x04desc\x18# \x01(\tR\x04desc\x120\n" +
	"\x06impact\x18\x17 \x01(\v2\x18.cnquery.explorer.ImpactR\x06impact\x126\n" +
	"\x04tags\x18\" \x03(\v2\".cnquery.explorer.Mquery.TagsEntryR\x04tags\x123\n" +
	"\afilters\x18% \x01(\v2\x19.cnquery.explorer.FiltersR\afilters\x120\n" +
	"\x05props\x18& \x03(\v2\x1a.cnquery.explorer.PropertyR\x05props\x127\n" +
	"\bvariants\x18' \x03(\v2\x1b.cnquery.explorer.ObjectRefR\bvariants\x120\n" +
	"\x06action\x18) \x01(\x0e2\x18.cnquery.explorer.ActionR\x06action\x1a7\n" +
	"\tTagsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\xc2\x01\n" +
	"\x06Impact\x123\n" +
	"\x05value\x18\x01 \x01(\v2\x1d.cnquery.explorer.ImpactValueR\x05value\x129\n" +
	"\ascoring\x18\x02 \x01(\x0e2\x1f.cnquery.explorer.ScoringSystemR\ascoring\x12\x16\n" +
	"\x06weight\x18\x03 \x01(\x05R\x06weight\x120\n" +
	"\x06action\x18\x04 \x01(\x0e2\x18.cnquery.explorer.ActionR\x06action\"#\n" +
	"\rQueryPackDocs\x12\x12\n" +
	"\x04desc\x18\x01 \x01(\tR\x04desc\"\xae\x01\n" +
	"\n" +
	"MqueryDocs\x12\x12\n" +
	"\x04desc\x18\x01 \x01(\tR\x04desc\x12\x14\n" +
	"\x05audit\x18\x02 \x01(\tR\x05audit\x12/\n" +
	"\x04refs\x18\x04 \x03(\v2\x1b.cnquery.explorer.MqueryRefR\x04refs\x12?\n" +
	"\vremediation\x18\x05 \x01(\v2\x1d.cnquery.explorer.RemediationR\vremediationJ\x04\b\x03\x10\x04\"?\n" +
	"\vRemediation\x120\n" +
	"\x05items\x18\x01 \x03(\v2\x1a.cnquery.explorer.TypedDocR\x05items\"\xf3\x01\n" +
	"\bTypedDoc\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x12\n" +
	"\x04desc\x18\x02 \x01(\tR\x04desc\x12\x1a\n" +
	"\blanguage\x18\x03 \x01(\tR\blanguage\x12\x18\n" +
	"\acreated\x18\x14 \x01(\x03R\acreated\x12\x1a\n" +
	"\bmodified\x18\x15 \x01(\x03R\bmodified\x128\n" +
	"\x04tags\x18\x16 \x03(\v2$.cnquery.explorer.TypedDoc.TagsEntryR\x04tags\x1a7\n" +
	"\tTagsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"2\n" +
	"\x06Author\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x14\n" +
	"\x05email\x18\x02 \x01(\tR\x05email\"3\n" +
	"\tMqueryRef\x12\x14\n" +
	"\x05title\x18\x01 \x01(\tR\x05title\x12\x10\n" +
	"\x03url\x18\x02 \x01(\tR\x03url\"\xff\x02\n" +
	"\fExecutionJob\x12\x1a\n" +
	"\bchecksum\x18\x01 \x01(\tR\bchecksum\x12E\n" +
	"\aqueries\x18\x02 \x03(\v2+.cnquery.explorer.ExecutionJob.QueriesEntryR\aqueries\x12N\n" +
	"\n" +
	"datapoints\x18\x03 \x03(\v2..cnquery.explorer.ExecutionJob.DatapointsEntryR\n" +
	"datapoints\x1a\\\n" +
	"\fQueriesEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x126\n" +
	"\x05value\x18\x02 \x01(\v2 .cnquery.explorer.ExecutionQueryR\x05value:\x028\x01\x1a^\n" +
	"\x0fDatapointsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x125\n" +
	"\x05value\x18\x02 \x01(\v2\x1f.cnquery.explorer.DataQueryInfoR\x05value:\x028\x01\";\n" +
	"\rDataQueryInfo\x12\x12\n" +
	"\x04type\x18\x01 \x01(\tR\x04type\x12\x16\n" +
	"\x06notify\x18\x02 \x03(\tR\x06notify\"\xa0\x02\n" +
	"\x0eExecutionQuery\x12\x14\n" +
	"\x05query\x18\x01 \x01(\tR\x05query\x12\x1a\n" +
	"\bchecksum\x18\x02 \x01(\tR\bchecksum\x12P\n" +
	"\n" +
	"properties\x18\x03 \x03(\v20.cnquery.explorer.ExecutionQuery.PropertiesEntryR\n" +
	"properties\x12\x1e\n" +
	"\n" +
	"datapoints\x18\x04 \x03(\tR\n" +
	"datapoints\x12+\n" +
	"\x04code\x18\x05 \x01(\v2\x17.cnquery.llx.CodeBundleR\x04code\x1a=\n" +
	"\x0fPropertiesEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\a\n" +
	"\x05Empty\"\x17\n" +
	"\x03Mrn\x12\x10\n" +
	"\x03mrn\x18\x01 \x01(\tR\x03mrn\":\n" +
	"\bMqueries\x12.\n" +
	"\x05items\x18\x01 \x03(\v2\x18.cnquery.explorer.MqueryR\x05items\":\n" +
	"\aListReq\x12\x1b\n" +
	"\towner_mrn\x18\x01 \x01(\tR\bownerMrn\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\"\x8d\x01\n" +
	"\x0fDefaultPacksReq\x12\x12\n" +
	"\x04kind\x18\x01 \x01(\tR\x04kind\x12\x1a\n" +
	"\bplatform\x18\x02 \x01(\tR\bplatform\x12\x18\n" +
	"\aruntime\x18\x03 \x01(\tR\aruntime\x12\x18\n" +
	"\aversion\x18\x04 \x01(\tR\aversion\x12\x16\n" +
	"\x06family\x18\x05 \x03(\tR\x06family\"\x1a\n" +
	"\x04URLs\x12\x12\n" +
	"\x04urls\x18\x01 \x03(\tR\x04urls\"F\n" +
	"\n" +
	"Assignment\x12\x1b\n" +
	"\tasset_mrn\x18\x01 \x01(\tR\bassetMrn\x12\x1b\n" +
	"\tpack_mrns\x18\x02 \x03(\tR\bpackMrns\"[\n" +
	"\bPropsReq\x12\x1d\n" +
	"\n" +
	"entity_mrn\x18\x01 \x01(\tR\tentityMrn\x120\n" +
	"\x05props\x18\x02 \x03(\v2\x1a.cnquery.explorer.PropertyR\x05props\"j\n" +
	"\n" +
	"ResolveReq\x12\x1d\n" +
	"\n" +
	"entity_mrn\x18\x01 \x01(\tR\tentityMrn\x12=\n" +
	"\rasset_filters\x18\x02 \x03(\v2\x18.cnquery.explorer.MqueryR\fassetFilters\"\xec\x01\n" +
	"\fResolvedPack\x12C\n" +
	"\rexecution_job\x18\x02 \x01(\v2\x1e.cnquery.explorer.ExecutionJobR\fexecutionJob\x122\n" +
	"\afilters\x18\x04 \x03(\v2\x18.cnquery.explorer.MqueryR\afilters\x128\n" +
	"\x18graph_execution_checksum\x18\a \x01(\tR\x16graphExecutionChecksum\x12)\n" +
	"\x10filters_checksum\x18\x14 \x01(\tR\x0ffiltersChecksum\"p\n" +
	"\x12UpdateAssetJobsReq\x12\x1b\n" +
	"\tasset_mrn\x18\x01 \x01(\tR\bassetMrn\x12=\n" +
	"\rasset_filters\x18\x02 \x03(\v2\x18.cnquery.explorer.MqueryR\fassetFilters\"\x8f\x03\n" +
	"\x0fStoreResultsReq\x12\x1b\n" +
	"\tasset_mrn\x18\x01 \x01(\tR\bassetMrn\x12?\n" +
	"\x04data\x18\x03 \x03(\v2+.cnquery.explorer.StoreResultsReq.DataEntryR\x04data\x12N\n" +
	"\tresources\x18\x04 \x03(\v20.cnquery.explorer.StoreResultsReq.ResourcesEntryR\tresources\x12\"\n" +
	"\ris_last_batch\x18\x05 \x01(\bR\visLastBatch\x1aL\n" +
	"\tDataEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12)\n" +
	"\x05value\x18\x02 \x01(\v2\x13.cnquery.llx.ResultR\x05value:\x028\x01\x1a\\\n" +
	"\x0eResourcesEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x124\n" +
	"\x05value\x18\x02 \x01(\v2\x1e.cnquery.llx.ResourceRecordingR\x05value:\x028\x01\"M\n" +
	"\x11EntityDataRequest\x12\x1d\n" +
	"\n" +
	"entity_mrn\x18\x01 \x01(\tR\tentityMrn\x12\x19\n" +
	"\bdata_mrn\x18\x02 \x01(\tR\adataMrn\"\xa9\x02\n" +
	"\x06Report\x12\x19\n" +
	"\bpack_mrn\x18\x01 \x01(\tR\apackMrn\x12\x1d\n" +
	"\n" +
	"entity_mrn\x18\x02 \x01(\tR\tentityMrn\x126\n" +
	"\x04data\x18\x05 \x03(\v2\".cnquery.explorer.Report.DataEntryR\x04data\x12\x18\n" +
	"\acreated\x18\x14 \x01(\x03R\acreated\x12\x1a\n" +
	"\bmodified\x18\x15 \x01(\x03R\bmodified\x12)\n" +
	"\x10resolved_version\x18! \x01(\tR\x0fresolvedVersion\x1aL\n" +
	"\tDataEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12)\n" +
	"\x05value\x18\x02 \x01(\v2\x13.cnquery.llx.ResultR\x05value:\x028\x01\"\xc0\x01\n" +
	"\x05Asset\x12\x10\n" +
	"\x03mrn\x18\x01 \x01(\tR\x03mrn\x12;\n" +
	"\x06labels\x18\x03 \x03(\v2#.cnquery.explorer.Asset.LabelsEntryR\x06labels\x12\x12\n" +
	"\x04name\x18\x12 \x01(\tR\x04name\x12\x19\n" +
	"\btrace_id\x18\x13 \x01(\tR\atraceId\x1a9\n" +
	"\vLabelsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\xce\x05\n" +
	"\x10ReportCollection\x12F\n" +
	"\x06assets\x18\x01 \x03(\v2..cnquery.explorer.ReportCollection.AssetsEntryR\x06assets\x120\n" +
	"\x06bundle\x18\x02 \x01(\v2\x18.cnquery.explorer.BundleR\x06bundle\x12I\n" +
	"\areports\x18\x03 \x03(\v2/.cnquery.explorer.ReportCollection.ReportsEntryR\areports\x12F\n" +
	"\x06errors\x18\x04 \x03(\v2..cnquery.explorer.ReportCollection.ErrorsEntryR\x06errors\x12L\n" +
	"\bresolved\x18\x05 \x03(\v20.cnquery.explorer.ReportCollection.ResolvedEntryR\bresolved\x1aR\n" +
	"\vAssetsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12-\n" +
	"\x05value\x18\x02 \x01(\v2\x17.cnquery.explorer.AssetR\x05value:\x028\x01\x1aT\n" +
	"\fReportsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12.\n" +
	"\x05value\x18\x02 \x01(\v2\x18.cnquery.explorer.ReportR\x05value:\x028\x01\x1aX\n" +
	"\vErrorsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x123\n" +
	"\x05value\x18\x02 \x01(\v2\x1d.cnquery.explorer.ErrorStatusR\x05value:\x028\x01\x1a[\n" +
	"\rResolvedEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x124\n" +
	"\x05value\x18\x02 \x01(\v2\x1e.cnquery.explorer.ResolvedPackR\x05value:\x028\x01\"k\n" +
	"\vErrorStatus\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12.\n" +
	"\adetails\x18\x03 \x03(\v2\x14.google.protobuf.AnyR\adetails\"\x91\x01\n" +
	"\x0fAssignmentDelta\x12\x10\n" +
	"\x03mrn\x18\x01 \x01(\tR\x03mrn\x12@\n" +
	"\x06action\x18\x02 \x01(\x0e2(.cnquery.explorer.AssignmentDelta.ActionR\x06action\"*\n" +
	"\x06Action\x12\v\n" +
	"\aUNKNOWN\x10\x00\x12\a\n" +
	"\x03ADD\x10\x01\x12\n" +
	"\n" +
	"\x06DELETE\x10\x02\"\xdb\x01\n" +
	"\x13BundleMutationDelta\x12\x1b\n" +
	"\towner_mrn\x18\x01 \x01(\tR\bownerMrn\x12I\n" +
	"\x06deltas\x18\x02 \x03(\v21.cnquery.explorer.BundleMutationDelta.DeltasEntryR\x06deltas\x1a\\\n" +
	"\vDeltasEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x127\n" +
	"\x05value\x18\x02 \x01(\v2!.cnquery.explorer.AssignmentDeltaR\x05value:\x028\x01\"d\n" +
	"\x14SynchronizeAssetsReq\x12\x1b\n" +
	"\tspace_mrn\x18\x01 \x01(\tR\bspaceMrn\x12/\n" +
	"\x04list\x18\x02 \x03(\v2\x1b.cnquery.providers.v1.AssetR\x04list\"t\n" +
	" SynchronizeAssetsRespAssetDetail\x12!\n" +
	"\fplatform_mrn\x18\x01 \x01(\tR\vplatformMrn\x12\x1b\n" +
	"\tasset_mrn\x18\x02 \x01(\tR\bassetMrn\x12\x10\n" +
	"\x03url\x18\x03 \x01(\tR\x03url\"\xd7\x01\n" +
	"\x15SynchronizeAssetsResp\x12N\n" +
	"\adetails\x18\x01 \x03(\v24.cnquery.explorer.SynchronizeAssetsResp.DetailsEntryR\adetails\x1an\n" +
	"\fDetailsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12H\n" +
	"\x05value\x18\x02 \x01(\v22.cnquery.explorer.SynchronizeAssetsRespAssetDetailR\x05value:\x028\x01\"#\n" +
	"\vImpactValue\x12\x14\n" +
	"\x05value\x18\x01 \x01(\x05R\x05value*a\n" +
	"\x06Action\x12\x0f\n" +
	"\vUNSPECIFIED\x10\x00\x12\n" +
	"\n" +
	"\x06MODIFY\x10\x01\x12\x0e\n" +
	"\n" +
	"DEACTIVATE\x10\x02\x12\f\n" +
	"\bACTIVATE\x10\x03\x12\n" +
	"\n" +
	"\x06IGNORE\x10\x04\x12\x10\n" +
	"\fOUT_OF_SCOPE\x10\x05*\x96\x01\n" +
	"\rScoringSystem\x12\x17\n" +
	"\x13SCORING_UNSPECIFIED\x10\x00\x12\f\n" +
	"\bWEIGHTED\x10\x01\x12\t\n" +
	"\x05WORST\x10\x02\x12\v\n" +
	"\aAVERAGE\x10\x03\x12\r\n" +
	"\tDATA_ONLY\x10\x04\x12\x10\n" +
	"\fIGNORE_SCORE\x10\x05\x12\n" +
	"\n" +
	"\x06BANDED\x10\x06\x12\v\n" +
	"\aDECAYED\x10\a\x12\f\n" +
	"\bDISABLED\x10\b2\xb1\x04\n" +
	"\bQueryHub\x12@\n" +
	"\tSetBundle\x12\x18.cnquery.explorer.Bundle\x1a\x17.cnquery.explorer.Empty\"\x00\x12C\n" +
	"\x0fDeleteQueryPack\x12\x15.cnquery.explorer.Mrn\x1a\x17.cnquery.explorer.Empty\"\x00\x12E\n" +
	"\x0eValidateBundle\x12\x18.cnquery.explorer.Bundle\x1a\x17.cnquery.explorer.Empty\"\x00\x12>\n" +
	"\tGetBundle\x12\x15.cnquery.explorer.Mrn\x1a\x18.cnquery.explorer.Bundle\"\x00\x12D\n" +
	"\fGetQueryPack\x12\x15.cnquery.explorer.Mrn\x1a\x1b.cnquery.explorer.QueryPack\"\x00\x12A\n" +
	"\n" +
	"GetFilters\x12\x15.cnquery.explorer.Mrn\x1a\x1a.cnquery.explorer.Mqueries\"\x00\x12A\n" +
	"\x04List\x12\x19.cnquery.explorer.ListReq\x1a\x1c.cnquery.explorer.QueryPacks\"\x00\x12K\n" +
	"\fDefaultPacks\x12!.cnquery.explorer.DefaultPacksReq\x1a\x16.cnquery.explorer.URLs\"\x002\xaa\x04\n" +
	"\x0eQueryConductor\x12A\n" +
	"\x06Assign\x12\x1c.cnquery.explorer.Assignment\x1a\x17.cnquery.explorer.Empty\"\x00\x12C\n" +
	"\bUnassign\x12\x1c.cnquery.explorer.Assignment\x1a\x17.cnquery.explorer.Empty\"\x00\x12A\n" +
	"\bSetProps\x12\x1a.cnquery.explorer.PropsReq\x1a\x17.cnquery.explorer.Empty\"\x00\x12I\n" +
	"\aResolve\x12\x1c.cnquery.explorer.ResolveReq\x1a\x1e.cnquery.explorer.ResolvedPack\"\x00\x12L\n" +
	"\fStoreResults\x12!.cnquery.explorer.StoreResultsReq\x1a\x17.cnquery.explorer.Empty\"\x00\x12L\n" +
	"\tGetReport\x12#.cnquery.explorer.EntityDataRequest\x1a\x18.cnquery.explorer.Report\"\x00\x12f\n" +
	"\x11SynchronizeAssets\x12&.cnquery.explorer.SynchronizeAssetsReq\x1a'.cnquery.explorer.SynchronizeAssetsResp\"\x00B$Z\"go.mondoo.com/cnquery/v11/explorerb\x06proto3"

var (
	file_cnquery_explorer_proto_rawDescOnce sync.Once
	file_cnquery_explorer_proto_rawDescData []byte
)

func file_cnquery_explorer_proto_rawDescGZIP() []byte {
	file_cnquery_explorer_proto_rawDescOnce.Do(func() {
		file_cnquery_explorer_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_cnquery_explorer_proto_rawDesc), len(file_cnquery_explorer_proto_rawDesc)))
	})
	return file_cnquery_explorer_proto_rawDescData
}

var file_cnquery_explorer_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_cnquery_explorer_proto_msgTypes = make([]protoimpl.MessageInfo, 60)
var file_cnquery_explorer_proto_goTypes = []any{
	(Action)(0),                              // 0: cnquery.explorer.Action
	(ScoringSystem)(0),                       // 1: cnquery.explorer.ScoringSystem
	(AssignmentDelta_Action)(0),              // 2: cnquery.explorer.AssignmentDelta.Action
	(*Bundle)(nil),                           // 3: cnquery.explorer.Bundle
	(*QueryGroup)(nil),                       // 4: cnquery.explorer.QueryGroup
	(*QueryPack)(nil),                        // 5: cnquery.explorer.QueryPack
	(*ObjectRef)(nil),                        // 6: cnquery.explorer.ObjectRef
	(*Filters)(nil),                          // 7: cnquery.explorer.Filters
	(*QueryPacks)(nil),                       // 8: cnquery.explorer.QueryPacks
	(*Docs)(nil),                             // 9: cnquery.explorer.Docs
	(*Property)(nil),                         // 10: cnquery.explorer.Property
	(*Mquery)(nil),                           // 11: cnquery.explorer.Mquery
	(*Impact)(nil),                           // 12: cnquery.explorer.Impact
	(*QueryPackDocs)(nil),                    // 13: cnquery.explorer.QueryPackDocs
	(*MqueryDocs)(nil),                       // 14: cnquery.explorer.MqueryDocs
	(*Remediation)(nil),                      // 15: cnquery.explorer.Remediation
	(*TypedDoc)(nil),                         // 16: cnquery.explorer.TypedDoc
	(*Author)(nil),                           // 17: cnquery.explorer.Author
	(*MqueryRef)(nil),                        // 18: cnquery.explorer.MqueryRef
	(*ExecutionJob)(nil),                     // 19: cnquery.explorer.ExecutionJob
	(*DataQueryInfo)(nil),                    // 20: cnquery.explorer.DataQueryInfo
	(*ExecutionQuery)(nil),                   // 21: cnquery.explorer.ExecutionQuery
	(*Empty)(nil),                            // 22: cnquery.explorer.Empty
	(*Mrn)(nil),                              // 23: cnquery.explorer.Mrn
	(*Mqueries)(nil),                         // 24: cnquery.explorer.Mqueries
	(*ListReq)(nil),                          // 25: cnquery.explorer.ListReq
	(*DefaultPacksReq)(nil),                  // 26: cnquery.explorer.DefaultPacksReq
	(*URLs)(nil),                             // 27: cnquery.explorer.URLs
	(*Assignment)(nil),                       // 28: cnquery.explorer.Assignment
	(*PropsReq)(nil),                         // 29: cnquery.explorer.PropsReq
	(*ResolveReq)(nil),                       // 30: cnquery.explorer.ResolveReq
	(*ResolvedPack)(nil),                     // 31: cnquery.explorer.ResolvedPack
	(*UpdateAssetJobsReq)(nil),               // 32: cnquery.explorer.UpdateAssetJobsReq
	(*StoreResultsReq)(nil),                  // 33: cnquery.explorer.StoreResultsReq
	(*EntityDataRequest)(nil),                // 34: cnquery.explorer.EntityDataRequest
	(*Report)(nil),                           // 35: cnquery.explorer.Report
	(*Asset)(nil),                            // 36: cnquery.explorer.Asset
	(*ReportCollection)(nil),                 // 37: cnquery.explorer.ReportCollection
	(*ErrorStatus)(nil),                      // 38: cnquery.explorer.ErrorStatus
	(*AssignmentDelta)(nil),                  // 39: cnquery.explorer.AssignmentDelta
	(*BundleMutationDelta)(nil),              // 40: cnquery.explorer.BundleMutationDelta
	(*SynchronizeAssetsReq)(nil),             // 41: cnquery.explorer.SynchronizeAssetsReq
	(*SynchronizeAssetsRespAssetDetail)(nil), // 42: cnquery.explorer.SynchronizeAssetsRespAssetDetail
	(*SynchronizeAssetsResp)(nil),            // 43: cnquery.explorer.SynchronizeAssetsResp
	(*ImpactValue)(nil),                      // 44: cnquery.explorer.ImpactValue
	nil,                                      // 45: cnquery.explorer.QueryPack.TagsEntry
	nil,                                      // 46: cnquery.explorer.ObjectRef.TagsEntry
	nil,                                      // 47: cnquery.explorer.Filters.ItemsEntry
	nil,                                      // 48: cnquery.explorer.Mquery.TagsEntry
	nil,                                      // 49: cnquery.explorer.TypedDoc.TagsEntry
	nil,                                      // 50: cnquery.explorer.ExecutionJob.QueriesEntry
	nil,                                      // 51: cnquery.explorer.ExecutionJob.DatapointsEntry
	nil,                                      // 52: cnquery.explorer.ExecutionQuery.PropertiesEntry
	nil,                                      // 53: cnquery.explorer.StoreResultsReq.DataEntry
	nil,                                      // 54: cnquery.explorer.StoreResultsReq.ResourcesEntry
	nil,                                      // 55: cnquery.explorer.Report.DataEntry
	nil,                                      // 56: cnquery.explorer.Asset.LabelsEntry
	nil,                                      // 57: cnquery.explorer.ReportCollection.AssetsEntry
	nil,                                      // 58: cnquery.explorer.ReportCollection.ReportsEntry
	nil,                                      // 59: cnquery.explorer.ReportCollection.ErrorsEntry
	nil,                                      // 60: cnquery.explorer.ReportCollection.ResolvedEntry
	nil,                                      // 61: cnquery.explorer.BundleMutationDelta.DeltasEntry
	nil,                                      // 62: cnquery.explorer.SynchronizeAssetsResp.DetailsEntry
	(*llx.CodeBundle)(nil),                   // 63: cnquery.llx.CodeBundle
	(*anypb.Any)(nil),                        // 64: google.protobuf.Any
	(*inventory.Asset)(nil),                  // 65: cnquery.providers.v1.Asset
	(*llx.Result)(nil),                       // 66: cnquery.llx.Result
	(*llx.ResourceRecording)(nil),            // 67: cnquery.llx.ResourceRecording
}
var file_cnquery_explorer_proto_depIdxs = []int32{
	5,  // 0: cnquery.explorer.Bundle.packs:type_name -> cnquery.explorer.QueryPack
	11, // 1: cnquery.explorer.Bundle.queries:type_name -> cnquery.explorer.Mquery
	10, // 2: cnquery.explorer.Bundle.props:type_name -> cnquery.explorer.Property
	11, // 3: cnquery.explorer.QueryGroup.queries:type_name -> cnquery.explorer.Mquery
	7,  // 4: cnquery.explorer.QueryGroup.filters:type_name -> cnquery.explorer.Filters
	11, // 5: cnquery.explorer.QueryPack.queries:type_name -> cnquery.explorer.Mquery
	4,  // 6: cnquery.explorer.QueryPack.groups:type_name -> cnquery.explorer.QueryGroup
	10, // 7: cnquery.explorer.QueryPack.props:type_name -> cnquery.explorer.Property
	7,  // 8: cnquery.explorer.QueryPack.computed_filters:type_name -> cnquery.explorer.Filters
	7,  // 9: cnquery.explorer.QueryPack.filters:type_name -> cnquery.explorer.Filters
	13, // 10: cnquery.explorer.QueryPack.docs:type_name -> cnquery.explorer.QueryPackDocs
	17, // 11: cnquery.explorer.QueryPack.authors:type_name -> cnquery.explorer.Author
	45, // 12: cnquery.explorer.QueryPack.tags:type_name -> cnquery.explorer.QueryPack.TagsEntry
	46, // 13: cnquery.explorer.ObjectRef.tags:type_name -> cnquery.explorer.ObjectRef.TagsEntry
	47, // 14: cnquery.explorer.Filters.items:type_name -> cnquery.explorer.Filters.ItemsEntry
	5,  // 15: cnquery.explorer.QueryPacks.items:type_name -> cnquery.explorer.QueryPack
	18, // 16: cnquery.explorer.Docs.refs:type_name -> cnquery.explorer.MqueryRef
	6,  // 17: cnquery.explorer.Property.for:type_name -> cnquery.explorer.ObjectRef
	18, // 18: cnquery.explorer.Mquery.refs:type_name -> cnquery.explorer.MqueryRef
	14, // 19: cnquery.explorer.Mquery.docs:type_name -> cnquery.explorer.MqueryDocs
	12, // 20: cnquery.explorer.Mquery.impact:type_name -> cnquery.explorer.Impact
	48, // 21: cnquery.explorer.Mquery.tags:type_name -> cnquery.explorer.Mquery.TagsEntry
	7,  // 22: cnquery.explorer.Mquery.filters:type_name -> cnquery.explorer.Filters
	10, // 23: cnquery.explorer.Mquery.props:type_name -> cnquery.explorer.Property
	6,  // 24: cnquery.explorer.Mquery.variants:type_name -> cnquery.explorer.ObjectRef
	0,  // 25: cnquery.explorer.Mquery.action:type_name -> cnquery.explorer.Action
	44, // 26: cnquery.explorer.Impact.value:type_name -> cnquery.explorer.ImpactValue
	1,  // 27: cnquery.explorer.Impact.scoring:type_name -> cnquery.explorer.ScoringSystem
	0,  // 28: cnquery.explorer.Impact.action:type_name -> cnquery.explorer.Action
	18, // 29: cnquery.explorer.MqueryDocs.refs:type_name -> cnquery.explorer.MqueryRef
	15, // 30: cnquery.explorer.MqueryDocs.remediation:type_name -> cnquery.explorer.Remediation
	16, // 31: cnquery.explorer.Remediation.items:type_name -> cnquery.explorer.TypedDoc
	49, // 32: cnquery.explorer.TypedDoc.tags:type_name -> cnquery.explorer.TypedDoc.TagsEntry
	50, // 33: cnquery.explorer.ExecutionJob.queries:type_name -> cnquery.explorer.ExecutionJob.QueriesEntry
	51, // 34: cnquery.explorer.ExecutionJob.datapoints:type_name -> cnquery.explorer.ExecutionJob.DatapointsEntry
	52, // 35: cnquery.explorer.ExecutionQuery.properties:type_name -> cnquery.explorer.ExecutionQuery.PropertiesEntry
	63, // 36: cnquery.explorer.ExecutionQuery.code:type_name -> cnquery.llx.CodeBundle
	11, // 37: cnquery.explorer.Mqueries.items:type_name -> cnquery.explorer.Mquery
	10, // 38: cnquery.explorer.PropsReq.props:type_name -> cnquery.explorer.Property
	11, // 39: cnquery.explorer.ResolveReq.asset_filters:type_name -> cnquery.explorer.Mquery
	19, // 40: cnquery.explorer.ResolvedPack.execution_job:type_name -> cnquery.explorer.ExecutionJob
	11, // 41: cnquery.explorer.ResolvedPack.filters:type_name -> cnquery.explorer.Mquery
	11, // 42: cnquery.explorer.UpdateAssetJobsReq.asset_filters:type_name -> cnquery.explorer.Mquery
	53, // 43: cnquery.explorer.StoreResultsReq.data:type_name -> cnquery.explorer.StoreResultsReq.DataEntry
	54, // 44: cnquery.explorer.StoreResultsReq.resources:type_name -> cnquery.explorer.StoreResultsReq.ResourcesEntry
	55, // 45: cnquery.explorer.Report.data:type_name -> cnquery.explorer.Report.DataEntry
	56, // 46: cnquery.explorer.Asset.labels:type_name -> cnquery.explorer.Asset.LabelsEntry
	57, // 47: cnquery.explorer.ReportCollection.assets:type_name -> cnquery.explorer.ReportCollection.AssetsEntry
	3,  // 48: cnquery.explorer.ReportCollection.bundle:type_name -> cnquery.explorer.Bundle
	58, // 49: cnquery.explorer.ReportCollection.reports:type_name -> cnquery.explorer.ReportCollection.ReportsEntry
	59, // 50: cnquery.explorer.ReportCollection.errors:type_name -> cnquery.explorer.ReportCollection.ErrorsEntry
	60, // 51: cnquery.explorer.ReportCollection.resolved:type_name -> cnquery.explorer.ReportCollection.ResolvedEntry
	64, // 52: cnquery.explorer.ErrorStatus.details:type_name -> google.protobuf.Any
	2,  // 53: cnquery.explorer.AssignmentDelta.action:type_name -> cnquery.explorer.AssignmentDelta.Action
	61, // 54: cnquery.explorer.BundleMutationDelta.deltas:type_name -> cnquery.explorer.BundleMutationDelta.DeltasEntry
	65, // 55: cnquery.explorer.SynchronizeAssetsReq.list:type_name -> cnquery.providers.v1.Asset
	62, // 56: cnquery.explorer.SynchronizeAssetsResp.details:type_name -> cnquery.explorer.SynchronizeAssetsResp.DetailsEntry
	11, // 57: cnquery.explorer.Filters.ItemsEntry.value:type_name -> cnquery.explorer.Mquery
	21, // 58: cnquery.explorer.ExecutionJob.QueriesEntry.value:type_name -> cnquery.explorer.ExecutionQuery
	20, // 59: cnquery.explorer.ExecutionJob.DatapointsEntry.value:type_name -> cnquery.explorer.DataQueryInfo
	66, // 60: cnquery.explorer.StoreResultsReq.DataEntry.value:type_name -> cnquery.llx.Result
	67, // 61: cnquery.explorer.StoreResultsReq.ResourcesEntry.value:type_name -> cnquery.llx.ResourceRecording
	66, // 62: cnquery.explorer.Report.DataEntry.value:type_name -> cnquery.llx.Result
	36, // 63: cnquery.explorer.ReportCollection.AssetsEntry.value:type_name -> cnquery.explorer.Asset
	35, // 64: cnquery.explorer.ReportCollection.ReportsEntry.value:type_name -> cnquery.explorer.Report
	38, // 65: cnquery.explorer.ReportCollection.ErrorsEntry.value:type_name -> cnquery.explorer.ErrorStatus
	31, // 66: cnquery.explorer.ReportCollection.ResolvedEntry.value:type_name -> cnquery.explorer.ResolvedPack
	39, // 67: cnquery.explorer.BundleMutationDelta.DeltasEntry.value:type_name -> cnquery.explorer.AssignmentDelta
	42, // 68: cnquery.explorer.SynchronizeAssetsResp.DetailsEntry.value:type_name -> cnquery.explorer.SynchronizeAssetsRespAssetDetail
	3,  // 69: cnquery.explorer.QueryHub.SetBundle:input_type -> cnquery.explorer.Bundle
	23, // 70: cnquery.explorer.QueryHub.DeleteQueryPack:input_type -> cnquery.explorer.Mrn
	3,  // 71: cnquery.explorer.QueryHub.ValidateBundle:input_type -> cnquery.explorer.Bundle
	23, // 72: cnquery.explorer.QueryHub.GetBundle:input_type -> cnquery.explorer.Mrn
	23, // 73: cnquery.explorer.QueryHub.GetQueryPack:input_type -> cnquery.explorer.Mrn
	23, // 74: cnquery.explorer.QueryHub.GetFilters:input_type -> cnquery.explorer.Mrn
	25, // 75: cnquery.explorer.QueryHub.List:input_type -> cnquery.explorer.ListReq
	26, // 76: cnquery.explorer.QueryHub.DefaultPacks:input_type -> cnquery.explorer.DefaultPacksReq
	28, // 77: cnquery.explorer.QueryConductor.Assign:input_type -> cnquery.explorer.Assignment
	28, // 78: cnquery.explorer.QueryConductor.Unassign:input_type -> cnquery.explorer.Assignment
	29, // 79: cnquery.explorer.QueryConductor.SetProps:input_type -> cnquery.explorer.PropsReq
	30, // 80: cnquery.explorer.QueryConductor.Resolve:input_type -> cnquery.explorer.ResolveReq
	33, // 81: cnquery.explorer.QueryConductor.StoreResults:input_type -> cnquery.explorer.StoreResultsReq
	34, // 82: cnquery.explorer.QueryConductor.GetReport:input_type -> cnquery.explorer.EntityDataRequest
	41, // 83: cnquery.explorer.QueryConductor.SynchronizeAssets:input_type -> cnquery.explorer.SynchronizeAssetsReq
	22, // 84: cnquery.explorer.QueryHub.SetBundle:output_type -> cnquery.explorer.Empty
	22, // 85: cnquery.explorer.QueryHub.DeleteQueryPack:output_type -> cnquery.explorer.Empty
	22, // 86: cnquery.explorer.QueryHub.ValidateBundle:output_type -> cnquery.explorer.Empty
	3,  // 87: cnquery.explorer.QueryHub.GetBundle:output_type -> cnquery.explorer.Bundle
	5,  // 88: cnquery.explorer.QueryHub.GetQueryPack:output_type -> cnquery.explorer.QueryPack
	24, // 89: cnquery.explorer.QueryHub.GetFilters:output_type -> cnquery.explorer.Mqueries
	8,  // 90: cnquery.explorer.QueryHub.List:output_type -> cnquery.explorer.QueryPacks
	27, // 91: cnquery.explorer.QueryHub.DefaultPacks:output_type -> cnquery.explorer.URLs
	22, // 92: cnquery.explorer.QueryConductor.Assign:output_type -> cnquery.explorer.Empty
	22, // 93: cnquery.explorer.QueryConductor.Unassign:output_type -> cnquery.explorer.Empty
	22, // 94: cnquery.explorer.QueryConductor.SetProps:output_type -> cnquery.explorer.Empty
	31, // 95: cnquery.explorer.QueryConductor.Resolve:output_type -> cnquery.explorer.ResolvedPack
	22, // 96: cnquery.explorer.QueryConductor.StoreResults:output_type -> cnquery.explorer.Empty
	35, // 97: cnquery.explorer.QueryConductor.GetReport:output_type -> cnquery.explorer.Report
	43, // 98: cnquery.explorer.QueryConductor.SynchronizeAssets:output_type -> cnquery.explorer.SynchronizeAssetsResp
	84, // [84:99] is the sub-list for method output_type
	69, // [69:84] is the sub-list for method input_type
	69, // [69:69] is the sub-list for extension type_name
	69, // [69:69] is the sub-list for extension extendee
	0,  // [0:69] is the sub-list for field type_name
}

func init() { file_cnquery_explorer_proto_init() }
func file_cnquery_explorer_proto_init() {
	if File_cnquery_explorer_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_cnquery_explorer_proto_rawDesc), len(file_cnquery_explorer_proto_rawDesc)),
			NumEnums:      3,
			NumMessages:   60,
			NumExtensions: 0,
			NumServices:   2,
		},
		GoTypes:           file_cnquery_explorer_proto_goTypes,
		DependencyIndexes: file_cnquery_explorer_proto_depIdxs,
		EnumInfos:         file_cnquery_explorer_proto_enumTypes,
		MessageInfos:      file_cnquery_explorer_proto_msgTypes,
	}.Build()
	File_cnquery_explorer_proto = out.File
	file_cnquery_explorer_proto_goTypes = nil
	file_cnquery_explorer_proto_depIdxs = nil
}
