// Code generated by protoc-gen-rangerrpc version DO NOT EDIT.
// source: cnquery_resources_explorer.proto

package resources

import (
	"context"
	"errors"
	"net/http"
	"net/url"
	"strings"

	ranger "go.mondoo.com/ranger-rpc"
	"go.mondoo.com/ranger-rpc/metadata"
	jsonpb "google.golang.org/protobuf/encoding/protojson"
	pb "google.golang.org/protobuf/proto"
)

// service interface definition

type ResourcesExplorer interface {
	GetResourcesData(context.Context, *EntityResourcesReq) (*EntityResourcesRes, error)
	ListResources(context.Context, *ListResourcesReq) (*ListResourcesRes, error)
}

// client implementation

type ResourcesExplorerClient struct {
	ranger.Client
	httpclient ranger.HTTPClient
	prefix     string
}

func NewResourcesExplorerClient(addr string, client ranger.HTTPClient, plugins ...ranger.ClientPlugin) (*ResourcesExplorerClient, error) {
	base, err := url.Parse(ranger.SanitizeUrl(addr))
	if err != nil {
		return nil, err
	}

	u, err := url.Parse("./ResourcesExplorer")
	if err != nil {
		return nil, err
	}

	serviceClient := &ResourcesExplorerClient{
		httpclient: client,
		prefix:     base.ResolveReference(u).String(),
	}
	serviceClient.AddPlugins(plugins...)
	return serviceClient, nil
}
func (c *ResourcesExplorerClient) GetResourcesData(ctx context.Context, in *EntityResourcesReq) (*EntityResourcesRes, error) {
	out := new(EntityResourcesRes)
	err := c.DoClientRequest(ctx, c.httpclient, strings.Join([]string{c.prefix, "/GetResourcesData"}, ""), in, out)
	return out, err
}
func (c *ResourcesExplorerClient) ListResources(ctx context.Context, in *ListResourcesReq) (*ListResourcesRes, error) {
	out := new(ListResourcesRes)
	err := c.DoClientRequest(ctx, c.httpclient, strings.Join([]string{c.prefix, "/ListResources"}, ""), in, out)
	return out, err
}

// server implementation

type ResourcesExplorerServerOption func(s *ResourcesExplorerServer)

func WithUnknownFieldsForResourcesExplorerServer() ResourcesExplorerServerOption {
	return func(s *ResourcesExplorerServer) {
		s.allowUnknownFields = true
	}
}

func NewResourcesExplorerServer(handler ResourcesExplorer, opts ...ResourcesExplorerServerOption) http.Handler {
	srv := &ResourcesExplorerServer{
		handler: handler,
	}

	for i := range opts {
		opts[i](srv)
	}

	service := ranger.Service{
		Name: "ResourcesExplorer",
		Methods: map[string]ranger.Method{
			"GetResourcesData": srv.GetResourcesData,
			"ListResources":    srv.ListResources,
		},
	}
	return ranger.NewRPCServer(&service)
}

type ResourcesExplorerServer struct {
	handler            ResourcesExplorer
	allowUnknownFields bool
}

func (p *ResourcesExplorerServer) GetResourcesData(ctx context.Context, reqBytes *[]byte) (pb.Message, error) {
	var req EntityResourcesReq
	var err error

	md, ok := metadata.FromIncomingContext(ctx)
	if !ok {
		return nil, errors.New("could not access header")
	}

	switch md.First("Content-Type") {
	case "application/protobuf", "application/octet-stream", "application/grpc+proto":
		err = pb.Unmarshal(*reqBytes, &req)
	default:
		// handle case of empty object
		if len(*reqBytes) > 0 {
			err = jsonpb.UnmarshalOptions{DiscardUnknown: true}.Unmarshal(*reqBytes, &req)
		}
	}

	if err != nil {
		return nil, err
	}
	return p.handler.GetResourcesData(ctx, &req)
}
func (p *ResourcesExplorerServer) ListResources(ctx context.Context, reqBytes *[]byte) (pb.Message, error) {
	var req ListResourcesReq
	var err error

	md, ok := metadata.FromIncomingContext(ctx)
	if !ok {
		return nil, errors.New("could not access header")
	}

	switch md.First("Content-Type") {
	case "application/protobuf", "application/octet-stream", "application/grpc+proto":
		err = pb.Unmarshal(*reqBytes, &req)
	default:
		// handle case of empty object
		if len(*reqBytes) > 0 {
			err = jsonpb.UnmarshalOptions{DiscardUnknown: true}.Unmarshal(*reqBytes, &req)
		}
	}

	if err != nil {
		return nil, err
	}
	return p.handler.ListResources(ctx, &req)
}
