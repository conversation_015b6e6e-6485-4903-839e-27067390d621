// Copyright (c) Mondoo, Inc.
// SPDX-License-Identifier: BUSL-1.1

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: cnquery_resources_explorer.proto

package resources

import (
	llx "go.mondoo.com/cnquery/v11/llx"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type EntityResourcesReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	EntityMrn     string                 `protobuf:"bytes,1,opt,name=entity_mrn,json=entityMrn,proto3" json:"entity_mrn,omitempty"`
	Resources     []*ResourceDataReq     `protobuf:"bytes,2,rep,name=resources,proto3" json:"resources,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EntityResourcesReq) Reset() {
	*x = EntityResourcesReq{}
	mi := &file_cnquery_resources_explorer_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EntityResourcesReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EntityResourcesReq) ProtoMessage() {}

func (x *EntityResourcesReq) ProtoReflect() protoreflect.Message {
	mi := &file_cnquery_resources_explorer_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EntityResourcesReq.ProtoReflect.Descriptor instead.
func (*EntityResourcesReq) Descriptor() ([]byte, []int) {
	return file_cnquery_resources_explorer_proto_rawDescGZIP(), []int{0}
}

func (x *EntityResourcesReq) GetEntityMrn() string {
	if x != nil {
		return x.EntityMrn
	}
	return ""
}

func (x *EntityResourcesReq) GetResources() []*ResourceDataReq {
	if x != nil {
		return x.Resources
	}
	return nil
}

type ResourceDataReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Resource      string                 `protobuf:"bytes,1,opt,name=resource,proto3" json:"resource,omitempty"`
	Id            string                 `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
	Fields        []string               `protobuf:"bytes,3,rep,name=fields,proto3" json:"fields,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ResourceDataReq) Reset() {
	*x = ResourceDataReq{}
	mi := &file_cnquery_resources_explorer_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ResourceDataReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResourceDataReq) ProtoMessage() {}

func (x *ResourceDataReq) ProtoReflect() protoreflect.Message {
	mi := &file_cnquery_resources_explorer_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResourceDataReq.ProtoReflect.Descriptor instead.
func (*ResourceDataReq) Descriptor() ([]byte, []int) {
	return file_cnquery_resources_explorer_proto_rawDescGZIP(), []int{1}
}

func (x *ResourceDataReq) GetResource() string {
	if x != nil {
		return x.Resource
	}
	return ""
}

func (x *ResourceDataReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ResourceDataReq) GetFields() []string {
	if x != nil {
		return x.Fields
	}
	return nil
}

type EntityResourcesRes struct {
	state         protoimpl.MessageState   `protogen:"open.v1"`
	EntityMrn     string                   `protobuf:"bytes,1,opt,name=entity_mrn,json=entityMrn,proto3" json:"entity_mrn,omitempty"`
	Resources     []*llx.ResourceRecording `protobuf:"bytes,4,rep,name=resources,proto3" json:"resources,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EntityResourcesRes) Reset() {
	*x = EntityResourcesRes{}
	mi := &file_cnquery_resources_explorer_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EntityResourcesRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EntityResourcesRes) ProtoMessage() {}

func (x *EntityResourcesRes) ProtoReflect() protoreflect.Message {
	mi := &file_cnquery_resources_explorer_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EntityResourcesRes.ProtoReflect.Descriptor instead.
func (*EntityResourcesRes) Descriptor() ([]byte, []int) {
	return file_cnquery_resources_explorer_proto_rawDescGZIP(), []int{2}
}

func (x *EntityResourcesRes) GetEntityMrn() string {
	if x != nil {
		return x.EntityMrn
	}
	return ""
}

func (x *EntityResourcesRes) GetResources() []*llx.ResourceRecording {
	if x != nil {
		return x.Resources
	}
	return nil
}

type ListResourcesReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	EntityMrn     string                 `protobuf:"bytes,1,opt,name=entity_mrn,json=entityMrn,proto3" json:"entity_mrn,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListResourcesReq) Reset() {
	*x = ListResourcesReq{}
	mi := &file_cnquery_resources_explorer_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListResourcesReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListResourcesReq) ProtoMessage() {}

func (x *ListResourcesReq) ProtoReflect() protoreflect.Message {
	mi := &file_cnquery_resources_explorer_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListResourcesReq.ProtoReflect.Descriptor instead.
func (*ListResourcesReq) Descriptor() ([]byte, []int) {
	return file_cnquery_resources_explorer_proto_rawDescGZIP(), []int{3}
}

func (x *ListResourcesReq) GetEntityMrn() string {
	if x != nil {
		return x.EntityMrn
	}
	return ""
}

type ListResourcesRes struct {
	state     protoimpl.MessageState `protogen:"open.v1"`
	EntityMrn string                 `protobuf:"bytes,1,opt,name=entity_mrn,json=entityMrn,proto3" json:"entity_mrn,omitempty"`
	// Resources for this entity, limited to their name + ID.
	// If you want to access their data, look at EntityResourcesRes
	Resources     []*llx.ResourceRecording `protobuf:"bytes,4,rep,name=resources,proto3" json:"resources,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListResourcesRes) Reset() {
	*x = ListResourcesRes{}
	mi := &file_cnquery_resources_explorer_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListResourcesRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListResourcesRes) ProtoMessage() {}

func (x *ListResourcesRes) ProtoReflect() protoreflect.Message {
	mi := &file_cnquery_resources_explorer_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListResourcesRes.ProtoReflect.Descriptor instead.
func (*ListResourcesRes) Descriptor() ([]byte, []int) {
	return file_cnquery_resources_explorer_proto_rawDescGZIP(), []int{4}
}

func (x *ListResourcesRes) GetEntityMrn() string {
	if x != nil {
		return x.EntityMrn
	}
	return ""
}

func (x *ListResourcesRes) GetResources() []*llx.ResourceRecording {
	if x != nil {
		return x.Resources
	}
	return nil
}

var File_cnquery_resources_explorer_proto protoreflect.FileDescriptor

const file_cnquery_resources_explorer_proto_rawDesc = "" +
	"\n" +
	" cnquery_resources_explorer.proto\x12\x1acnquery.explorer.resources\x1a\rllx/llx.proto\"~\n" +
	"\x12EntityResourcesReq\x12\x1d\n" +
	"\n" +
	"entity_mrn\x18\x01 \x01(\tR\tentityMrn\x12I\n" +
	"\tresources\x18\x02 \x03(\v2+.cnquery.explorer.resources.ResourceDataReqR\tresources\"U\n" +
	"\x0fResourceDataReq\x12\x1a\n" +
	"\bresource\x18\x01 \x01(\tR\bresource\x12\x0e\n" +
	"\x02id\x18\x02 \x01(\tR\x02id\x12\x16\n" +
	"\x06fields\x18\x03 \x03(\tR\x06fields\"q\n" +
	"\x12EntityResourcesRes\x12\x1d\n" +
	"\n" +
	"entity_mrn\x18\x01 \x01(\tR\tentityMrn\x12<\n" +
	"\tresources\x18\x04 \x03(\v2\x1e.cnquery.llx.ResourceRecordingR\tresources\"1\n" +
	"\x10ListResourcesReq\x12\x1d\n" +
	"\n" +
	"entity_mrn\x18\x01 \x01(\tR\tentityMrn\"o\n" +
	"\x10ListResourcesRes\x12\x1d\n" +
	"\n" +
	"entity_mrn\x18\x01 \x01(\tR\tentityMrn\x12<\n" +
	"\tresources\x18\x04 \x03(\v2\x1e.cnquery.llx.ResourceRecordingR\tresources2\xf8\x01\n" +
	"\x11ResourcesExplorer\x12t\n" +
	"\x10GetResourcesData\x12..cnquery.explorer.resources.EntityResourcesReq\x1a..cnquery.explorer.resources.EntityResourcesRes\"\x00\x12m\n" +
	"\rListResources\x12,.cnquery.explorer.resources.ListResourcesReq\x1a,.cnquery.explorer.resources.ListResourcesRes\"\x00B.Z,go.mondoo.com/cnquery/v11/explorer/resourcesb\x06proto3"

var (
	file_cnquery_resources_explorer_proto_rawDescOnce sync.Once
	file_cnquery_resources_explorer_proto_rawDescData []byte
)

func file_cnquery_resources_explorer_proto_rawDescGZIP() []byte {
	file_cnquery_resources_explorer_proto_rawDescOnce.Do(func() {
		file_cnquery_resources_explorer_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_cnquery_resources_explorer_proto_rawDesc), len(file_cnquery_resources_explorer_proto_rawDesc)))
	})
	return file_cnquery_resources_explorer_proto_rawDescData
}

var file_cnquery_resources_explorer_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_cnquery_resources_explorer_proto_goTypes = []any{
	(*EntityResourcesReq)(nil),    // 0: cnquery.explorer.resources.EntityResourcesReq
	(*ResourceDataReq)(nil),       // 1: cnquery.explorer.resources.ResourceDataReq
	(*EntityResourcesRes)(nil),    // 2: cnquery.explorer.resources.EntityResourcesRes
	(*ListResourcesReq)(nil),      // 3: cnquery.explorer.resources.ListResourcesReq
	(*ListResourcesRes)(nil),      // 4: cnquery.explorer.resources.ListResourcesRes
	(*llx.ResourceRecording)(nil), // 5: cnquery.llx.ResourceRecording
}
var file_cnquery_resources_explorer_proto_depIdxs = []int32{
	1, // 0: cnquery.explorer.resources.EntityResourcesReq.resources:type_name -> cnquery.explorer.resources.ResourceDataReq
	5, // 1: cnquery.explorer.resources.EntityResourcesRes.resources:type_name -> cnquery.llx.ResourceRecording
	5, // 2: cnquery.explorer.resources.ListResourcesRes.resources:type_name -> cnquery.llx.ResourceRecording
	0, // 3: cnquery.explorer.resources.ResourcesExplorer.GetResourcesData:input_type -> cnquery.explorer.resources.EntityResourcesReq
	3, // 4: cnquery.explorer.resources.ResourcesExplorer.ListResources:input_type -> cnquery.explorer.resources.ListResourcesReq
	2, // 5: cnquery.explorer.resources.ResourcesExplorer.GetResourcesData:output_type -> cnquery.explorer.resources.EntityResourcesRes
	4, // 6: cnquery.explorer.resources.ResourcesExplorer.ListResources:output_type -> cnquery.explorer.resources.ListResourcesRes
	5, // [5:7] is the sub-list for method output_type
	3, // [3:5] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_cnquery_resources_explorer_proto_init() }
func file_cnquery_resources_explorer_proto_init() {
	if File_cnquery_resources_explorer_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_cnquery_resources_explorer_proto_rawDesc), len(file_cnquery_resources_explorer_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_cnquery_resources_explorer_proto_goTypes,
		DependencyIndexes: file_cnquery_resources_explorer_proto_depIdxs,
		MessageInfos:      file_cnquery_resources_explorer_proto_msgTypes,
	}.Build()
	File_cnquery_resources_explorer_proto = out.File
	file_cnquery_resources_explorer_proto_goTypes = nil
	file_cnquery_resources_explorer_proto_depIdxs = nil
}
