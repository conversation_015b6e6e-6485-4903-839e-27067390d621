---
apiVersion: v1
kind: Pod
metadata:
  labels:
    admission-result: pass
  name: passing-pod-yaml
  namespace: default
spec:
  automountServiceAccountToken: false
  containers:
  - image: ubuntu:20.04
    imagePullPolicy: Always
    command: ["/bin/sh", "-c"]
    args: ["sleep 6000"]
    name: ubuntu
---
apiVersion: v1
kind: Pod
metadata:
  labels:
    admission-result: pass
  name: passing-pod-yaml-2
  namespace: default
spec:
  automountServiceAccountToken: false
  containers:
  - image: ubuntu:20.04
    imagePullPolicy: Always
    command: ["/bin/sh", "-c"]
    args: ["sleep 6000"]
    name: ubuntu
