// Copyright (c) Mondoo, Inc.
// SPDX-License-Identifier: BUSL-1.1

syntax = "proto3";

package cnquery.explorer.scan;

import "explorer/cnquery_explorer.proto";
import "providers-sdk/v1/inventory/inventory.proto";

option go_package = "go.mondoo.com/cnquery/v11/explorer/scan";

message Job {
  cnquery.providers.v1.Inventory inventory = 1;
  cnquery.explorer.Bundle bundle = 2;
  
  bool do_record = 20;
  repeated string query_pack_filters = 21;
  map<string,string> props = 22;
}

