// Copyright (c) Mondoo, Inc.
// SPDX-License-Identifier: BUSL-1.1

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: cnquery_explorer_scan.proto

package scan

import (
	explorer "go.mondoo.com/cnquery/v11/explorer"
	inventory "go.mondoo.com/cnquery/v11/providers-sdk/v1/inventory"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Job struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	Inventory        *inventory.Inventory   `protobuf:"bytes,1,opt,name=inventory,proto3" json:"inventory,omitempty"`
	Bundle           *explorer.Bundle       `protobuf:"bytes,2,opt,name=bundle,proto3" json:"bundle,omitempty"`
	DoRecord         bool                   `protobuf:"varint,20,opt,name=do_record,json=doRecord,proto3" json:"do_record,omitempty"`
	QueryPackFilters []string               `protobuf:"bytes,21,rep,name=query_pack_filters,json=queryPackFilters,proto3" json:"query_pack_filters,omitempty"`
	Props            map[string]string      `protobuf:"bytes,22,rep,name=props,proto3" json:"props,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *Job) Reset() {
	*x = Job{}
	mi := &file_cnquery_explorer_scan_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Job) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Job) ProtoMessage() {}

func (x *Job) ProtoReflect() protoreflect.Message {
	mi := &file_cnquery_explorer_scan_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Job.ProtoReflect.Descriptor instead.
func (*Job) Descriptor() ([]byte, []int) {
	return file_cnquery_explorer_scan_proto_rawDescGZIP(), []int{0}
}

func (x *Job) GetInventory() *inventory.Inventory {
	if x != nil {
		return x.Inventory
	}
	return nil
}

func (x *Job) GetBundle() *explorer.Bundle {
	if x != nil {
		return x.Bundle
	}
	return nil
}

func (x *Job) GetDoRecord() bool {
	if x != nil {
		return x.DoRecord
	}
	return false
}

func (x *Job) GetQueryPackFilters() []string {
	if x != nil {
		return x.QueryPackFilters
	}
	return nil
}

func (x *Job) GetProps() map[string]string {
	if x != nil {
		return x.Props
	}
	return nil
}

var File_cnquery_explorer_scan_proto protoreflect.FileDescriptor

const file_cnquery_explorer_scan_proto_rawDesc = "" +
	"\n" +
	"\x1bcnquery_explorer_scan.proto\x12\x15cnquery.explorer.scan\x1a\x1fexplorer/cnquery_explorer.proto\x1a*providers-sdk/v1/inventory/inventory.proto\"\xb8\x02\n" +
	"\x03Job\x12=\n" +
	"\tinventory\x18\x01 \x01(\v2\x1f.cnquery.providers.v1.InventoryR\tinventory\x120\n" +
	"\x06bundle\x18\x02 \x01(\v2\x18.cnquery.explorer.BundleR\x06bundle\x12\x1b\n" +
	"\tdo_record\x18\x14 \x01(\bR\bdoRecord\x12,\n" +
	"\x12query_pack_filters\x18\x15 \x03(\tR\x10queryPackFilters\x12;\n" +
	"\x05props\x18\x16 \x03(\v2%.cnquery.explorer.scan.Job.PropsEntryR\x05props\x1a8\n" +
	"\n" +
	"PropsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01B)Z'go.mondoo.com/cnquery/v11/explorer/scanb\x06proto3"

var (
	file_cnquery_explorer_scan_proto_rawDescOnce sync.Once
	file_cnquery_explorer_scan_proto_rawDescData []byte
)

func file_cnquery_explorer_scan_proto_rawDescGZIP() []byte {
	file_cnquery_explorer_scan_proto_rawDescOnce.Do(func() {
		file_cnquery_explorer_scan_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_cnquery_explorer_scan_proto_rawDesc), len(file_cnquery_explorer_scan_proto_rawDesc)))
	})
	return file_cnquery_explorer_scan_proto_rawDescData
}

var file_cnquery_explorer_scan_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_cnquery_explorer_scan_proto_goTypes = []any{
	(*Job)(nil),                 // 0: cnquery.explorer.scan.Job
	nil,                         // 1: cnquery.explorer.scan.Job.PropsEntry
	(*inventory.Inventory)(nil), // 2: cnquery.providers.v1.Inventory
	(*explorer.Bundle)(nil),     // 3: cnquery.explorer.Bundle
}
var file_cnquery_explorer_scan_proto_depIdxs = []int32{
	2, // 0: cnquery.explorer.scan.Job.inventory:type_name -> cnquery.providers.v1.Inventory
	3, // 1: cnquery.explorer.scan.Job.bundle:type_name -> cnquery.explorer.Bundle
	1, // 2: cnquery.explorer.scan.Job.props:type_name -> cnquery.explorer.scan.Job.PropsEntry
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_cnquery_explorer_scan_proto_init() }
func file_cnquery_explorer_scan_proto_init() {
	if File_cnquery_explorer_scan_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_cnquery_explorer_scan_proto_rawDesc), len(file_cnquery_explorer_scan_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_cnquery_explorer_scan_proto_goTypes,
		DependencyIndexes: file_cnquery_explorer_scan_proto_depIdxs,
		MessageInfos:      file_cnquery_explorer_scan_proto_msgTypes,
	}.Build()
	File_cnquery_explorer_scan_proto = out.File
	file_cnquery_explorer_scan_proto_goTypes = nil
	file_cnquery_explorer_scan_proto_depIdxs = nil
}
