# See https://github.com/check-spelling/check-spelling/wiki/Configuration-Examples:-patterns

# acceptable duplicates
# ls directory listings
[-bcdlpsw](?:[-r][-w][-sx]){3}\s+\d+\s+(\S+)\s+\g{-1}\s+\d+\s+

# Commit message -- Signed-off-by and friends
^\s*(?:(?:Based-on-patch|Co-authored|Helped|Mentored|Reported|Reviewed|Signed-off)-by|Thanks-to): (?:[^<]*<[^>]*>|[^<]*)\s*$

# Autogenerated revert commit message
^This reverts commit [0-9a-f]{40}\.$

# ignore long runs of a single character:
\b([A-Za-z])\g{-1}{3,}\b

# ignore funky space IDs that blow up spell checking
api\.mondoo\.app\/.*\b
console\.mondoo\.com\/.*\b

# azure subscription ID
[0-9A-Fa-f]{8}-([0-9A-Fa-f]{4}-){3}[0-9A-Fa-f]{12}

# azure subscriptions URL
\/subscriptions\/\S*

# docker container
\b[a-z,0-9]{12}\b

# URLs in markdown links / images
]\(.*\)

# Azure Key Vault Vault. It feels wrong, but it's technically right
Key Vault Vault

# luna containers in scan output
\bluna/.*\b

# this comes up in permissions and is valid
\broot root\b

# AWS resources
(ami|subnet|vpc|sg|fs)-[0-9a-fA-F]{17}

# http and https URLs
https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)

# registry key paths
HKEY_[\w\\]*

# Container digests
\bsha256:\w*

# mime types
\bapplication\/\S*

# mql certificate IDs
certificate:\w*

# ARN values
\barn:\S*

# mac user dir path
\/Users\/\S*

# AWS Token, ID access key, etc
aws_session_token\s+\=(\s+)?.+
aws_access_key_id\s+\=(\s+)?.+
aws_secret_access_key\s+\=(\s+)?.+

# PGP
\b(?:[0-9A-F]{4} ){9}[0-9A-F]{4}\b
# GPG keys
\b(?:[0-9A-F]{4} ){5}(?: [0-9A-F]{4}){5}\b

# uuid
\b[0-9a-fA-F]{8}-(?:[0-9a-fA-F]{4}-){3}[0-9a-fA-F]{12}\b

# curl arguments
\b(?:\\n|)curl(?:\s+-[a-zA-Z]{1,2}\b)*(?:\s+-[a-zA-Z]{3,})(?:\s+-[a-zA-Z]+)*

# set arguments
\bset(?:\s+-[abefimouxE]{1,2})*\s+-[abefimouxE]{3,}(?:\s+-[abefimouxE]+)*

# tar arguments
\b(?:\\n|)g?tar(?:\.exe|)(?:(?:\s+--[-a-zA-Z]+|\s+-[a-zA-Z]+|\s[ABGJMOPRSUWZacdfh-pr-xz]+\b)(?:=[^ ]*|))+

# file permissions
['"`\s][-bcdLlpsw](?:[-r][-w][-Ssx]){2}[-r][-w][-SsTtx]\+?['"`\s]

# score score is valid in MQL docs
score score

# macOS temp folders
/var/folders/\w\w/[+\w]+/(?:T|-Caches-)/

# ssh
(?:ssh-\S+|-nistp256) [-a-zA-Z=;:\/0-9+]{12,}

# kubernetes object suffix
-[0-9a-f]{10}-\w{5}\s

# sed regular expressions
sed 's/(?:[^/]*?[a-zA-Z]{3,}[^/]*?/){2}

# UNIX device paths
\/dev\/\w*

# AWS RDS instance types
db.\w{2}.\w*

# uuid
[<({"'>][0-9a-fA-F]{8}-(?:[0-9a-fA-F]{4}-){3}[0-9a-fA-F]{12}[<'"})>]

# rsa private keys
MII[BCEJ]\w*

# ID data MQL policies / query packs
- uid: \S*
secret_id: \S*
secret: \S*
publicKeyData: \S*
fingerprint: "\S*
p=MII\S*
BackupPlanVersion: "\S*
id: "\S*
pkix.extension id = \S*
id="\S*

# OCI config
user=\S*
fingerprint=\S*
tenancy=\S*

# Mondoo registration token https://rubular.com/r/3UzGvhUamOAa0U
MONDOO_REGISTRATION_TOKEN='\S*'

# IPv6 addresses
\b([a-f0-9:]+:+)+[a-f0-9]+\b
