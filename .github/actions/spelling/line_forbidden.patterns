# Detect common combinations of valid words that are in fact invalid.
# Useful for brand capitalizations

#
# Catch placeholder text
#

\b[Ll]orem [Ii]psum\b

#
# Overly formal style
#

# s.b. Whether
\bIndicates whether\b
\bIndicates if\b
\bIndicates\b
\bWhether or not\b
\bDenotes if\b

#
# Terms to avoid
#

# s.b. Allow list
\b[Ww]hitelist\b
\b[Ww]hitelisting\b
\b[Ww]hitelisted\b
\b[Ww]hite list\b
\b[Ww]hite listing\b
\b[Ww]hite listed\b

# s.b. Block list
\b[Bb]lacklist\b
\b[Bb]lacklisting\b
\b[Bb]lacklisted\b
\b[Bb]lack list\b
\b[Bb]lack listing\b
\b[Bb]lack listed\b

#
# Our Terms
#

# s.b. Mondoo Platform
\bthe Mondoo Platform\b
\bMondoo platform\b

# s.b. Compliance Hub
\b[Cc]ompliance hub\b

#
# Compliance Terms
#

# s.b. SOC 2
\bSOC2\b

# s.b. NIS2
\bNIS 2\b

# s.b. ISO 270001
\bISO270001\b

#
# Industry Terms
#

# s.b. NetFlow
\bNetflow\b

# s.b. Side scanning
\b[Ss]idescanning\b

# s.b. DevOps
\bDev Ops\b
\bDevops\b

# s.b. SaaS
# \b[Ss]aas\b # false positives in query packs

# s.b. Docker Hub
\bDocker[Hh]ub\b
\bdocker hub\b

# s.b. REST API
\b[Rr]est API\b
\brest api\b

# s.b. DevSecOps
\bDevsec[Oo]ps\b

# s.b. on-premises
\bon-premise\b

# s.b. email
\be-mail\b

# s.b. APIs
\bapis\b

# s.b. Base64
\bBase 64\b

# s.b. CRC32
\bCRC 32\b

# s.b. IaC
\bIAC\b

#
# Product Names
#

# s.b. Copilot
\bCoPilot\b

# s.b. OpenAI
\bOpen AI\b

# s.b. auditd
\bAuditd\b

# s.b. WatchGuard
\bWatchguard\b

# s.b. Airtable
\bAirTable\b

# s.b. Tailscale
\bTailScale\b

# s.b. TeamCity
\bTeamcity\b

# s.b. Bitbucket
\bBitBucket\b

# s.b. Logstash
\bLogStash\b

# s.b. CyberArk
\bCyberark\b

# s.b. Chef Infra Server
\bChef Server\b

# s.b. Chef Infra Client
\bChef Client\b

# s.b. containerd
\bContainerd\b

# s.b. systemd
\bSystemd\b

# s.b. OpenLDAP
\bOpen LDAP\b

# s.b. WebSphere
\bWebsphere\b

# s.b. JBoss
\bJboss\b

# s.b. WildFly
\bWildfly\b

# s.b. DigiCert
\bDigicert\b

# s.b. Cloudflare
\bCloudFlare\b

# s.b. Memcached
\bMemCached\b

# s.b. Jira
\bJIRA\b

# s.b. MariaDB
\bMaria DB\b
\bmariaDB\b
# \bmariaDb\b causes failures in MQL queries

# s.b. PostgreSQL
\bPostgreSql\b

# s.b. Firefox
\bFireFox\b

# s.b. CentOS
\bCentos\b
\bCent OS\b
\bcentOS\b

# s.b. macOS
\bOS X\b
\bMacOS\b
\bMac OS\b

# s.b. Okta
\bOcta\b

# s.b. Elasticsearch
\bElasticSearch\b

# s.b. DocuSign
\bDocu Sign\b

# s.b. DocuSign
\bDocu Sign\b

# s.b. DocuSign
\bDocu Sign\b
\bDocusign\b

# s.b. MongoDB
\bMongo DB\b
\bMongoDb\b

# s.b. MySQL
\bMysql\b
\bMySql\b

# s.b. OpenStack
\bOpen Stack\b
\bOpenstack\b

# s.b. Red Hat
\bRedHat\b
\bRedhat\b

# s.b. EuroLinux
\bEurolinux\b
\bEuro Linux\b

# s.b. AlmaLinux
\bAlma Linux\b

# s.b. CloudLinux
\bCloud Linux\b
\bCloudlinux\b

# s.b. openSUSE
\bOpenSUSE\b

# s.b. openSUSE
\bopenSuse\b

# s.b. CircleCI
\bCircleCi\b
\bCircle CI\b

# s.b. AppArmor
\bApparmor\b
\bApp Armor\b

# s.b. SELinux
\bSeLinux\b
\bSelinux\b

# s.b. InSpec
\b[Ii]nspec\b

# s.b. GitHub
\bGithub\b

# s.b. GitLab
\bGitlab\b

# s.b. JavaScript
\bJavascript\b

# s.b. OpenSSL
\bOpenssl\b
\bopenSSL\b

# s.b. CloudBees
\b[Cc]loudbees\b

# s.b. System76
\bSystem 76\b

# s.b. VirtualBox
\b[Vv]irtualbox\b
\bVirtual Box\b

# s.b. SentinelOne
\bSentinal[Oo]ne\b
\bSentin[ae]lone\b
\bSentin[ae]l One\b

# s.b. CrowdStrike
\bCrowd Strike\b
\b[Cc]rowdstrike\b

# s.b. Zendesk
\bZenDesk\b

# s.b. ServiceNow
\bService Now\b
\bServicenow\b

# s.b. name server
\bnameserver\b

# s.b. Nmap
\b[Nn]Map\b

#
# Kubernetes Terms
#

# s.b. DaemonSet
\bDaemonset\b

# s.b. Dockershim
\bDockerShim\b
\bdockershim\b

# s.b. LimitRange
\bLimitrange\b

# s.b. Minikube
\bMiniKube\b

# s.b. ReplicaSet
\bReplicaset\b

# s.b. StatefulSet
\bStatefulset\b

#
# HashiCorp Products
#

# s.b. HashiCorp
\bHashicorp\b

# s.b. Terraform
\bTerraForm\b

# s.b. Vagrantfile
\bVagrant file\b
\bVagrantFile\b

#
# Microsoft Products
#

# s.b. Entra ID
\bEntraID\b

# s.b. WinRM
\bwinRM\b
\bWin RM\b

# s.b. Microsoft
\bMicroSoft\b

# s.b. PowerPoint
\bPower Point\b
\bPowerpoint\b

# s.b. OneNote
\bOne Note\b
\bOnenote\b

# s.b. Windows Server
\bWindows server\b

# s.b. Team Foundation Server
\bTeam foundation server\b
\bteam foundation server\b

# s.b. Active Directory
\bActive directory\b
\bactive directory\b

# s.b. Group Policy Object
\bGroup policy object\b
\bgroup policy object\b
\bGroup Policy object\b

# s.b. Power BI
\bPowerBI\b

# s.b. SharePoint
\bSharepoint\b
\bShare Point\b

# s.b. BitLocker
\bBitlocker\b
\bbitLocker\b

# s.b. VS Code
\bVSCode\b
\bVScode\b

# s.b. LinkedIn
\bLinked In\b
\bLinkedin\b

# s.b. Microsoft IIS
\bIIS Server\b

#
# VMware Products
#

# s.b. VMware
\bVmware\b
\bVMWare\b

# s.b. vCenter
\bVcenter\b
\bVCenter\b

# s.b. vSphere
\bVsphere\b
\bVSphere\b

# s.b. ESXi
\bEsxi\b

#
# AWS Products
#

# s.b. App2Container
\bApp2container\b

# s.b. AppFlow
\bAppflow\b

# s.b. AppSync
\bAppsync\b

# s.b. CloudEnsure
\bCloudensure\b

# s.b. CloudFormation
\bCloudformation\b
\bCloud Formation\b

# s.b. CloudFront
\bCloudfront\b

# s.b. CloudHSM
\bCloud[Hh]sm\b

# s.b. CloudSearch
\bCloudsearch\b

# s.b. CloudShell
# we can't check for Cloud Shell since that's what Azure calls it
\bCloudshell\b
\bcloudshell\b

# s.b. CloudTrail
\bCloudtrail\b

# s.b. CloudWatch
\bCloudwatch\b

# s.b. CodeArtifact
\bCodeartifact\b

# s.b. CodeBuild
\bCodebuild\b

# s.b. CodeCommit
\bCodecommit\b

# s.b. CodeDeploy
\bCodedeploy\b

# s.b. CodeGuru
\bCodeguru\b

# s.b. CodePipeline
\bCodepipeline\b

# s.b. CodeStar
\bCodestar\b

# s.b. AWS Config
\bAWS config\b

# s.b. Copilot
\bCoPilot\b

# s.b. DeepRacer
\bDeepracer\b

# s.b. DocumentDB
\bDocument DB\b
\bDocumentDb\b

# s.b. DynamoDB
\bDynamo DB\b
\bDynamoDb\b

# s.b. ElastiCache
\bElasticache\b

# s.b. EventBridge
\bEventbridge\b

# s.b. Fargate
\bFarGate\b
\bFar Gate\b

# s.b. FinSpace
\bFinSpace\b

# s.b. FSx
\bFSX\b

# s.b. GameLift
\bGamelift\b

# s.b. GuardDuty
\bGuardduty\b

# s.b. Honeycode
\bHoneyCode\b

# s.b. Lambda
\bLamba\b

# s.b. Lightsail
\bLightSail\b

# s.b. MXNet
\bMxnet\b
\bMXnet\b

# s.b. OpenSearch
\bOpensearch\b

# s.b. OpenShift
\bOpenshift\b

# s.b. PrivateLink
\bPrivatelink\b

# s.b. QuickSight
\bQuicksight\b

# s.b. Redshift
\bRedShift\b

# s.b. RoboMaker
\bRobomaker\b

# s.b. Route 53
\bRoute53\b

# s.b. SageMaker
\bSagemaker\b

# s.b. SiteWise
\bSitewise\b

# s.b. StackSets
\bStacksets\b

# s.b. WorkDocs
\bWorkdocs\b

# s.b. WorkMail
\bWorkmail\b

#
# Google Cloud Products
#

# s.b. AlloyDB
\bAlloy DB\b

# s.b. AppEngine
\bApp Engine\b

# s.b. BigLake
\bBig Lake\b
\bBiglake\b

# s.b. BigQuery
\bBig Query\b
\bBigquery\b

# s.b. Cloud Build
\bCloudBuild\b
\bCloud build\b

# s.b. Cloud CDN
\bCloudCDN\b

# s.b. Cloud Functions
\bCloud functions\b

# s.b. Cloud Run
\bCloudRun\b
\bCloud run\b

# s.b. Cloud SQL
\bCloudSQL\b

# s.b. Compute Engine
\bComputeEngine\b
\bCompute engine\b

# s.b. Dataplex
\bDataPlex\b

# s.b. Datastream
\bDataStream\b
\bData Stream\b

# s.b. Dialogflow
\bDialogFlow\b

# s.b. Firestore
\bFireStore\b

# s.b. gVNIC
\bGVNIC\b

# s.b. Knative
\bKNative\b

# s.b. Memorystore
\bMemoryStore\b
\bMemory Store\b
\bMem[Ss]tore\b

# s.b. Pub/Sub
\bPubSub\b

# s.b. TensorFlow
\bTensor Flow\b

# s.b. Vertex AI
\bVertexAI\b

# s.b. VMware Engine
\bVMware engine\b
\bVMWare Engine\b

# s.b. Bigtable
\bBigTable\b
\bBig Table\b

# s.b. Datastore
\bDataStore\b

#
# Azure Products
#

# s.b. Azure Pipelines
\bAzure DevOps Pipelines\b

# s.b. Key Vault
\bKey vault\b
\bKeyVault\b

# s.b. Ampere
\bampere\b

# s.b. Azure DevOps Server
\bAzure DevOps server\b

# s.b. Synapse Analytics
\bSynapse analytics\b
\bsynapse analytics\b

# s.b. Cognitive Services
\bCognitive services\b
\bcognitive services\b

# s.b. Event Hubs
\bEvent hubs\b
\bevent hubs\b

# s.b. CloudOps
\bCloud Ops\b
\bCloud ops\b
\bcloud ops\b

# s.b. Batch Service
\bBatch service\b
\bbatch service\b

# s.b. Service Fabric Cluster
\bservice fabric cluster\b

# s.b. Azure Kubernetes Service
\bAzure Kubernetes service\b

# s.b. Cosmos DB
\bCosmosDB\b
\bCosmoDB\b
\bCosmo DB\b

# s.b. SignalR Service
\bSignalR service\b
\bSignal R Service\b

# s.b. App Service Certificate
\bapp service certificate\b

# s.b. Privileged Identity Management
\bprivileged identity management\b

# s.b. BizTalk Service
\bBizTalk service\b
\bBiztalk service\b
\bBiz Talk service\b
\bBiz Talk Service\b

# s.b. Data Box
\bdata box\b

# s.b. Database Migration Service
\bdatabase migration service\b

# s.b. Internet Analyzer
\bInternet analyzer\b
\binternet analyzer\b

# s.b. Web Application Firewall
\bWeb application firewall\b
\bweb Application Firewall\b

# s.b. SQL Vulnerability Assessment
\bSQL vulnerability assessment\b

# s.b. StorSimple
\bStor Simple\b

#
# Common Typos
#

# s.b. another
\ban[- ]other\b

# s.b. greater than
\bgreater then\b

# s.b. less than
\bless then\b

# s.b. otherwise
\bother[- ]wise\b

# s.b. nonexistent
\bnon existing\b
\b[Nn]o[nt][- ]existent\b

# s.b. preexisting
[Pp]re-existing

# s.b. preempt
[Pp]re-empt\b

# s.b. preemptively
[Pp]re-emptively

# s.b. reentrancy
[Rr]e-entrancy

# s.b. reentrant
[Rr]e-entrant

# s.b. policies
[Pp]olices

# s.b. ID
# \bId\b # disabled in this repo due to false positives

# s.b. CSV
\bCVS\b

# Reject duplicate words
\s([A-Z]{3,}|[A-Z][a-z]{2,}|[a-z]{3,})\s\g{-1}\s

# s.b. it's or its
\bits['’]

# s.b. understand
\bunder stand\b

# find spaces before a comma
( )+,
