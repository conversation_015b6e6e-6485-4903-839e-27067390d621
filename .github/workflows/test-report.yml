
name: 'Test Report'
on:
  workflow_run:
    workflows: ['Code Test'] # runs after tests
    types:
      - completed
jobs:
  report:
    permissions:
      actions: read        # Required to read the artifact
      contents: read       # Required to read the source
      checks: write        # Required to write the results
      pull-requests: write # Required to write comments
    runs-on: ubuntu-latest
    steps:
    - name: Download and Extract Artifacts
      uses: dawidd6/action-download-artifact@v11
      with:
        run_id: ${{ github.event.workflow_run.id }}
        path: artifacts

    - name: Publish Test Results
      uses: EnricoMi/publish-unit-test-result-action@v2
      with:
        commit: ${{ github.event.workflow_run.head_sha }}
        event_file: artifacts/Event File/event.json
        event_name: ${{ github.event.workflow_run.event }}
        files: "artifacts/**/*.xml"
