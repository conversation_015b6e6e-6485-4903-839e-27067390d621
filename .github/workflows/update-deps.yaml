name: Update cnquery and provider dependencies

on:
  schedule:
    - cron: "11 8 * * 1" # every monday
  workflow_dispatch:

jobs:
  update-deps:
    runs-on: ubuntu-latest
    permissions:
      contents: write
      pull-requests: write
    steps:
# https://github.com/peter-evans/create-pull-request/issues/48
# https://github.com/peter-evans/create-pull-request/blob/main/docs/concepts-guidelines.md#push-using-ssh-deploy-keys
# tl;dr:
# The GITHUB_TOKEN is limited when creating PRs from a workflow
# because of that we use a ssh key for which the limitations do not apply
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ssh-key: ${{ secrets.CNQUERY_DEPLOY_KEY_PRIV }}

      - name: Import environment variables from file
        run: cat ".github/env" >> $GITHUB_ENV

      - name: Install Go
        uses: actions/setup-go@v5
        with:
          go-version: ">=${{ env.golang-version }}"
          cache: false

      - name: Update deps
        id: update-deps
        run: |
          shopt -s expand_aliases
          alias version="go run providers-sdk/v1/util/version/version.go"
          version mod-update . --latest
          version mod-update providers/*/ --latest
          version mod-tidy providers/*/
          version mod-tidy .

      - name: Prepare title and branch name
        id: branch
        run: |
          BRANCH_NAME="version/deps_update_$(date +%Y%m%d_%H%M)"
          COMMIT_MSG="🧹 Update deps for cnquery and providers $(date +%Y%m%d)"
          echo "COMMIT_TITLE=${COMMIT_MSG}" >> $GITHUB_OUTPUT
          echo "BRANCH_NAME=${BRANCH_NAME}" >> $GITHUB_OUTPUT

# We have to use this extensions, because `gh pr create` does not support the ssh key case
      - name: Create pull request
        id: cpr
        uses: peter-evans/create-pull-request@v7
        with:
          base: main
          labels: dependencies,go
          committer: "Mondoo Tools <<EMAIL>>"
          commit-message: ${{ steps.branch.outputs.COMMIT_TITLE }}
          author: "Mondoo Tools <<EMAIL>>"
          title: ${{ steps.branch.outputs.COMMIT_TITLE }}
          branch: ${{ steps.branch.outputs.BRANCH_NAME }}
          body-path: .github/pr-body.md

      - name: PR infos
        if: ${{ steps.cpr.outputs.pull-request-number }}
        run: |
          echo "Pull Request Number - ${{ steps.cpr.outputs.pull-request-number }}"
          echo "Pull Request URL - ${{ steps.cpr.outputs.pull-request-url }}"
